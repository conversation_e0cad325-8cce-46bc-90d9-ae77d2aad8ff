import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  // Build configuration
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
        gallery: resolve(__dirname, 'gallery.html'),
      },
      output: {
        manualChunks: {
          vendor: ['intersection-observer'],
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name!.split('.')
          const ext = info[info.length - 1]
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name!)) {
            return `assets/images/[name]-[hash].${ext}`
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name!)) {
            return `assets/fonts/[name]-[hash].${ext}`
          }
          return `assets/${ext}/[name]-[hash].${ext}`
        },
      },
    },
    // Code splitting for better caching
    chunkSizeWarningLimit: 1000,
  },

  // CSS configuration
  css: {
    preprocessorOptions: {
      scss: {
        // Use modern @use instead of @import in additionalData
        // Variables and mixins are imported in main.scss
      },
    },
    devSourcemap: true,
  },

  // Server configuration for development
  server: {
    port: 3000,
    open: true,
    cors: true,
    host: true,
  },

  // Preview configuration for production testing
  preview: {
    port: 3001,
    open: true,
    cors: true,
  },

  // Plugin configuration
  plugins: [
    // Add plugins as needed
  ],

  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types'),
    },
    extensions: ['.ts', '.js', '.scss', '.css'],
  },

  // Optimization
  optimizeDeps: {
    include: ['intersection-observer'],
  },

  // Define global constants
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version),
  },

  // Environment variables
  envPrefix: 'COBRA_',

  // Base URL for deployment
  base: process.env.NODE_ENV === 'production' ? '/cobra-ryu/' : '/',
}) 