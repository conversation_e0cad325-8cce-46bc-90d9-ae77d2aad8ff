/**
 * Copyright 2023 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';
import { HTTPResponse } from '../../api/HTTPResponse.js';
import { WaitForOptions } from '../../api/Page.js';
import { EventEmitter } from '../EventEmitter.js';
import { TimeoutSettings } from '../TimeoutSettings.js';
import { EvaluateFunc, HandleFor } from '../types.js';
import { Connection } from './Connection.js';
import { ElementHandle } from './ElementHandle.js';
import { J<PERSON><PERSON>and<PERSON> } from './JSHandle.js';
/**
 * @internal
 */
export declare class Context extends EventEmitter {
    #private;
    _contextId: string;
    _timeoutSettings: TimeoutSettings;
    constructor(connection: Connection, result: Bidi.BrowsingContext.Info);
    get connection(): Connection;
    get id(): string;
    evaluateHandle<Params extends unknown[], Func extends EvaluateFunc<Params> = EvaluateFunc<Params>>(pageFunction: Func | string, ...args: Params): Promise<HandleFor<Awaited<ReturnType<Func>>>>;
    evaluate<Params extends unknown[], Func extends EvaluateFunc<Params> = EvaluateFunc<Params>>(pageFunction: Func | string, ...args: Params): Promise<Awaited<ReturnType<Func>>>;
    goto(url: string, options?: WaitForOptions & {
        referer?: string | undefined;
        referrerPolicy?: string | undefined;
    }): Promise<HTTPResponse | null>;
    url(): string;
    setContent(html: string, options?: WaitForOptions | undefined): Promise<void>;
}
/**
 * @internal
 */
export declare function getBidiHandle(context: Context, result: Bidi.CommonDataTypes.RemoteValue): JSHandle | ElementHandle<Node>;
//# sourceMappingURL=Context.d.ts.map