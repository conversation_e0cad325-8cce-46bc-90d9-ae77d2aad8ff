{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/injected/util.ts"], "names": [], "mappings": "AAAA,MAAM,wBAAwB,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,CAC7B,IAAiB,EACjB,OAAiB,EACD,EAAE;IAClB,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,OAAO,KAAK,KAAK,CAAC;KAC1B;IACD,IAAI,OAAO,KAAK,SAAS,EAAE;QACzB,OAAO,IAAI,CAAC;KACb;IACD,MAAM,OAAO,GAAG,CACd,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAClD,CAAC;IAEb,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,SAAS,GACb,KAAK;QACL,CAAC,wBAAwB,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC;QACpD,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAC/B,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAC9C,CAAC,CAAC;AAEF,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,MAAM,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;IAC7C,OAAO,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/C,CAAC;AAED,MAAM,aAAa,GAAG,CAAC,IAAU,EAA2C,EAAE;IAC5E,OAAO,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,UAAU,YAAY,UAAU,CAAC;AACvE,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,SAAS,CAAC,CAAC,MAAM,CAAC,IAAU;IAChC,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;QACvB,MAAM,IAAI,CAAC,UAAU,CAAC;KACvB;SAAM;QACL,MAAM,IAAI,CAAC;KACZ;AACH,CAAC;AAED;;GAEG;AACH,MAAM,SAAS,CAAC,CAAC,SAAS,CAAC,IAAU;IACnC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;IACjC,MAAM,IAAI,CAAC;IACX,MAAM,OAAO,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;IAC3E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,IAAoB,CAAC;QACzB,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAoB,CAAC,EAAE;YACnD,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBACpB,SAAS;aACV;YACD,MAAM,IAAI,CAAC,UAAU,CAAC;YACtB,OAAO,CAAC,IAAI,CACV,QAAQ,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CACpE,CAAC;SACH;KACF;AACH,CAAC"}