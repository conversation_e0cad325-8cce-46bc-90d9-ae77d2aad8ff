export class ReportRenderer {
    /**
     * @param {DOM} dom
     */
    constructor(dom: DOM);
    /** @type {DOM} */
    _dom: DOM;
    /** @type {LH.Renderer.Options} */
    _opts: LH.Renderer.Options;
    /**
     * @param {LH.Result} lhr
     * @param {HTMLElement?} rootEl Report root element containing the report
     * @param {LH.Renderer.Options=} opts
     * @return {!Element}
     */
    renderReport(lhr: LH.Result, rootEl: HTMLElement | null, opts?: LH.Renderer.Options | undefined): Element;
    /**
     * @param {LH.ReportResult} report
     * @return {DocumentFragment}
     */
    _renderReportTopbar(report: LH.ReportResult): DocumentFragment;
    /**
     * @return {DocumentFragment}
     */
    _renderReportHeader(): DocumentFragment;
    /**
     * @param {LH.ReportResult} report
     * @return {DocumentFragment}
     */
    _renderReportFooter(report: LH.ReportResult): DocumentFragment;
    /**
     * @param {LH.ReportResult} report
     * @param {DocumentFragment} footer
     */
    _renderMetaBlock(report: LH.ReportResult, footer: DocumentFragment): void;
    /**
     * Returns a div with a list of top-level warnings, or an empty div if no warnings.
     * @param {LH.ReportResult} report
     * @return {Node}
     */
    _renderReportWarnings(report: LH.ReportResult): Node;
    /**
     * @param {LH.ReportResult} report
     * @param {CategoryRenderer} categoryRenderer
     * @param {Record<string, CategoryRenderer>} specificCategoryRenderers
     * @return {!DocumentFragment[]}
     */
    _renderScoreGauges(report: LH.ReportResult, categoryRenderer: CategoryRenderer, specificCategoryRenderers: Record<string, CategoryRenderer>): DocumentFragment[];
    /**
     * @param {LH.ReportResult} report
     * @return {!DocumentFragment}
     */
    _renderReport(report: LH.ReportResult): DocumentFragment;
}
export type DOM = import('./dom.js').DOM;
import { CategoryRenderer } from "./category-renderer.js";
//# sourceMappingURL=report-renderer.d.ts.map