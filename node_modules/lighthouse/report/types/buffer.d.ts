/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

/**
 * A minimal type for Node's `Buffer` to avoid importing all Node types for
 * atob/btoa fallback for testing `text-encoding.js`.
 */

declare global {
  class Buffer {
    static from(str: string, encoding?: string): Buffer;
    toString(encoding?: string): string;
  }
}

export {}
