{"core/audits/accessibility/accesskeys.js | description": {"message": "A hozzáférési kulcsok segítségével a felhasználók gyorsan fókuszálhatnak az oldal adott részére. A megfelelő navigáció érdekében az összes hozzáférési kulcsnak egyedinek kell lennie. [További információ a hozzáférési kulcsokról](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "A(z) `[accesskey]` értékek nem egyediek"}, "core/audits/accessibility/accesskeys.js | title": {"message": "A következő értékek egyediek: `[accesskey]`"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Minden egyes ARIA `role` szerepkö<PERSON> `aria-*` attribútumok konkrét részhalmazát támogatja. A hibás párosításuk érvényteleníti az `aria-*` attribútumokat. [Tov<PERSON><PERSON><PERSON> információ az <PERSON>-attribútumok és szerepkörük megfeleltetéséről](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Bizonyos `[aria-*]` attribútumok nem felelnek meg szerepkörüknek"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "A(z) `[aria-*]` attribútumok megfelelnek szerepüknek"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Ha valamelyik elem nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami haszná<PERSON>lan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a parancselemek hozzáférhetőbbé tételéről](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "A(z) `button`, `link` és `menuitem` elemek nem rendelkeznek akadálymentes névvel."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "A(z) `button`, `link` és `menuitem` elemek akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "A segítő technológiák (például a képernyőolvasók) nem működnek konzisztensen, ha az `aria-hidden=\"true\"` be van állítva a dokumentum `<body>` elemében. [To<PERSON><PERSON><PERSON><PERSON> inform<PERSON>, hogy milyen hatással van az `aria-hidden` a dokumentum törzsére](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "A(z) `[aria-hidden=\"true\"]` megtal<PERSON><PERSON>ható a dokumentum `<body>` elemében"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "A(z) `[aria-hidden=\"true\"]` nem tal<PERSON><PERSON>ható meg a dokumentum `<body>` elem<PERSON>ben"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Egy `[aria-hidden=\"true\"]` elemen bel<PERSON>li fó<PERSON><PERSON><PERSON><PERSON><PERSON>ó leszármazottak megakadályozzák azt, hogy a segítő technológiák (például a képernyőolvasók) felhasználói hozzáférhessenek az érintett interaktív elemekhez. [Tov<PERSON><PERSON>i információ <PERSON>, hogy az `aria-hidden` hogyan befolyásolja a fókuszálható elemeket](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "A(z) `[aria-hidden=\"true\"]` elemek fókuszálható utódelemeket tartalmaznak"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "A(z) `[aria-hidden=\"true\"]` elemek nem tartalmaznak fókuszálható utódelemeket"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Ha valamelyik beviteli mező nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhatatlan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a beviteli mező címkéiről](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Egyes ARIA beviteli mezők nem rendelkeznek akadálymentes névvel"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Az ARIA beviteli mezők akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Ha egy számláló elem nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhatatlan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a(z) `meter` elemek elnevezéséről](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "A(z) `meter` ARIA-elemek nem rendelkeznek akadálymentes névvel."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "A(z) `meter` ARIA-elemek akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Ha egy `progressbar` elem nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhat<PERSON>lan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a `progressbar` elemek címkézéséről](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "A(z) `progressbar` ARIA-elemek nem rendelkeznek akadálymentes névvel."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "A(z) `progressbar` ARIA-elemek akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Bizonyos ARIA-szerepkörök kötelező attribútumokkal rendelkeznek, amelyek az elem állapotát írják le a képernyőolvasók számára. [További információ a szerepkörökről és a kötelező attribútumokról](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "A(z) `[role]` elemek nem rendelkeznek minden szükséges `[aria-*]` attribútummal"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "A(z) `[role]` attribútumok minden szükséges `[aria-*]` attribútummal rendelkeznek"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Bizonyos fölérendelt ARIA-szerepköröknek meghatározott alárendelt szerepköröket kell tartalma<PERSON>uk, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [További információ a szerepkörökről és a kötelező alárendelt elemekről](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` attrib<PERSON>tummal rendelkez<PERSON> elemekből, me<PERSON><PERSON> gyermekelemeiben kell lennie adott `[role]` attri<PERSON><PERSON><PERSON>nak, hiányzik ezeknek a kötelező gyermekelemeknek mindegyike vagy némelyike."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` attribútummal rendelkező elemekben, me<PERSON><PERSON> gyermekelemeiben kell lennie adott `[role]` attri<PERSON><PERSON><PERSON>nak, megvan a kötelező gyermekelemek mindegyike."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Bizonyos alárendelt ARIA-szerepköröket meghatározott fölérendelt szerepköröknek kell tartalma<PERSON>uk, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [További információ az ARIA-szerepkörökről és a szükséges fölérendelt elemről](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "A(z) `[role]` elemek nem a megfelelő szülőelemben vannak"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "A(z) `[role]` elemek a megfelelő szülőelemben vannak"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Az ARIA-szerepköröknek érvényes értékekkel kell rendelkezniük, hogy megfelelően teljesíthessék a kívánt kisegítő funkciójukat. [További információ az érvényes ARIA-szerepkörökről](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "A(z) `[role]` értékek nem érvényesek"}, "core/audits/accessibility/aria-roles.js | title": {"message": "A(z) `[role]` értékek érvényesek"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Ha valamelyik kapcsolómező nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhatatlan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a mezők közötti váltásról](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Egyes <PERSON>-kapcsolómezők nem rendelkeznek akadálymentes névvel"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Az ARIA-kapcsolómezők akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Ha valamelyik elemleírási elem nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhatatlan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a(z) `tooltip` elemek elnevezéséről](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "A(z) `tooltip` ARIA-elemek nem rendelkeznek akadálymentes névvel."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "A(z) `tooltip` ARIA-elemek akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Ha egy `treeitem` elem nem rendelkezik kisegítő névvel, a képernyőolvasók általános néven olvassák fel, ami használhatatlan a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a `treeitem`-elemek címkézéséről](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "A(z) `treeitem` ARIA-elemek nem rendelkeznek akadálymentes névvel."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "A(z) `treeitem` ARIA-elemek akadálymentes névvel rendelkeznek"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "A segítő technológiák (például a képernyőolvasók) nem tudják értelmezni az érvénytelen értékkel rendelkez<PERSON> ARIA-attribútumokat. [További információ az ARIA-attribútumok érvényes értékeiről](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "A(z) `[aria-*]` attribútumoknak nincs érvényes értékük"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "A(z) `[aria-*]` attribútumok érvényes értékkel rendelkeznek"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "A segítő technológiák (például a képernyőolvasók) nem tudják értelmezni az érvénytelen névvel rendelkező ARIA-attribútumokat. [További információ az érvényes ARIA-attribútumokról](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "A(z) `[aria-*]` attribútumok nem érvényesek vagy elgépelést tartalmaznak"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "A(z) `[aria-*]` attribútumok érvényesek és nincsenek elgépelve"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "<PERSON><PERSON><PERSON> el<PERSON>"}, "core/audits/accessibility/button-name.js | description": {"message": "Ha valamelyik gomb nem rendelkezik kisegítő névvel, a képernyőolvasók „gomb” néven olvassák fel, ami nem igazán hasznos a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a gombok hozzáférhetőbbé tételéről](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Bizonyos gombok nem rendelkeznek hozzáférhető névvel"}, "core/audits/accessibility/button-name.js | title": {"message": "A gombok rendelkeznek kisegítő névvel"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON>z ismétlődő tartalmakat megkerülő módszerek megvalósításával a billentyűzetet használók hatékonyabban navigálhatnak az oldalon. [További információ a letiltások megkerüléséről](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Az oldal nem tartalma<PERSON> cí<PERSON>, átugró linket vagy igazodásipont-régiót"}, "core/audits/accessibility/bypass.js | title": {"message": "<PERSON>z oldal címsort, átugró linket vagy igazodásipont-régiót tartalmaz"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Az alacsony kontrasztú szöveg sokak számára nehezen vagy egyáltalán nem olvasható. [További információ a megfelelő színkontraszt megadásáról](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> a háttér- és előtérszínek közötti kontrasztarány."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Megfelelő a háttér- és előtérszínek közötti kontrasztarány"}, "core/audits/accessibility/definition-list.js | description": {"message": "Ha nem megfelelő a definíciós listák jelölése, a képernyőolvasók zavaros vagy pontatlan szöveget generálhatnak. [További információ a definíciós listák helyes strukturálásáról](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "A `<dl>` elemek nem csak megfelelően rendezett `<dt>` és `<dd>` csoportokat, illetve `<script>`, `<template>` vagy `<div>` elemeket tartalmaznak."}, "core/audits/accessibility/definition-list.js | title": {"message": "A `<dl>` elemek csak megfelelően rendezett `<dt>` és `<dd>` csoportokat, illetve `<script>`, `<template>` vagy `<div>` elemeket tartalmaznak."}, "core/audits/accessibility/dlitem.js | description": {"message": "A definíciós listák elemeit (`<dt>` és `<dd>`) `<dl>` s<PERSON><PERSON><PERSON><PERSON><PERSON>mbe kell <PERSON>, hogy a képernyőolvasók megfelelően felolvashassák őket. [További információ a definíciós listák helyes strukturálásáról](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "A definíciós listák elemei ninc<PERSON>ek `<dl>` elemekben"}, "core/audits/accessibility/dlitem.js | title": {"message": "A definíciós listák elemei `<dl>` elem<PERSON><PERSON> vannak"}, "core/audits/accessibility/document-title.js | description": {"message": "A cím áttekintést ad az oldalról a képernyőolvasót használóknak, a keresőmotorok felhasználói pedig nagymértékben hagyatkoznak rá annak meghatározásához, hogy az oldal releváns-e az adott kereséshez. [További információ a dokumentumcímekről](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "A dokumentum nem rendelkezik `<title>` elemmel"}, "core/audits/accessibility/document-title.js | title": {"message": "A dokumentum tartalmaz `<title>` elemet"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Egyéni `id` meg<PERSON><PERSON><PERSON> van szükség az összes fókuszálható elemnél, hogy biztosan láthatók legyenek a segítő technológiák számára. [További információ az ismétl<PERSON>dő `id` elemek kijavításáról](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>, f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elemek `[id]` attribútumai nem egyediek"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "<PERSON>z <PERSON>í<PERSON>, fókuszálható elemek `[id]` attribútumai egyediek"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Az ARIA-azonosító értékének egyedinek kell lennie, hogy a kisegítő technológiák ne hagyják figyelmen kívül a többi előfordulást. [További információ az ismétlődő ARIA-azonosítók kijavításáról](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Egy<PERSON>-azonosítók nem egyediek"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Az ARIA-azonosítók egyediek"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "A több címkével rendelkező űrlapmezőket félreérthetően olvashatják fel a segítő technológiák (például a képernyőolvasók), am<PERSON><PERSON> az <PERSON>, az uto<PERSON><PERSON> vagy pedig az összes címkét használják. [További információ az űrlapcímkék használatáról](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Az űrlapmezők több címkével rendelkeznek"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Nincsenek több címkével rendelkező űrlapmezők"}, "core/audits/accessibility/frame-title.js | description": {"message": "A képernyőolvasót használó látogatók keretcímekre hagyatkozhatnak a keretek tartalmának leírásakor. [További információ a keretcímekről](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "A(z) `<frame>` és a(z) `<iframe>` elemeknek nincs cí<PERSON>ük"}, "core/audits/accessibility/frame-title.js | title": {"message": "Van címe a(z) `<frame>` és `<iframe>` elemeknek"}, "core/audits/accessibility/heading-order.js | description": {"message": "A megfelelően rendezett címsorok (amelyek nem ugranak át szinteket) megfelelően közvetítik az oldal szemantikai struktúráj<PERSON>t, így könnyebbé teszik a navigációt és a megértést a segítő technológiák használata esetén. [További információ a címsorok sorrendjéről](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "A fejlécelemek nem egymást követő csökkenő sorrendben jelennek meg"}, "core/audits/accessibility/heading-order.js | title": {"message": "A fejlécelemek egymást követve, csökkenő sorrendben jelennek meg"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Ha az oldal nem határoz meg `lang` attribútumot, a képernyőolvasók azt feltételezik majd, hogy az oldal nyelve megegyezik azzal az alapértelmezett nyelvvel, amelyet a felhasználó a képernyőolvasó beállításakor választott. Ha az oldal tényleges nyelve eltér az alapértelmezett nyelvtől, ak<PERSON> előfordulhat, hogy a képernyőolvasók helytelenül olvassák majd fel az oldal szövegét. [További információ a `lang` attribútumról](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "A(z) `<html>` elem nem rendelkezik `[lang]` attribútummal"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "A(z) `<html>` elem<PERSON>z tartozik `[lang]` attribútum"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "<PERSON> érvényes, [BCP 47 által definiált nyelvet](https://www.w3.org/International/questions/qa-choosing-language-tags#question) has<PERSON><PERSON><PERSON>, a képernyőolvasók könnyebben felolvassák a szövegeket. [Tov<PERSON><PERSON><PERSON> információ a `lang` attribútum használatáról](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "A(z) `<html>` elem `[lang]` attribútumának nincs érvényes értéke."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "A(z) `<html>` elem `[lang]` attribútumának értéke érvényes"}, "core/audits/accessibility/image-alt.js | description": {"message": "<PERSON><PERSON><PERSON>, beszédes alternatív szöveget használjon a tájékoztató elemekhez. A díszítőelemek figyelmen kívül hagyhatók üres alt attribútummal. [További információ az `alt` attribútumról](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "A képelemekhez nem tartozik `[alt]` attribútum"}, "core/audits/accessibility/image-alt.js | title": {"message": "A képelemekhez tartozik `[alt]` attribútum"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Ha az `<input>` t<PERSON><PERSON><PERSON> gombként használt képekhez alternatív szöveget is megad, segíthet a képernyőolvasót használó látogatóknak a gomb rendeltetésének megértésében. [Tov<PERSON><PERSON>i információ a bemeneti kép leíró szövegéről](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "A(z) `<input type=\"image\">` elemekhez nem tartozik `[alt]` szöveg"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "A(z) `<input type=\"image\">` elemek rendelkez<PERSON>k `[alt]` szöveggel"}, "core/audits/accessibility/label.js | description": {"message": "A címkék biztosítják, hogy a segítő technológiák (pl. a képernyőolvasók) megfelelően jelezzék az űrlapvezérlőket. [További információ az űrlapelemcímkékről](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Bizonyos formátumelemek nem rendelkeznek társított címk<PERSON>kel"}, "core/audits/accessibility/label.js | title": {"message": "A formátumelemekhez megfelelő címkék vannak társítva"}, "core/audits/accessibility/link-name.js | description": {"message": "A felismerhető, egyedi és fókuszálható linkszövegek (és linkként használt képek alternatív szövegei) jobb navigációs élményt biztosítanak a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a linkek hozzáférhetővé tételéről](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "A linkekhez nem tartozik felismerhető név"}, "core/audits/accessibility/link-name.js | title": {"message": "A linkekhez felismerhető név tartozik"}, "core/audits/accessibility/list.js | description": {"message": "A képernyőolvasók sajátos módon olvassák fel a listákat. A megfelelő listastruktúra biztosítása segíti a képernyőolvasók működését. [További információ a megfelelő listastruktúráról](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "A listák nem csak `<li>` elemeket és szkripttámogató elemeket (`<script>` és `<template>`) tartalmaznak."}, "core/audits/accessibility/list.js | title": {"message": "A listák csak `<li>` elemeket és szkripttámogató elemeket (`<script>`, `<template>`) tartalmaznak."}, "core/audits/accessibility/listitem.js | description": {"message": "A listaelemeket (`<li>`) fölérendelt `<ul>`, `<ol>` vagy `<menu>` elemekben kell elhelyezni, hogy a képernyőolvasók megfelelően olvassák fel őket. [További információ a megfelelő listastruktúráról](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "A listael<PERSON>ek (`<li>`) nem `<ul>`, `<ol>` vagy `<menu>` szülőelemekben szerepelnek."}, "core/audits/accessibility/listitem.js | title": {"message": "A listael<PERSON>ek (`<li>`) `<ul>`, `<ol>` vagy `<menu>` szülőelemekben vannak."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "A felhasználók nem számítanak az oldal automatikus frissítésére, ráadásul a frissítés visszahelyezi a fókuszt az oldal tetejére. Ez frusztrá<PERSON> le<PERSON>, valamint összezavarhatja a felhasználót. [Tov<PERSON>bbi információ a refresh metacímkéről](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "A dokumentum `<meta http-equiv=\"refresh\">` címkét használ"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "A dokumentum nem használja a(z) `<meta http-equiv=\"refresh\">` címkét"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "A nagyítás letiltása problémát jelent a gyengén látó felhasználók számára, akik a képernyőnagyításra hagyatkoznak ahhoz, hogy megfelelően láthassák a weboldal tartalmát. [További információ a viewport-metacímkéről](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "A(z) `[user-scalable=\"no\"]` szerepel a(z) `<meta name=\"viewport\">` elemben, vagy a(z) `[maximum-scale]` attribútum 5-n<PERSON>l kisebb."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Nem használja a(z) `[user-scalable=\"no\"]` attribútumot a(z) `<meta name=\"viewport\">` elemben, a(z) `[maximum-scale]` attribútum pedig nem kevesebb 5-nél."}, "core/audits/accessibility/object-alt.js | description": {"message": "A képernyőolvasók nem tudj<PERSON> lefordítani a nem szövegalapú tartalmakat. Ha alternatív szöveget helyez el az `<object>` elem<PERSON><PERSON>, a képernyőolvasók kommunikálhatják az elemek jelentését a felhasználóknak. [További információ az `object` elemekhez tartozó leíró szövegről](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "A(z) `<object>` elemekhez nem tartozik alternatív szöveg"}, "core/audits/accessibility/object-alt.js | title": {"message": "A(z) `<object>` elemek rendelkeznek alternatív szöveggel"}, "core/audits/accessibility/tabindex.js | description": {"message": "A 0-nál nagyobb érték explicit navigációs sorrendet jelent. Ez ugyan technikailag érvényes, azonban gyakran problémát jelent a segítő technológiákra hagyatkozó felhasználók számára. [További információ a `tabindex` attribútumról](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Bizonyos elemek `[tabindex]` attribútuma 0-nál nagyobb értékkel rendelkezik."}, "core/audits/accessibility/tabindex.js | title": {"message": "Egyetlen elem `[tabindex]` attribútumának sem 0-nál nagyobb az értéke"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "A képernyőolvasók olyan funkciókkal is re<PERSON><PERSON><PERSON><PERSON><PERSON>, amely<PERSON> meg<PERSON>önnyítik a táblázatokban való navigációt. Ha a `[headers]` attribútumot hasz<PERSON> `<td>` cell<PERSON> csak a saját táblázatuk más celláira hivatkoznak, az jobb felhasználói élményt nyújthat a képernyő felolvasása során. [További információ a `headers` attribútumról](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "<PERSON><PERSON> egyik `<table>` elem<PERSON> l<PERSON>, `[headers]` attribútumot használó cellák olyan `id` elemre hi<PERSON>, amely nem tal<PERSON><PERSON>ható meg ugyanabban a táblázatban."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "<PERSON><PERSON> egyik `<table>` elem<PERSON> lév<PERSON>, `[headers]` attribútumot használó cellák a saját táblázatuk celláira hivatkoznak."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "A képernyőolvasók olyan funkciókkal is re<PERSON><PERSON><PERSON>ne<PERSON>, amely<PERSON> megkönnyítik a táblázatokban való navigációt. Ha biztosítja, hogy a táblázatok fejlécei mindig hivatkozzanak bizonyos cellákra, megkönnyítheti az oldal használatát a képernyőolvasóra hagyatkozó felhasználók számára. [További információ a táblázatfejlécekről](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "A(z) `<th>` elemekhez és a(z) `[role=\"columnheader\"/\"rowheader\"]` tartalmú elemekhez nem tartoznak általuk leírt adatcellák."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "A(z) `<th>` elemek és a(z) `[role=\"columnheader\"/\"rowheader\"]` tartalmú elemek rendelkeznek a leírt adatcellákkal."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Ha az elemeken érvényes, [BCP 47 által definiált nyelvet](https://www.w3.org/International/questions/qa-choosing-language-tags#question) hat<PERSON><PERSON><PERSON> meg, azzal segíthet a képernyőolvasóknak a szöveg helyes kiejtésében. [Tová<PERSON>i információ a `lang` attribútum használatáról](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "A(z) `[lang]` attribútumokhoz nem tartozik érvényes érték"}, "core/audits/accessibility/valid-lang.js | title": {"message": "A(z) `[lang]` attribútumok érvényes értékkel rendelkeznek"}, "core/audits/accessibility/video-caption.js | description": {"message": "Ha a videóhoz felirat tartozik, a siket és hallássérült felhasználók egyszerűbben hozzájuthatnak a videóban található információkhoz. [További információ a videofeliratokról](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "A(z) `<video>` elemekhez nem tartozik `[kind=\"captions\"]` tartalmú `<track>` elem."}, "core/audits/accessibility/video-caption.js | title": {"message": "A(z) `<video>` elemek<PERSON>z `[kind=\"captions\"]` tartalmú `<track>` elem tartozik"}, "core/audits/autocomplete.js | columnCurrent": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Javasolt token"}, "core/audits/autocomplete.js | description": {"message": "Az `autocomplete` segítségével a felhasználók gyorsabban kitölthetik az űrlapokat. A felhasználók dolgának megkönnyítése érdekében fontolja meg az automatikus kiegészítés engedélyezését az `autocomplete` attribútum érvényes értékre állításával. [További információ a következőről az űrlapokon: `autocomplete`](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "A(z) `<input>` elemekhez nem tartozik megfelelő `autocomplete` attribútum"}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON><PERSON>zi ellenőrzés szükséges"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Tokenek sorrend<PERSON><PERSON><PERSON>"}, "core/audits/autocomplete.js | title": {"message": "A(z) `<input>` elemek megfelel<PERSON>en használj<PERSON> az `autocomplete` attribútumot."}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete` tokenek: a(z) „{token}” érvénytelen itt: {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Nézze át a(z) „{tokens}” tokenek sorrendjét itt: {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Végrehajtható"}, "core/audits/bf-cache.js | description": {"message": "Számos navigáció az előző oldalra való visszal<PERSON>p<PERSON>sel, vagy az onnan való visszalépéssel történik. Az előre-vissza gyorsítótárazás (bfcache) felgyorsíthatja ezeket a visszalépési navigációkat. [További információ a bfcache-ről](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)."}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 hibaok}other{# hibaok}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "A hiba oka"}, "core/audits/bf-cache.js | failureTitle": {"message": "Az oldal megakadályozta az előre-vissza gyorsítótárazás visszaállítását"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Nem v<PERSON>ó"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Böngészőtámogatás függőben"}, "core/audits/bf-cache.js | title": {"message": "Az oldal nem akadályozta meg az előre-vissza gyorsítótárazás visszaállítását"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Egyes <PERSON>-bővítmények kedvezőtlenül befolyásolták az oldal betöltési teljesítményét. Próbálkozzon az oldal inkognitó módban vagy bővítmények nélküli Chrome-profilból történő ellenőrzésével."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Szkriptértékelés"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Szkriptelemzés"}, "core/audits/bootup-time.js | columnTotal": {"message": "<PERSON><PERSON>s <PERSON>"}, "core/audits/bootup-time.js | description": {"message": "Érdemes csökkenteni a JavaScript elemzésére, összeállítására és végrehajtására fordított időt. E<PERSON><PERSON> seg<PERSON>, ha kisebb méretű JavaScript-forrásokat továbbít. [További információ a JavaScript végrehajtási idejének csökkentéséről](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Csökkentse a JavaScript végrehajtási idejét"}, "core/audits/bootup-time.js | title": {"message": "JavaScript végrehajtási ideje"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Távolítsa el a csomagjaiból a nagy méretű, többször előforduló JavaScript-modulokat, hogy kevesebb hálózati forgalmat okozzanak. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Távolítsa el a többször szereplő modulokat a JavaScript-csomagokból"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "A túl nagy GIF-fájlokkal nem lehet hatékonyan animált tartalmakat nyújtani. Az adatforgalom csökkentésének érdekében a GIF-ek helyett érdemes az animációkhoz MPEG4-/WebM-videókat használnia, a statikus képekhez pedig PNG-/WebP-képeket. [További információ a hatékony videóformátumokról](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)."}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Használjon videoformátumot az animált tartalmakhoz"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "A polifill és az átalakítás lehetővé teszi, hogy a régi böngészők is használhassák az újabb JavaScript-funkciókat. A modern böngészők esetében azonban sok polifill és átalakítás szükségtelen. Használjon modern szkripttelepítő módszert csomagolt JavaScript-kódjaihoz, amely észleli a module/nomodule utasításokat. Így kevesebb kódot küld a modern böngészőknek, mégis megtarthatja a régi böngészők támogatását. [További információ a modern JavaScript használatáról](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> régi <PERSON> megjelenítését a modern böngészőkben"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Az olyan képformátumok, mint a WebP és az AVIF gyakran jobb tömörítést biztosítanak, mint a PNG vagy a JPEG, ami gyorsabb letöltést és kisebb adatforgalmat jelent. [Tová<PERSON><PERSON> információ a modern képformátumokról](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Jelenítse meg a képeket következő generációs formátumok<PERSON>"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Vegye fontolóra a képernyőn kívüli és rejtett képek késleltetett, kritikus források utáni betöltését, hogy csökkentse az oldal interaktivitásig eltelt idejét. [További információ a képernyőn kívüli képek betöltésének késleltetéséről](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Késleltesse a képernyőn kívüli képek betöltését"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Források blokkolják az oldal első vizuális válaszát. Javasoljuk, hogy a legfontosabb JavaScript-/CSS-elemeket beágyazva továbbítsa, a nem fontos JavaScriptet/stílust pedig késleltesse [További információ a megjelenítést gátló erőforrások eltávolításáról](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Távolítsa el a megjelenítést gátló erőforrásokat"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "A nagy hálózati terhelés tényleges anyagi költséget jelenthet a felhasználóknak, és jellemzően jelentősen megnöveli a betöltési időt. [További információ a továbbított adatok méretének csökkentéséről](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "A teljes méret {totalBytes, number, bytes} KiB volt"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Kerülje a nagyon nagy hálózati hasznosadat-forgalmat"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "A nagyon nagy hálózati terhelés elkerülése"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "A CSS-fájlok kicsinyítése csökkentheti a hálózaton továbbított adatok méretét. [További információ a CSS kicsinyítéséről](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minimalizálja a CSS-fájlokat"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "A JavaScript-fájlok kicsinyítésével csökkenthető a továbbított adatok mérete és a szkriptek elemzésére fordított idő. [További információ arró<PERSON>, hogy miként kicsinyíthető a JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minimalizálja a JavaScript-kódot"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "A stíluslapok nem használt szabályainak eltávolításával és a hajtás feletti tartalomhoz nem használt CSS-ek késleltetésével csökkentheti a hálózati tevékenység által felhasznált bájtmennyiséget. [További információ a nem használt CSS csökkentéséről](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Nem használt CSS-kódok számának csökkentése"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "A nem használt JavaScript-kódok számának csökkentésével és a betöltési szkriptek késleltetésével csökkentheti a hálózati tevékenység által felhasznált bájtmennyiséget. [További információ a nem használt JavaScript-kódok számának csökkentéséről](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Nem használt JavaScript-kódok számának csökkentése"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Ha a gyorsítótárak <PERSON> ho<PERSON>zú, gyo<PERSON><PERSON><PERSON> válnak az oldal későbbi ismételt megnyitásai. [További információ a hatékony gyorsítótár-házirendekről](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 erőforrás található}other{# erőforrás található}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Jelenítse meg a statikus eszközöket hatékony gyorsítótár-házirend <PERSON>"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Használjon hatékony gyorsítótár-házirendet a statikus eszközöknél"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Az optimalizált képek gyorsabban bet<PERSON>dnek, és kevesebb mobiladat-forgalmat generálnak. [További információ a képek hatékony kódolásáról](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Kódo<PERSON>ja <PERSON> a képeket"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "<PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "A képek nagyobbak voltak a megjelenített méretüknél"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "A képek megfelelők voltak a megjelenített méretükhöz"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Használjon olyan ké<PERSON>, amelyek me<PERSON>fe<PERSON> méretükkel elősegítik a mobiladat-forgalom csökkentését és a betöltési idő javulását. [További információ a képek méretezéséről](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Méretezze megfelelően a képeket"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "A szövegalapú forrásokat tömö<PERSON> (gzip, Deflate v<PERSON><PERSON>) célszerű továbbítani a hálózati adatforgalom minimalizálása érdekében. [További információ a szöveg tömörítéséről](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Engedélyezze a szövegtömörítést"}, "core/audits/content-width.js | description": {"message": "Ha az alkalmazás tartalmának szélessége nem egyezik a megjelenítési terület szélességével, ak<PERSON> le<PERSON>t, hogy alkalmazása nincs optimalizálva a mobilok képernyőjére. [További információ a tartalom megjelenítési területhez való méretezéséről](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "A megjelenítési terület mérete ({innerWidth} képpont) nem egyezik az ablak méretével ({outerWidth} képpont)."}, "core/audits/content-width.js | failureTitle": {"message": "A tartalom nincs megfelelően méretezve a megjelenítési területhez"}, "core/audits/content-width.js | title": {"message": "A tartalom megfelelően van méretezve a megjelenítési területhez"}, "core/audits/critical-request-chains.js | description": {"message": "Az alábbi kritikus kérésláncok megjelenítik, hogy milyen források töltődnek be magas prioritással. Az oldalbetöltés javítása érdekében fontolja meg a láncok hosszának csökkentését, a letöltött források méretének csökkentését vagy a felesleges források letöltésének késleltetését. [További információ a kritikus kérések láncba rendezésének elkerüléséről](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 lánc található}other{# lánc található}}"}, "core/audits/critical-request-chains.js | title": {"message": "Kerülje el a kritikus kérések láncba fűzését"}, "core/audits/csp-xss.js | columnDirective": {"message": "Utasítás"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "A hatékony Tartalombiztonsági <PERSON> (CSP) jelentősen csökkentik a webhelyek közötti, parancsfájlt alkalmazó (XSS) támadások kockázatát. [További informáci<PERSON>, hogy miként előzhető meg CSP használatával az XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Az oldal tartalmaz egy <meta> címkében definiált CSP-t. Fontolja meg a CSP áthelyezését HTTP-fejlécbe, vagy adjon meg másik szigorú CSP-t egy HTTP-fejlécben."}, "core/audits/csp-xss.js | noCsp": {"message": "<PERSON>em <PERSON>ó CSP a kényszerítő módban"}, "core/audits/csp-xss.js | title": {"message": "Gondoskod<PERSON> arról, hogy a CSP hatékony az XSS-támadásokkal szemben"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Elavulás / Figyelmeztetés"}, "core/audits/deprecations.js | columnLine": {"message": "Sor"}, "core/audits/deprecations.js | description": {"message": "Az elavult API-k előbb-utóbb kikerülnek a böngészőből. [További információ az elavult API-król](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 figyelmeztetés}other{# figyelmeztetés}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Elavult API-kat <PERSON>"}, "core/audits/deprecations.js | title": {"message": "Ker<PERSON>li az elavult API-kat"}, "core/audits/dobetterweb/charset.js | description": {"message": "Kötelező deklarálnia a karakterkódolást. Ezt megteheti egy `<meta>` címkében, ha az a HTML első 1024 bájtjá<PERSON> kerül, vagy a HTTP-válasz Content-Type fejlécében. [További információ a karakterkódolás deklarálásáról](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "A charset deklaráci<PERSON><PERSON>k, vagy t<PERSON> k<PERSON> szerepel a HTML-ben"}, "core/audits/dobetterweb/charset.js | title": {"message": "Meg<PERSON><PERSON><PERSON><PERSON><PERSON> megh<PERSON> charset"}, "core/audits/dobetterweb/doctype.js | description": {"message": "A doctype meghat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a böngésző visszafelé kompatibilis módra váltson. [További információ a doctype deklarálásáról](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "A doctype nevének a `html` szövegnek kell lennie"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "A dokumentum `doctype` elemet tarta<PERSON>, amely a következőt aktiválja: `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "A dokumentumnak doctype szakaszt kell tartalmaznia"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "A publicId nem üres karakterlánc"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "A systemId mező nem üres karakterlánc"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "A dokumentum `doctype` elemet tarta<PERSON>, amely a következőt aktiválja: `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Az oldalon nincs HTML doctype, ez<PERSON>rt vissza<PERSON><PERSON> kompatibilis módot indít"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Az oldalon szerepel a HTML doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Jellemző"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "A nagy méretű DOM megnöveli a memória<PERSON>, hosszabb ideig tartó [st<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t<PERSON><PERSON>](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) igényel, valamint költséges [elrendez<PERSON>-újraszámítással](https://developers.google.com/speed/articles/reflow) jár. [További információ a túl nagy méretű DOM elkerüléséről](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elem}other{# elem}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Ke<PERSON><PERSON><PERSON><PERSON> a túl nagy DOM-méretet"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "<PERSON><PERSON><PERSON>-mélys<PERSON>g"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM-el<PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Alárendelt elemek maximális <PERSON>"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> a túlzó DOM-méretet"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "A tartózkodási helyre vonat<PERSON><PERSON> engedély váratlan kérése bizalmatlanságra adhat okot, valamint összezavarhatja a felhasználókat. Érdemes inkább felhasználói műveletekhez kötni a kérést. [További információ a földrajzihely-meghatározásra vonatkozó engedélyről](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Oldalbetöltéskor a földrajzi helyre vonat<PERSON>ó engedélyt kéri"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> a földrajzi hely<PERSON> von<PERSON><PERSON> engedély k<PERSON>ét oldalbetöltéskor"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "A probléma típusa"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "A Chrome fejlesztői eszközök `Issues` panelén feltüntetett problémák megoldatlan problémákat jeleznek. A problémák okai lehetnek sikertelen hálózati kérések, nem megfelelő biztonsági ve<PERSON>k, vala<PERSON>t más, böngészővel kapcsolatos tényezők is. Nyissa meg a Chrome fejlesztői eszközök Issues (Problémák) panelét, ahol további információkat találhat az egyes problémákról."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Problémák találhatók a(z) `Issues` panelen a Chrome fejlesztői eszközökben"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Eredeteken átívelő kérelmekre vonatkozó házirend <PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "A hirdetések magas erőforráshasználata"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "<PERSON>em <PERSON>hatók problémák a Chrome fejlesztői eszközök `Issues` panelén"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Az oldalon észlelt minden front-end JavaScript-függvénytár. [További információ a JavaScript-függvénytár észlelésével kapcsolatos diagnosztikai ellenőrzésről](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Észlelt JavaScript-függvénytárak"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "A külső szkriptek `document.write()` segítségével történő dinamikus beillesztése több tíz másodperccel lassíthatja az oldalbetöltést a lassú kapcsolaton csatlakozó felhasználók esetében. [További információ a document.write() elkerüléséről](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Ne használja a következőt: `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Kerüli a(z) `document.write()` hasz<PERSON>lat<PERSON><PERSON>"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Az értesítése<PERSON><PERSON> von<PERSON> engedély váratlan kérése bizalmatlanságra adhat okot, valamint összezavarhatja a felhasználókat. Érdemes inkább felhasználói műveletekhez kötni a kérést. [További információ az értesítések<PERSON> vonatko<PERSON> engedély felelősségteljes kéréséről](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Oldalbetöltéskor az értesítések<PERSON> von<PERSON> engedély<PERSON> kéri"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> az értesíté<PERSON>k<PERSON> engedély k<PERSON><PERSON><PERSON>t oldalbetöltéskor"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "A HTTP/2 számos előnyt nyújt a HTTP/1.1-he<PERSON> k<PERSON>, p<PERSON><PERSON><PERSON><PERSON> bin<PERSON> fejléceket és multiplexelést. [További információ a HTTP/2-ről](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 nem HTTP/2-t hasz<PERSON><PERSON><PERSON> kéré<PERSON>}other{# nem HTTP/2-t hasz<PERSON><PERSON><PERSON> kéré<PERSON>}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Használjon HTTP/2-t"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Fontolja meg az érintési és görgetési eseményfigyelők megjelölését mint `passive`, hogy javuljon az oldal görgetést érintő teljesítménye. [További információ a passzív eseményfigyelők alkalmazásáról](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Nem <PERSON>l passzív figyelőket a görgetés teljesítményének javításához"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Passzív figyelőket alkalmaz a görgetés teljesítményének javításához"}, "core/audits/errors-in-console.js | description": {"message": "A konzolon megjelenő hibák megoldatlan problémákat jeleznek. Okaik lehetnek sikertelen hálózati ké<PERSON>k, valamint más böngészős tényezők is. [További információ a konzol diagnosztikai ellenőrzésekor fellépő hibákról](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "A böngészőhibák a konzolon láthatók"}, "core/audits/errors-in-console.js | title": {"message": "<PERSON>em k<PERSON> böngészőhiba a konzolra"}, "core/audits/font-display.js | description": {"message": "A `font-display` CSS-függ<PERSON><PERSON><PERSON> bekapcsolásával biztosíthatja, hogy a szöveg olvasható legyen a felhasználók számára, mialatt a webes betűtípusokat betölti a rendszer. [További információ a következőről: `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Biztosítsa, hogy a szöveg látható marad a webes betűtípusok betöltése során"}, "core/audits/font-display.js | title": {"message": "Az összes szöveg látható marad a webes betűtípusok betöltésekor"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON><PERSON>in,plural, =1{A Lighthouse nem tudta automatikusan ellenőrizni a(z) `font-display` értéket a(z) {fontOrigin} eredetnél.}other{A Lighthouse nem tudta automatikusan ellenőrizni a(z) `font-display` értékeket a(z) {fontOrigin} eredetnél.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Képarány (tényleges)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Képarány (megjelenített)"}, "core/audits/image-aspect-ratio.js | description": {"message": "A képmegjelenítési méretek ideális esetben természetes képarányt alkalmaznak. [További információ a képméretarányról](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Hibás képaránnyal jeleníti meg a képeket"}, "core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON><PERSON> jele<PERSON>íti meg a képeket"}, "core/audits/image-size-responsive.js | columnActual": {"message": "<PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/image-size-responsive.js | description": {"message": "A kép természetes méreteinek arányosnak kell lenniük a megjelenítési mérettel és a pixelaránnyal a lehető legnagyobb képtisztaság érdekében. [További információ a reszponzív képek megadásáról](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "A képek megjelenítése alacsony felbontásban történik"}, "core/audits/image-size-responsive.js | title": {"message": "A képek megjelenítése megfelelő felbontásban történik"}, "core/audits/installable-manifest.js | already-installed": {"message": "Az alkalmazás már telepítve van."}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "<PERSON><PERSON> letölteni a szükséges ikont a manifestből"}, "core/audits/installable-manifest.js | columnValue": {"message": "A hiba oka"}, "core/audits/installable-manifest.js | description": {"message": "A service worker elnevezésű technológia lehető teszi az alkalmazás számára a progresszív webes alkalmazások funkcióinak használatát. Ilyen funkció például az offline működés, a kezdőképernyőhöz való hozzáadás és a leküldött (push) értesítések. A megfelelő service worker- és manifestmegvalósítással rendelkező böngészők proaktív módon kérhetik a felhasználókat, hogy adják hozzá az Ön alkalmazását a kezdőképernyőjükhöz, ami erősebb elköteleződéshez vezethet. [További információ a manifest telepíthetőségi követelményeiről](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 ok}other{# ok}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Az internetes alkalmazás manifestje vagy service workere nem felel meg a telepíthetőségi követelményeknek"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "A Play Áruház alkalmazás-URL-je és a Play Áruház azonosítója nem egyezik."}, "core/audits/installable-manifest.js | in-incognito": {"message": "Az oldal inkognitó módban lett betöltve."}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "A manifest „display” tulajdonságának a következők egyikének kell lennie: „standalone”, „fullscreen” vagy „minimal-ui”."}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "A manifest tartalmazza a „display_override” <PERSON><PERSON><PERSON><PERSON>, és az első támogatott megjelenítési módnak a következők egyikének kell lennie: „standalone”, „fullscreen” vagy „minimal-ui”."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "<PERSON><PERSON> a manifest lekérése vagy elemzése, vagy üres a manifest."}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "A manifest URL-je módosítva lett a manifest lekérése során."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "A manifest nem tartalmaz „name” v<PERSON><PERSON> „short_name” <PERSON><PERSON><PERSON><PERSON>."}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "A manifest nem tartalmaz megfelelő ikont. PNG-, SVG- vagy WebP-formátum<PERSON> van szükség legalább {value0} px méretben. Be kell állítani a méretek attribútumát. Ha be van állítva a cél attribútuma, tartalmaznia kell az „any” (bármilyen) értéket."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nincs megadva olyan ikon legalább {value0} k<PERSON><PERSON><PERSON><PERSON>, négyzetes méretben, PNG-, SVG- vagy WebP-<PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON><PERSON> célattribútuma ninc<PERSON>, vagy „any” (bármely) <PERSON><PERSON><PERSON><PERSON><PERSON> állítva."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "A letöltött ikon üres vagy sérült"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Nincs megadva Play Áruház-azonosító."}, "core/audits/installable-manifest.js | no-manifest": {"message": "<PERSON>z oldalon nem található manifest-URL (<link>)"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "<PERSON><PERSON> egyez<PERSON> service worker. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy újra be kell töltenie az oldalt, v<PERSON><PERSON> <PERSON> kell, hogy a service worker jelenlegi oldalra vonatkozó scope-ja tartalmazza-e a manifestben található scope-ot és start_url-t."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "<PERSON><PERSON> a service worker <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mivel nem tal<PERSON><PERSON><PERSON><PERSON> „start_url” mező a manifestben."}, "core/audits/installable-manifest.js | noErrorId": {"message": "<PERSON>em lehet felismerni a következő telepíthetőségi hibaazonosítót: {errorId}"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Az oldal megjelenítése nem biztonságos eredetről történik"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Az oldal nem töltődött be a fő keretbe."}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Az oldal nem működik offline állapotban"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Megtörtént a PWA eltávolítása, visszaállnak a telepíthetőségi ellenőrzések."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "A megadott alkalmazásplatform nem támogatott Androidon."}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "A manifest meghatározza a következőt: „prefer_related_applications: true”"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "A prefer_related_applications csak a Chrome bétaverziójában és a Stabil csatornákon támogatott Androidon."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "A Lighthouse nem tudta me<PERSON><PERSON><PERSON>, hogy volt-e service worker. Próbálja újra a Chrome újabb verziój<PERSON>val."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "A manifest URL-sémája ({scheme}) nem támogatott Androidon."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "A manifest kezdési URL-je érvénytelen"}, "core/audits/installable-manifest.js | title": {"message": "Az internetes alkalmazás manifestje és a service worker nem felel meg a telepíthetőségi követelményeknek"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "A manifestben található URL felhasználónevet, j<PERSON><PERSON><PERSON>t vagy portot tartalmaz."}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Az oldal nem működik offline állapotban. A rendszer nem tekinti telepíthetőnek az oldalt a Chrome 93-as verzi<PERSON>ja után (stabil kiadás: 2021. augusztus)."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Nem bi<PERSON>tonságos URL"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Minden webhelyet HTTPS-sel kell védeni, még akkor is, ha nem kezelnek bizalmas adatokat Ez vonatkozik a [vegyes tartalmak](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) elkerülésére is, amelyek esetében egyes források annak ellenére töltődnek be HTTP-kapcsolaton keresztül, hogy a kezdeti kérés HTTPS-kapcsolaton keresztül lett kiszolgálva. A HTTPS megakadályozza, hogy behatolók módosítsák vagy passzívan megfigyeljék az alkalmazás és a felhasználók közötti kommunikációt. Ezt a protokollt megköveteli a HTTP/2, valamint számos új webes API is. [További információ a HTTPS-ről](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 nem biztons<PERSON> kérés}other{# nem biztons<PERSON>gos kérés}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "<PERSON>em használ HTTPS-t"}, "core/audits/is-on-https.js | title": {"message": "HTTPS-t használ"}, "core/audits/is-on-https.js | upgraded": {"message": "Automatikusan frissítve HTTPS-re"}, "core/audits/is-on-https.js | warning": {"message": "Figyelmeztetéssel engedélyezve"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "<PERSON><PERSON> a leg<PERSON><PERSON><PERSON>, tartalommal rendelkező elem a megjelenítési területen. [További információ a Legnagyobb vizuális tartalom elemről](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | title": {"message": "A legnagyobb vizuális tartalomválasz eleme"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-hozzájárulás"}, "core/audits/layout-shift-elements.js | description": {"message": "Ezek a DOM-elemek járulnak hozzá leginkább az oldal CLS-éhez. [További információ a CLS javításáról](https://web.dev/optimize-cls/)."}, "core/audits/layout-shift-elements.js | title": {"message": "Az elrendezés nagy mértékű mozgásának elkerülése"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "A késleltetve betöltött, haj<PERSON><PERSON> feletti képek az oldal életciklusában később jelennek meg, ami késleltetheti a legnagyobb vizuális tartalomválaszt. [További információ az optimális késleltetett betöltésről](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "A legnagyobb vizuális tartalomválasz késleltetve lett betöltve"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "A legnagyobb vizuális tartalomválasz nem késleltetve lett betöltve"}, "core/audits/long-tasks.js | description": {"message": "A fő szál leghosszabb feladatait listázza. Hasznos az interakciótól számított késéshez legnagyobb mértékben hozzájáruló elemek azonosításához. [További információ a<PERSON>, hogy miként kerülhetők el a hosszú feladatok a fő szálban](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# hosszú feladat található}other{# hosszú feladat található}}"}, "core/audits/long-tasks.js | title": {"message": "Kerülje a fő szálban a hosszú feladatokat"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategória"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Érdemes csökkenteni a JavaScript elemzésére, kompilálására és végrehajtására fordított időt. E<PERSON><PERSON> seg<PERSON>, ha kisebb méretű JavaScript-forrásokat továbbít. [További információ a fő szál terhelésének minimalizálásáról](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimalizálja a fő szál terhelését"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimalizálja a fő szál terhelését"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "A lehető legtöbb felhasználó elérése érdekében a webhelyeknek minden elterjedtebb böngészőben működniük kell. [További információ a több böngészőre kiterjedő kompatibilitásról](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "A webhely több<PERSON><PERSON><PERSON> b<PERSON> is működik"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Lássa el az egyes oldalakat mélylinkként használható URL-ekkel, és ügyeljen arra, hogy ezek az URL-ek egyediek legyenek a közösségi médiában való megosztás céljából. [További információ a mélylinkek megadásáról](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Minden oldal rendelkezik URL-címmel"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "A koppintgatás során az átmeneteknek még lassú h<PERSON> is gyorsnak kell lenniük. Ez az egyik legfontosabb tényező a felhasználók által észlelt teljesítmény tekintetében. [További információ az oldalátmenetről](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Az oldalak közti váltásnak nem szabadna akadályozni a hálózati forgalmat"}, "core/audits/maskable-icon.js | description": {"message": "Maszkolható ikonnal biztosíthatja, hogy a kép a forma egészét kitöltse: ne kerüljön köré letterbox, amikor a felhasználó eszközre telepíti az alkalmazást. [További információ a maszkolható manifestikonokról](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "A manifest nem tartalmaz maszkolható ikont"}, "core/audits/maskable-icon.js | title": {"message": "A manifest tartalmaz maszkolható ikont"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Az Elrendezés összmozgása mutató az oldal megjelenítési területén látható elemek mozgását méri. [További információ az Elrendezés összmozgása mutatóról](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Az interakciótól a következő vizuális válaszig eltelt idő az oldal válaszadási hajlandóságát méri, vagyi<PERSON> azt, hogy az oldal mennyi idő alatt reagál láthatóan a felhasználói bevitelre. [További információ Az interakciótól a következő vizuális válaszig eltelt idő mutatóról](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON>z első vizuális tartalomválasz azt az időpontot jel<PERSON>li, amikor a rendszer megkezdi az első szöveg vagy kép megjelenítését. [További információ az Első vizuális tartalomválasz mutatóról](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Az első releváns vizuális válasz azt méri, hogy mikor válik láthatóvá az oldal elsődleges tartalma. [További információ az Első releváns vizuális válasz mutatóról](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Az interaktivitásig eltelt idő az az idő, amely ahhoz szükséges, hogy az oldal teljesen interaktívvá váljon. [További információ az Interaktivitásig eltelt idő mutatóról](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "A Legnagyobb vizuális tartalom betöltési ideje azt mutatja meg, hogy mennyi idő telik el a legnagyobb szöveg vagy kép megjelenítéséig. [További információ a Legnagyobb vizuális tartalom betöltési ideje mutatóról](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "A felhasználók által tapasztalható, első interakciótól számított esetleges maximális válaszkésés a leghosszabb feladat időtartama. [További információ az Első interakciótól számított esetleges maximális válaszkésés mutatóról](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "A Sebességindex mutató azt j<PERSON>, hogy az adott oldal tartalmai milyen gyorsan válnak láthatóvá. [További információ a Sebességindex mutatóról](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "<PERSON>z első vizuális tartalomválasz és az interaktivitásig eltelt idő közötti minden (50 ms-nál hosszabb feladatot jelentő) periódus időtartamának összege milliszekundumban kifejezve. [További információ a Teljes tiltási idő mutatóról](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "A hálózati oda-vissza út ideje (RTT) nagy hatással van a teljesítményre. Ha túl magas az RTT értéke valamelyik eredet esetében, az azt jelenti, hogy a felhasználóhoz közelebbi szerverek használata javíthatja a teljesítményt. [További információ az oda-vissza út idejéről](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Oda-visszautak ideje a hálózaton"}, "core/audits/network-server-latency.js | description": {"message": "A szerverek várakozási ideje hatással lehet a webes teljesítményre. Túlterhelést vagy nem megfelelő háttérteljesítmény jelez, ha az eredetszerver várakozási ideje túl magas. [További információ a szerverválaszidőről](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Várakozási idő a háttérszervereknél"}, "core/audits/no-unload-listeners.js | description": {"message": "Az `unload` esem<PERSON>y működése nem megbízható, és figyelése megakadályozhatja a böngésző optimalizálását (például az előre-vissza gyorsítótárazást). Használja helyette a következő események valamelyikét: `pagehide` vagy `visibilitychange`. [További információ az eseményfigyelők kiürítéséről](https://web.dev/bfcache/#never-use-the-unload-event)."}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "`unload`-<PERSON><PERSON><PERSON><PERSON><PERSON> regisztrál"}, "core/audits/no-unload-listeners.js | title": {"message": "Elkerüli a(z) `unload`-eseményfigyelőket"}, "core/audits/non-composited-animations.js | description": {"message": "A nem kompozit animációk minősége elégtelen lehet, és az ilyen animációk növelhetik a CLS-t. [További információ a nem kompozit animációk elkerüléséről](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animált elem található}other{# animált elem található}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "A szűrővel kapcsolatos tulajdonság elmozdíthatja a képpontokat"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "A cél másik, nem kompatibilis animációval rendelkezik"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Az effekt nem a „replace” kompozitmóddal rendelkezik"}, "core/audits/non-composited-animations.js | title": {"message": "Kerülje a nem kompozit animációk használatát"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "<PERSON>z átalakítással kapcsolatos tulajdonság a mező méretétől függ"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nem támogatott CSS-tulajdonság: {properties}}other{Nem támogatott CSS-tulajdonságok: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Az effekt nem támogatott időzítési paraméterekkel rendelkezik"}, "core/audits/performance-budget.js | description": {"message": "A hálózati kérések száma és mérete maradjon a megadott teljesítmény-határértékek által beállított célértékek alatt. [További információ a teljesítmény-határértékekről](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 kérés}other{# kérés}}"}, "core/audits/performance-budget.js | title": {"message": "Teljesítménybüdzsé"}, "core/audits/preload-fonts.js | description": {"message": "Töltse elő az `optional` bet<PERSON><PERSON><PERSON><PERSON>okat, hogy az első alkalommal érkező látogatók is használhassák őket. [További információ a betűtípusok előtöltéséről](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "A(z) `font-display: optional` attribútumot hasz<PERSON><PERSON>ó <PERSON>típusokat nem tölti elő a rendszer"}, "core/audits/preload-fonts.js | title": {"message": "A(z) `font-display: optional` attribútumot hasz<PERSON><PERSON>ó <PERSON>típusokat előtölti a rendszer"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Ha az LCP-elemet dinamikusan adja hozzá az oldalhoz, el<PERSON> kell töltenie a képet az LCP javításához. [További információ az LCP-elemek előtöltéséről](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "A legnagyobb vizuális tartalomválasz képének előtöltése"}, "core/audits/redirects.js | description": {"message": "Az átirányítások további késlekedéssel hosszabbítják meg az oldalbetöltéshez szükséges időt. [További információ az oldalátirányítások elkerüléséről](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Kerülje a többszörös oldalátirányítást"}, "core/audits/resource-summary.js | description": {"message": "Az oldal forrásainak mennyiségére és méretére vonatkozó határértékeket egy budget.json fájl hozzáadásával határozhatja meg. [További információ a teljesítmény-határértékekről](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 kérelem • {byteCount, number, bytes} KiB}other{# kérelem • {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "A kérések száma legyen kevés, az átvitelek pedig kis méretűek"}, "core/audits/seo/canonical.js | description": {"message": "A szabványos linkek a keresési eredményként megjelenítendő URL-re tesznek javaslatot. [További információ a szabványos linkekről](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "T<PERSON>bb ütköző URL ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Érvénytelen URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "<PERSON><PERSON><PERSON> `hreflang` helyre mutat ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "<PERSON><PERSON> a<PERSON>t URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "A domain gyökér-URL-jére (a kezdőlapra) mutat egyenértékű tartalommal rendelkez<PERSON> oldal helyett"}, "core/audits/seo/canonical.js | failureTitle": {"message": "A dokumentumhoz nem tartozik érvényes `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "A dokumentum érvényes `rel=canonical` attribútumot tartalmaz"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Feltérképezhetetlen link"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "A keresőmotorok `href` attribútumokat használhatnak a linkeknél a webhelyek feltérképezéséhez. Biztosítsa, hogy a horgonyelemek `href` attribútuma a megfelelő céloldalra hivatkozzon, hogy a webhely minél több oldala felfedezhető legyen. [További információ arró<PERSON>, hogy miként teheti a linkeket feltérképezhetővé](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Nem le<PERSON> a linkek feltérképezése"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "A linkek feltérképezhetők"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "További olvashatatlan szöveg"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Bet<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Oldal szövegének %-a"}, "core/audits/seo/font-size.js | columnSelector": {"message": "V<PERSON>lasztó"}, "core/audits/seo/font-size.js | description": {"message": "A 12 képpontnál kisebb betűméretek nehezen láthatók, ez<PERSON>rt a mobilhasználóknak a szöveget elolvasásához fel kell nagyítaniuk a képernyő tartalmát. Törekedjen arra, hogy az oldal szövegének legalább 60%-a minimum 12 képpontos betűmérettel jelenjen meg. [További információ az olvasható betűméretekről](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent}-nyi olvasható szöveg"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "A szöveg olvashatatlan, ugyanis a látható terület metacímkéje nincs optimalizálva a mobilképernyőkre"}, "core/audits/seo/font-size.js | failureTitle": {"message": "A dokumentum olvashatatlan betűméreteket használ"}, "core/audits/seo/font-size.js | legibleText": {"message": "Olvasható szöveg"}, "core/audits/seo/font-size.js | title": {"message": "A dokumentum olvasható betűméreteket tartalmaz"}, "core/audits/seo/hreflang.js | description": {"message": "A hreflang linkek azt mondják meg a keresőmotoroknak, hogy nyelvtől vagy régiótól függően az oldal melyik változatát kell megjeleníteniük a keresési találatokban. [További információ a következőről: `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "A dokumentum nem rendelkezik érvényes `hreflang` attribútummal"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relatív href-érték"}, "core/audits/seo/hreflang.js | title": {"message": "A dokumentum érvényes `hreflang` attribútumot tartalmaz"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Váratlan nyelvi kód"}, "core/audits/seo/http-status-code.js | description": {"message": "A sikertelenséget jelző HTTP-állapotkóddal rendelkező oldalak indexelése eredménytelen lehet. [További információ a HTTP-állapotkódokról](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Az oldal sikertelenséget jelző HTTP-állapotkóddal rendelkezik"}, "core/audits/seo/http-status-code.js | title": {"message": "Az oldal sikerességet jelző HTTP-állapotkóddal rendelkezik"}, "core/audits/seo/is-crawlable.js | description": {"message": "A keresőmotorok azokat az oldalakat nem tudják keresési eredményként megjeleníteni, amely<PERSON> feltérképezésére nincs engedélyü<PERSON>. [További információ a feltérképezési utasításokról](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Az oldal indexelését <PERSON>"}, "core/audits/seo/is-crawlable.js | title": {"message": "Az oldalnál nincs letiltva az indexelés"}, "core/audits/seo/link-text.js | description": {"message": "A leíró jellegű linkszövegek segítenek a keresőmotoroknak a tartalmak értelmezésében. [További információ a linkek hozzáférhetőbbé tételéről](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 link található}other{# link található}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "A linkek nem rendelkeznek leíró jellegű szöveggel"}, "core/audits/seo/link-text.js | title": {"message": "A linkek leíró jellegű szöveggel rendelkeznek"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Ellenőrizze a strukturált adatok érvényességét a [Strukturált adatok tesztelőeszköz](https://search.google.com/structured-data/testing-tool/) és a [Structured Data Linter](http://linter.structured-data.org/) segítségével. [További információ a strukturált adatokról](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "A strukturált adatok érvényesek"}, "core/audits/seo/meta-description.js | description": {"message": "Az oldaltartalom tömör összefoglalásának érdekében metaleírások szerepelhetnek a keresési eredményekben. [További információ a metaleírásról](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "A Leírás mező üres."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "A dokumentum nem rendelkezik metaleírással"}, "core/audits/seo/meta-description.js | title": {"message": "A dokumentum rendelkezik metaleírással"}, "core/audits/seo/plugins.js | description": {"message": "Sok eszköz korlátozza vagy nem támogatja a beépülő modulokat, a keresőmotorok pedig nem tudják indexelni a modulok által megjelenített tartalmakat. [További információ a beépülő modulok elkerüléséről](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "A dokumentum beépülő modulokat használ"}, "core/audits/seo/plugins.js | title": {"message": "A dokumentum nem használ beépülő modulokat"}, "core/audits/seo/robots-txt.js | description": {"message": "Ha a robots.txt fájl form<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a feltérképező robotok nem tudj<PERSON>, hogy Ön hogyan szeretné feltérképeztetni vagy indexeltetni a webhelyét. [Tov<PERSON><PERSON>i információ a robots.txt fájlról](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "A robots.txt fájlra irányuló kéré<PERSON> a következő HTTP-állapotkóddal tért vissza: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 hiba található}other{# hiba található}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "A Lighthouse nem tudta letölteni a robots.txt fájlt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "A robots.txt fájl nem érvényes"}, "core/audits/seo/robots-txt.js | title": {"message": "A robots.txt fájl érvényes"}, "core/audits/seo/tap-targets.js | description": {"message": "Az interaktív elemeknek (például a gomboknak és a linkeknek) elég nagynak kell lenni<PERSON>k (48 × 48 képpont), és elég helynek kell lennie k<PERSON>ük, hogy könnyen rá<PERSON>k le<PERSON> kop<PERSON> a szomszédos elemek megérintése nélkül. [További információ a koppintási célokról](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent}-nyi megfelelően méretezett koppintható elem"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "A koppintási célok túl k<PERSON>, ugyanis a látható terület metacímkéje nincs optimalizálva a mobilképernyőkre"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "A koppintási célok mérete nem megfelelő"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Átfedésben lévő cél"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "Koppintási cél"}, "core/audits/seo/tap-targets.js | title": {"message": "A koppintási célok mérete megfelelő"}, "core/audits/server-response-time.js | description": {"message": "Érdemes a szerver válaszidejét lerövidíteni a fő dokumentum esetében, mert az összes további kérés függ tőle. [További információ az Első bájt betöltéséhez szükséges idő mutatóról](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms a gyökérdokumentumhoz"}, "core/audits/server-response-time.js | failureTitle": {"message": "A szerver kezdeti válaszidejének csökkentése"}, "core/audits/server-response-time.js | title": {"message": "A kezdeti szerverválaszidő rövid volt"}, "core/audits/service-worker.js | description": {"message": "A service worker elnevezésű technológia lehető teszi az alkalmazás számára a progresszív webes alkalmazások funkcióinak használatát. Ilyen funkció például az offline működés, a kezdőképernyőhöz való hozzáadás és a leküldött (push) értesítések. [<PERSON>v<PERSON><PERSON><PERSON> információ a service worker technológiáról](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON>z oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` nem ta<PERSON>, ugyanis nem sikerült a manifest érvényes JSON-ként való elemzése"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Az oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` ({startUrl}) nincs a szolgáltató munkatárs hatókörében ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON>z oldalt szolgáltató munkatárs vezérli, azonban a(z) `start_url` nem ta<PERSON>, mert nem si<PERSON>ült lekérni a manifestfájlt."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Ez a forrás rendelkezik legalább egy szolgáltató munk<PERSON>á<PERSON>, azonban az oldal ({pageUrl}) nincs a hatókörükben."}, "core/audits/service-worker.js | failureTitle": {"message": "<PERSON><PERSON> re<PERSON><PERSON><PERSON> m<PERSON>, amely vezérli az oldalt és a(z) `start_url` URL-t"}, "core/audits/service-worker.js | title": {"message": "<PERSON><PERSON><PERSON> munkatársat regisztrál, amely vezérli az oldalt és a(z) `start_url` URL-t"}, "core/audits/splash-screen.js | description": {"message": "A saját témájú betöltési képernyő jó felhasználói élményt eredmé<PERSON>ez, amikor a kezdőképernyőről indítják el az alkalmazást. [További információ a betöltési képernyőről](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "<PERSON><PERSON><PERSON>ítva egyéni betöltési képernyő"}, "core/audits/splash-screen.js | title": {"message": "<PERSON> állítva egyéni betöltési képernyő"}, "core/audits/themed-omnibox.js | description": {"message": "A böngésző címsávjához megadható a webhelyhez illő téma. [Tov<PERSON><PERSON><PERSON> információ a címsávtémák beállításáról](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON><PERSON> be a téma színét a címsávon."}, "core/audits/themed-omnibox.js | title": {"message": "A téma színét állítja be a címsávon."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (Ügyfélszolgálat)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (Marketing)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (Közösségi)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (Videó)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Termék"}, "core/audits/third-party-facades.js | description": {"message": "<PERSON>gy<PERSON> k<PERSON> forrásoktól származó beágyazásoknál késleltetett betöltés lehet észlelhető. Fontolja meg egy kompomenscsomagra való lecserélésüket, amíg nincs rájuk szükség. [További információ a harmadik felek késleltetéséről komponenscsomag segítségével](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# facade-alternatíva áll rendelkez<PERSON>re}other{# facade-alternatíva áll rendelkez<PERSON>re}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "<PERSON>gyes k<PERSON> forrásoknál késleltetett betöltés lehet észlelhető facade-dal"}, "core/audits/third-party-facades.js | title": {"message": "Külső források késleltetett betöltése facade-okkal"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON>dik fél"}, "core/audits/third-party-summary.js | description": {"message": "A harmadik felektől származó kódok jelentős hatással lehetnek a betöltés teljesítményére. Minél kevesebb harmadik féltől származó kódot használjon, és lehetőleg azután töltse be őket, hogy az oldal nagyrészt már betöltődött. [További információ a harmadik felek hatásának minimalizálásáról](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Harmadik féltől származó kód {timeInMs, number, milliseconds} ms-ig akadályozta a fő szál végrehajtását"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Csökkentse a harmadik felek kódjai által kiváltott hatást"}, "core/audits/third-party-summary.js | title": {"message": "Minimalizálja a harmadik felektől származó kódok használatát"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Időkeret beállításával könnyebben figyelheti a webhelye teljesítményét. A jól teljesítő webhelyek gyorsan töltődnek be, és gyorsan válaszolnak a felhasználói beviteli eseményekre. [További információ a teljesítmény-határértékekről](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Időkeret"}, "core/audits/unsized-images.js | description": {"message": "Adja meg a képelemek pontos szélességét és magasságát, hogy kevésbé csússzon el az elrendezés, és javuljon a CLS. [További információ a képméretek beállításáról](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "A képelemekhez nem tartozik meghatározott `width` és `height`"}, "core/audits/unsized-images.js | title": {"message": "A képelemekhez meghatározott `width` és `height` tartozik"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Érdemes lehet felhasználnia alkalmazásában a User Timing API-t, amellyel valós használat során mért teljesítményadatokat kaphat a legfontosabb felhasználói műveletekről. [További információ a Felhasználói időmérés jeleiről](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 felhasználói időmérés}other{# felhasználói időmérés}}"}, "core/audits/user-timings.js | title": {"message": "Felhasználói időzítőjelek és intézkedések"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "`<link rel=preconnect>` tartozik a(z) „{securityO<PERSON>in}” c<PERSON><PERSON><PERSON><PERSON>, de a böngésző nem használta. Ellen<PERSON><PERSON>ze, hogy megfelelően használja-e a(z) `crossorigin` attribútumot."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Vegye fontolóra `preconnect` vagy `dns-prefetch` er<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ho<PERSON>, hogy korai kapcsolatokat hozhasson létre harmadik felekhez tartozó fontos forrásokkal. [További informá<PERSON>ó <PERSON>, hogyan lehet előcsatlakozni a szükséges eredetekhez](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "Csatlakozzon előre a szükséges forrásokhoz"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Több mint két `<link rel=preconnect>` kapcsolat található. Ezeket érdemes csak ritkán has<PERSON>lnia, a legfontosabb eredethelyekhez."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "`<link rel=preconnect>` tartozik a(z) „{securityO<PERSON>in}” c<PERSON><PERSON><PERSON><PERSON>, de a böngésző nem használta. Csak az oldal által majd biztosan k<PERSON>tt, fontos forrásokhoz használja a `preconnect` lehetőséget."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Előtöltési `<link>` tartozik a(z) „{preloadURL}” cím<PERSON>z, de a böngésző nem használta. Ellen<PERSON><PERSON>ze, hogy megfelelően használja-e a(z) `crossorigin` attribútumot."}, "core/audits/uses-rel-preload.js | description": {"message": "Fontolja meg a `<link rel=preload>` has<PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON> el<PERSON>t tölthesse be azokat a forrásokat, amelyeket az aktuális oldalbetöltés egyébként későbbre sorolt. [További információ a legfontosabb kérelmek előtöltéséről](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Töltse be előre a kulcsfontosságú kéréseket"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL-hozzárendelés"}, "core/audits/valid-source-maps.js | description": {"message": "A forrástérképek lefordítják a kicsinyített kódot az eredeti forráskódra. Ez segít a fejlesztőknek az éles verzión végzett hibaelhárításban. A Lighthouse további statisztikákat is tud biztosítani. Fontolja meg a forrástérképek használatát, hogy kihasználhassa ezeket az előnyöket. [További információ a forrástérképekről](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Hi<PERSON><PERSON><PERSON><PERSON>-hozzárendelések a nagy belső JavaScriptnél"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Forrás-hozzárendelés hiányzik az egyik nagy JavaScript-fájlból"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Figyelmeztetés: hiányzik 1 elem a `.sourcesContent` attribútumból}other{Figyelmeztetés: hiányzik # elem a `.sourcesContent` attribútumból}}"}, "core/audits/valid-source-maps.js | title": {"message": "Az oldal érvényes forrás-hozzárendelésekkel rendelkezik"}, "core/audits/viewport.js | description": {"message": "A `<meta name=\"viewport\">` nem csupán optimalizálja az alkalmazást a mobilképernyő-méretekre, hanem meg is akadályoz [egy 300 ezredmásodperces késleltetést a felhasználói bevitelnél](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [További információ a viewport-metacímke használatáról](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` címke"}, "core/audits/viewport.js | failureTitle": {"message": "Nincs `width` vagy `initial-scale` beállítással rendelkez<PERSON> `<meta name=\"viewport\">` címkéje"}, "core/audits/viewport.js | title": {"message": "Van `width` vagy `initial-scale` beállítással rendelkez<PERSON> `<meta name=\"viewport\">` címkéje"}, "core/audits/work-during-interaction.js | description": {"message": "Ez az a szálblokkoló tevékenység, amely Az interakciótól a következő vizuális válaszig eltelt idő során fordul elő. [További információ Az interakciótól a következő vizuális válaszig eltelt idő mutatóról](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms az „{interactionType}” eseményen"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Esemény<PERSON>l"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Munka minimalizálása a fontos interakciók során"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Bemeneti k<PERSON>és"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Prezentáció <PERSON>"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Feldolgozási idő"}, "core/audits/work-during-interaction.js | title": {"message": "Minimalizálja a munkát a fő interakció során"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Ezek a lehetőségek segíthetnek az ARIA alkalmazásban való használatának javításában, ami jobb felhasználói élményt biztosíthat a kisegítő technológiákat (például képernyőolvasót) használó személyeknek."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>, melyek révén alternatív tartalmakat biztosíthat videókhoz és hanganyagokhoz. Ezzel javíthatja a hallás- és látássérült felhasználók élményét."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Hang- és videohív<PERSON>ok"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Itt a kisegítő lehetőségekkel kapcsolatos bevált módszereket találja."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ezek az ellenőrzések olyan lehetőségeket emelnek ki, melyekkel javíthatók a [webalkalmazás kisegítő lehetőségei](https://developer.chrome.com/docs/lighthouse/accessibility/). A kisegítő lehetőségekkel kapcsolatos problémáknak csak egy része észlelhető automatikusan, ezért a manuális ellenőrzés is aj<PERSON><PERSON>t."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Az alábbiak automatikus tesztelőeszközzel nem ellenőrizhető területekre vonatkoznak. További információt a [kisegítő lehetőségek felülvizsgálatáról](https://web.dev/how-to-review/) szóló útmutatónkban talál."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Kisegítő lehetőségek"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Ezek a lehetőségek a tartalom olvashatóságát segítik."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "Kontraszt"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Ezek a lehetőségek a tartalom különböző országokban élő felhasználók általi értelmezését segítik."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Nemzetközi megoldások és honosítás"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Ezek a lehetőségek az alkalmazás szemantikai jellemzőinek fejlesztését segítik. Javíthatják a kisegítő technológiákat (például képernyőolvasót) használó személyek felhasználói élményét."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Nevek és címkék"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "<PERSON><PERSON><PERSON> is van a billentyűzettel való navigáció fejlesztésére az alkalmazásban."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Ezek a lehetőségek a táblázatos és listaformátumú adatok olvasási élményét javítják olyan kisegítő technológiák használata esetén, mint a képernyőolvasó."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Táblázatok és listák"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Böngészőkompatibilitás"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON>lán<PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Bizalom és biztonság"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Felhasználói <PERSON>y"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "A teljesítménybüdzsék elvárásokat határoznak meg a webhely teljesítmény<PERSON><PERSON>."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Büdzsék"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "További információ alkalmazása teljesítményéről. Ezek a számok [nem befolyásolják közvetlenül](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) a teljesítménypontszámot."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnosztika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "A teljesítmény legfontosabb szempontja az, hogy milyen gyorsan jelennek meg a képpontok a képernyőn. Legfontosabb mutatók: <PERSON><PERSON>ő, tartalommal rendelkező leképezés, Első releváns leképezés"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Az első leképezést érintő fejlesztések"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ezek a javaslatok segíthetnek az oldalbetöltés felgyorsításában. Mindazonáltal a teljesítménypontszámra [nincs közvetlen hatásuk](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Lehetőségek"}, "core/config/default-config.js | metricGroupTitle": {"message": "Mutatók"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Növelje az átfogó betöltési élményt annak érdekében, hogy az oldal reszponzív legyen, és a lehető legrövidebb időn belül használhatóvá váljon. Főbb mutatók: interaktivitásig eltelt idő, sebességindex"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Átfogó j<PERSON>"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Teljesítmény"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Ezek az ellenőrzések különböző szempontokból érvényesítik a progresszív webes alkalmazásokat. [További információ arró<PERSON>, mitől lesz jó egy progresszív webes alkalmazás](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ezek az ellenőrzések kötelezők a [progresszív webes alkalmazások alapvető ellenőrzőlistáján](https://web.dev/pwa-checklist/), de a Lighthouse nem végzi el őket automatikusan. Az ellenőrzések a kapott pontszámot nem befolyásolják, de fontos a manuális végrehajtásuk."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Telepíthető"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA-optimalizálás"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Ezekkel az ellenőrzésekkel győződhet meg arról, hogy oldala követi a keresőoptimalizálásra vonatkozó alapvető tanácsokat. Szá<PERSON>lyan, a Lighthouse által itt nem értékelt ténye<PERSON><PERSON> van még, amely hatással lehet a keresésbeli rangsorolásra, beleértve az [Alapvető webes vitals-mutatók](https://web.dev/learn-core-web-vitals/) teljesítményét is. [További információ a Google Kereső Essentials szolgáltatásról](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Ha ezeket a további érvényesítőket is futtat<PERSON>, egyéb bevált SEO-módszereket is ellenőrizhet."}, "core/config/default-config.js | seoCategoryTitle": {"message": "Keresőoptimalizálás"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formázza úgy a HTML-kódot, hogy a feltérképező robotok jobban megérthessék az alkalmazás tartalmait."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> bevált mó<PERSON>"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "A feltérképező robotoknak hozzáférésre van szükségük az alkalmazáshoz annak érdekében, hogy a webhely megjelenhessen a keresési találatok között."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Feltérképezés és indexelés"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Gondoskodjon oldalai mobilbaráttá tételéről, hogy a felhasználóknak ne kelljen nagyítaniuk a tartalmak elolvasásához. [További információ az oldalak mobilbaráttá tételéről](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "<PERSON><PERSON>, a tesztelt eszköz lassabb CPU-val rendelkezik, mint amire a Lighthouse számít. Ez negatív hatással lehet a teljesítményre kapott pontszámra. További információ a [megfelelő CPU-lassulási szorzó kalibrálásáról](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az oldal nem az elvártaknak megfelelően töltődik be, mert a teszt-URL ({requested}) átirányít ide: {final}. Próbálkozzon a második URL közvetlen tesztelésével."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Az oldal túl lassan tölt<PERSON> be, és túllépte az időkeretet. Az eredmények hiányosak lehetnek."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "A böngésző gyorsítótárának törlése időtúllépés miatt megs<PERSON>adt. <PERSON><PERSON><PERSON><PERSON> ú<PERSON>ra az oldalt, és ha a probléma tov<PERSON><PERSON><PERSON> is fen<PERSON><PERSON>, jelentse be a programhibát."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{<PERSON><PERSON><PERSON>ul<PERSON>, hogy a tárolt adatok befolyásolják a betöltési sebességet ezen a helyen: {locations}. Ellenőrizze ezt az oldalt inkognitó ablakban, hogy az érintett források ne lehessenek hatással a teljesítmény értékelésére.}other{Előfordulhat, hogy a tárolt adatok befolyásolják a betöltési sebességet ezeken a helyeken: {locations}. Ellenőrizze ezt az oldalt inkognitó ablakban, hogy az érintett források ne lehessenek hatással a teljesítmény értékelésére.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Az eredetadatok törlése időtúllépés miatt megs<PERSON>adt. <PERSON><PERSON><PERSON><PERSON>ra az oldalt, és ha a probléma továbbra is fen<PERSON><PERSON>, jelentse be a programhibát."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Kizárólag a GET-kéréssel betöltött oldalak alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Kizárólag a 2XX állapotkódú oldalak gyorsítótárazhatók."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "A Chrome JavaScript végrehajtására tett kísérletet észlelt, amíg az oldal a gyorsítótárban volt."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "Az AppBannert kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "<PERSON>z előre-vissza gyorsítótárazást jelzők tiltják le. Keresse fel a chrome://flags/#back-forward-cache oldalt, ahol helyileg tudja erre az eszk<PERSON><PERSON><PERSON> von<PERSON> engedélyezni."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "<PERSON>z előre-vissza gyorsítótárazás le van tiltva a parancssorban."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "A rendszer letiltotta az előre-vissza gyorsítótáraz<PERSON>t, mert nem áll rendelkezésre elegendő memória."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "<PERSON>z előre-vissza gyorsítótárazás meghatalmazó <PERSON> nem támogatott."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Az előzetes megjelenítő számára le van tilt<PERSON> az előre-vissza gyorsítótárazás."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Az oldal gyorsítótárazása nem lehetséges, mert regisztrált figyelőkkel rendelkező BroadcastChannel-példánya van."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Azok az oldalak, am<PERSON><PERSON> fej<PERSON> tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "A gyorsítótár kiürítése szándékosan tö<PERSON>ént."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, hogy lehetővé tegye egy másik oldal gyorsítótárazását."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "A beépülő modult tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "A FileChooser API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "A File System Access API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "A Media Device Dispatchert haszná<PERSON>ó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Az oldal elhagyása közben médialejátszó j<PERSON> le tartalmat."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "A MediaSession API-t használó és lejátszási állapotot beállító oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "A MediaSession API-t használó és műveletkezelőket beállító oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Az előre-vissza gyorsítótárazás le van tiltva a képernyőolvasó miatt."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "A SecurityHandler kezelőt használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "A Serial API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "A WebAuthentication API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "A WebBluetooth API-t használó oldalak nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "A WebUSB API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "A dedikált feldolgozót vagy workletet használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Nem fej<PERSON> be a dokumentum betöltése az oldal elhagyása előtt."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Alkalmazásszalag volt jelen az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Jelen volt a Chrome Jelszókezelő az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "A DOM Distiller működésben volt az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "A DOM Distiller Viewer jelen volt az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Az előre-vissza gyorsítótárazás le van tiltva üzenetkezelési API-t használó bővítmények miatt."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "A hosszú élettartamú kapcsolattal rendelkező bővítményeknek be kell zárniuk a kapcsolatot az előre-vissza gyorsítótárazás megkezdése előtt."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Hosszú <PERSON> ka<PERSON>olattal rendelkező bővítményeknek próbáltak üzeneteket küldeni az előre-vissza gyorsítótárban található kere<PERSON>ez."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "<PERSON>z előre-vissza gyorsítótárazás le van tilt<PERSON> bővítmények mi<PERSON>."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> (pl. űrlap újraküldésével vagy http-jels<PERSON><PERSON>val kapcsola<PERSON> párbeszédpanel) volt látható az oldalhoz az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Az offline oldal volt látható az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Az elfogyó memória miatti beavatkozás sávja jelen volt az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Engedélykérések jelentek meg az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Jelen volt előugró ablakokat blokkoló szoftver az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "A Biztonságos Böngészés részletei láthatók voltak az oldal elhagyásakor."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "A Biztonságos Böngészés visszaélésszerűnek ítélte ezt az oldalt, és letiltotta az előugró ablakokat."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Egy service worker f<PERSON><PERSON><PERSON>, am<PERSON>g az oldal az előre-vissza gyorsítótárban volt."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Az előre-vissza gyorsítótárazás le van tilt<PERSON> dokumentumhiba miatt."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "A FencedFrame elemeket használó oldalak nem tárolhatók előre-vissza gyorsítótárazásban."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, hogy lehetővé tegye egy másik oldal gyorsítótárazását."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "A médiastreamhez hozzáférést biztosító oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "A portált használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Az IdleManagert használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "A nyílt IndexedDB-kapcsolattal rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Nem megfelelő API-k használata."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Azok az <PERSON>, amelyekbe JavaScript van bővít<PERSON><PERSON><PERSON>, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Azok az old<PERSON>k, amelyekbe bővítmény szúrja be a StyleSheet fájlt, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Belső hiba."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "<PERSON>z előre-vissza gyorsítótárazás keepalive kérelem mi<PERSON> le <PERSON> tilt<PERSON>."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "A Billenntyűzár funkciót használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON>em fej<PERSON> be az oldal betöltése az oldal elhagyása előtt."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Azok az oldalak, amelyek fő erőforrása tartalmazza a cache-control:no-cache para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Azok az oldalak, amelyek fő erőforrása tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "A navigációt visszavonták, mielőtt az oldal visszaállítható lett volna az előre-vissza gyorsítótárazásból."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, mert egy aktív hálózati kapcsolat túl sok adatot fogadott. A Chrome korlátozza az egyes oldalak által gyorsítótárazásuk közben fogadható adatok mennyiségét."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "A menet közbeni fetch() lekéréssel vagy XHR-rel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Az oldalt a rendszer kizárta az előre-vissza gyorsítótárból, mert egy aktív hálózati kérés átirányítást tartalmazott."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Az oldalt a rendszer kizárta a gyorsítótárból, mert túl sokáig volt nyitva egy hálózati kapcsolat. A Chrome korlátozza azt az időt, ameddig az egyes oldalak gyorsítótárazásuk közben adatokat fogadhatnak."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "Azok az oldalak, amelyek nem rendelkeznek érvényes válasszal, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigáció tö<PERSON>ént a fő kerettől eltérő keretben."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "A folyamatban lévő indexelt DB-tranzakciókat tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "A menet közbeni hálózati kéréssel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "A menet közbeni lekérési hálózati kérést tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "A menet közbeni hálózati kéréssel rendelkező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "A menet közbeni XHR-hálózati kérést tartalmazó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "A PaymentManagert használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "A kép a képben funkciót használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | portal": {"message": "A portált használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | printing": {"message": "A Nyomtatás felhasználói felületet megjelenítő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Az oldal megnyitása a „`window.open()`” met<PERSON><PERSON><PERSON>, és egy másik lap rá mutató hivatkozást tartalmaz, vagy az oldal ablakot nyitott meg."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Összeomlott az előre-vissza gyorsítótárban lévő oldal megjelenítési folyamata."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Az előre-vissza gyorsítótárban lévő oldallal kapcsolatos megjelenítési folyamatot leállította a rendszer."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "A hangrögzítésre engedélyt kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Azok az old<PERSON>k, <PERSON><PERSON><PERSON>khoz való hozzáférési jogosultságot k<PERSON>rtek, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "A háttérszinkronizálást kérő vagy lekérési jogosultságot kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Azok az <PERSON>k, amelyek <PERSON>-eszközökhöz való hozzáférési jogosultsá<PERSON> k<PERSON>rte<PERSON>, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Az értesítési engedélyt kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "A tárhelyhozzáférést kérő oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "A videórögzítési engedélyt kérelmező oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Csak azok az oldalak gyorsítótárazhatók, amelyek URL-sémája HTTP vagy HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "<PERSON>z oldalt service worker <PERSON><PERSON><PERSON><PERSON><PERSON>, amíg az előre-vissza gyorsítótárazása folyamatban volt."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Service worker fájl kísérelte meg elküldeni az előre-vissza gyorsítótárban lévő oldal számára a következőt: `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "A rendszer törölte a ServiceWorker regisztrációj<PERSON>t, amíg az oldal az előre-vissza gyorsítótárban volt."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON>z oldalt egy service worker fájl aktiválása miatt zárta ki a rendszer az előre-vissza gyorsítótárazásból."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "A <PERSON>rome <PERSON>, és kiürítette az előre-vissza gyorsítótárazott bejegyzéseket."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "A SharedWorkert használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "A SpeechRecognizer API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "A SpeechSynthesist használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "Az oldalon lévő iframe navigációt indított, amely nem fejeződött be."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Azok az oldalak, am<PERSON><PERSON>forrása tartalmazza a cache-control:no-cache para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Azok az oldalak, am<PERSON><PERSON> alerőforrása tartalmazza a cache-control:no-store para<PERSON><PERSON><PERSON>, nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Az oldal túllépte a előre-vissza gyorsítótárban eltölthető időt, és lejá<PERSON>."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Az oldal időtúllépést okozott az előre-vissza gyorsítótárazásakor (valószínűleg a sokáig futó pagehide kezelők miatt)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Az oldal fő keretében kiürítéskezelő található."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Az oldal valamelyik alkeretében kiürítéskezelő található."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "A böngésző megváltoztatta a felhasználói ügynök felülbírálási fejlécét."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "A<PERSON>k az <PERSON>, am<PERSON><PERSON>férést adtak videó vagy hang rögzítéséhez, jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "A WebDatabase-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webHID": {"message": "A WebHID-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "A WebLocksot használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "A WebNfc-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "A WebOTPService API-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "A WebRTC-vel készített oldalak nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | webShare": {"message": "A WebShare-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "A WebSocket használatával készített oldalak nem kerülhetnek az előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "A WebTransport API-t tartalmazó oldalak nem kerülhetnek előre-vissza gyorsítótárba."}, "core/lib/bf-cache-strings.js | webXR": {"message": "A WebXR-t használó oldalak jelenleg nem alkalmasak az előre-vissza gyorsítótárazásra."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "A régebbi böngészőkkel való kompatibilitás érdekében fontolja meg https: és http: URL-sémák megadását (a „strict-dynamic” kulcsszót támogató böngészők figyelmen kívül hagyják ezeket)."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "A disown-opener a CSP3 bevezetésekor megszűnt. <PERSON><PERSON><PERSON><PERSON><PERSON> a Cross-Origin-Opener-Policy fejlécet."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "A referrer a CSP2 bevezetésekor megszűnt. Használja helyette a Referrer-Policy fejlécet."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "A reflected-xss a CSP2 bevezetésekor megszűnt. Használja helyette az X-XSS-Protection fejlécet."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "A hiányzó base-uri lehetővé teszi az injektált <base> címkéknek alap-URL-ek beállítását minden relatív URL-nél (pl. szkripttel) a támadó által irányított domainre. Állítsa a base-uri direktív<PERSON>t „none” vagy „self” értékre."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Az object-src hi<PERSON>ya olyan beépülő modulok beszúrását teszi lehetővé, amelyek nem biztonságos szkripteket futtatnak. Fontolja meg az object-ocr beállítását a „none” értékre, ha teheti."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Hiányzik a script-src utasítás. Ez lehetővé teheti nem biztonságos szkriptek futtatását."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Elfelejtette kitenni a pontosvesszőt? <PERSON>gy tűnik, hogy a(z) {keyword} u<PERSON><PERSON><PERSON><PERSON>, nem pedig kul<PERSON>."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "A nonce-oknak a base64 charsetet kell használniuk."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "A nonce-nak legalább nyolc karakterből kell állnia."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Kerülje az egyszerű URL-sémák ({keyword}) használatát ebben az utasításban. Az egyszerű URL-sémák nem biztonságos domainről származó szkriptek megjelenését idézhetik elő."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Kerülje az egyszerű helyettesítő karakterek ({keyword}) használatát ebben az utasításban. Az egyszerű helyettesítő karakterek nem biztonságos domainről származó szkriptek megjelenését idézhetik elő."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "A jelentés célhelye csak a report-to utasítással van beállítva. Ezt az utasítást csak a Chromium-alapú böngészők támogatják, ez<PERSON><PERSON> aj<PERSON>t a report-uri utasí<PERSON><PERSON> has<PERSON> is."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "A jelentési célt nem állítja be CSP. Ez később megnehezíti a CSP karbantartását, illetve a sebezhetőségek figyelemmel kísérését."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "A gazdagépek engedélyezőlistái gyakran megkerülhetők. Fontolja meg CSP nonce-ok vagy hashek has<PERSON>, és a „strict-dynamic” kul<PERSON><PERSON><PERSON>, ha szükséges."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Ismeretlen CSP-utasítás."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON>, hogy a(z) {keyword} érvényte<PERSON> k<PERSON>."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Az „unsafe-inline” lehetővé teszi nem biztonságos oldalon belüli szkriptek és eseménykezelők végrehajtását. A szkriptek egyenként történő engedélyezése érdekében fontolja meg CSP nonce-ok vagy hashek használatát."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "A régebbi böngészőkkel való kompatibilitás érdekében használhatja az „unsafe-inline” kulcsszót (a nonce-t/hasht támogató böngészők figyelmen kívül hagyják)."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "A CORS `Access-Control-Allow-Headers` kezelésében az engedélyezést nem fogja fedni a helyettesítő karakter (*)."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Le vannak tiltva azok az erőforráskérések, amelyek esetében az URL-ek eltávolított `(n|r|t)` térközkaraktereket és kisebb mint karaktereket (`<`) is tartalmaznak. Az ilyen erőforrások betöltése érdekében távolítsa el az „új sor” jelöléseket, és kódolja a kisebb mint karaktereket az olyan helyeken, mint az elemek attribútumértékei."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "A `chrome.loadTimes()` függvényt megszüntettük. Használja helyette a szabványosított Navigation Timing 2 API-t."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "A `chrome.loadTimes()` függvényt megszüntettük, haszná<PERSON>ja helyette a következő szabványosított API-t: <PERSON><PERSON>."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "A `chrome.loadTimes()` függvényt megszüntettük, használja helyette a következőt a szabványosított Navigation Timing 2 API keretein belül: `nextHopProtocol`."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "A `(0|r|n)` karaktert tartalmazó cookie-kat a böngésző csonkolás helyett el fogja utasítani."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Az ugyanarra az eredetre vonatkozó házirendnek a `document.domain` beállításával történő enyhítésére szolgáló lehetőséget megszüntettük, és alapértelmezés szerint le lesz tiltva. Ez a megszüntetési figyelmeztetés azokra az eredeteken átívelő hozzáférésekre érvényes, amelyek a `document.domain` beállításával lettek engedélyezve."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "A {PH1} eredeteken átívelő iframe-ekből való aktiválásának lehetőségét megszüntettük, és a jövőben el fogjuk távolítani."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Az `-internal-media-controls-overlay-cast-button` v<PERSON><PERSON><PERSON><PERSON><PERSON>ett a `disableRemotePlayback` attribútumot kell használni az alapértelmezett Cast-integr<PERSON><PERSON>ó letilt<PERSON>ához."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "A {PH1} paramétert megszüntettük. Használja helyette a következőt: {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Ez példa egy megszüntetett funkcióval kapcsolatos problémáról s<PERSON>óló, lefordított üzenetre."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Az ugyanarra az eredetre vonatkozó házirendnek a `document.domain` beállításával történ<PERSON> enyhítésére szolgá<PERSON>ó lehetőséget megszüntettük, és alapértelmezés szerint le lesz tiltva. A funkció használatának folytatásához iratkozzon le az eredetkulccsal rendelkező ügynökklaszterekről úgy, hogy a HTTP-válasszal együtt egy `Origin-Agent-Cluster: ?0` fejlécet is elküld a dokumentumok és a keretek esetében. További részletek: https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Az `Event.path` attribútumot megszüntettük, és el fogjuk távolítani. Használja helyette a következőt: `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Az `Expect-CT` fejlécet megszüntettük, és el fogjuk távolítani. A Chrome megköveteli A tanúsítványok átláthatósága funkció használatát a 2018. április 30. után kibocsátott összes nyilvánosan megbízható tanúsítvány esetében."}, "core/lib/deprecations-strings.js | feature": {"message": "További részleteket a funkció állapotát jelző oldalon találhat."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "A `getCurrentPosition()` és `watchPosition()` függvények többé nem működnek nem biztonságos eredetek esetében. A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "A következő függvényeket megszüntettük a nem biztonságos eredetek esetében: `getCurrentPosition()` és `watchPosition()`. A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "A `getUserMedia()` függv<PERSON>y többé nem működik nem biztonságos eredetek esetében. A funkció használatához fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "A `RTCPeerConnectionIceErrorEvent.hostCandidate` paramétert megszüntettük. Használja helyette a következők valamelyikét: `RTCPeerConnectionIceErrorEvent.address` vagy `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "A kereskedői eredetet és a `canmakepayment` service worker-eseményből származó tetszőleges adatokat megszüntettük, és eltávolítjuk őket: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "A webhely olyan hálózatból igényelt alerőforrást, amelyhez csak a felhasználói kiemelt hálózati pozíciója miatt tudott hozzáférni. Az ilyen kérelmek láthatóvá teszik a nem nyilvános eszközöket és szervereket az interneten, és így növelik a webhelyközi kérések hamisításával (CSRF) elkövetett támadások és az információszivárgás kockázatát. A kockázatok csökkentése érdekében a Chrome megszünteti a nem nyilvános részerőforrásokhoz irányuló olyan kérelmeket, amelyeket nem biztonságos környezetből indítanak, és elkezdi blokkolni őket."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "`file:` URL-ekből csak akkor lehet CSS-t betölteni, ha az URL-ek `.css` fájlkiterjesztésre végződnek."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Megszüntettük a `SourceBuffer.abort()` használatának lehetőségét a `remove()` függv<PERSON>y aszinkron tartományeltávolításának megszakítására a specifikációk változása miatt. A funkció támogatását is megszüntetjük a jövőben. Ehelyett figyelje az`updateend` eseményt. Az `abort()` csak arra <PERSON>l, hogy megszakítson egy aszinkron médiatartalom-hozzáfűzést, vagy hogy visszaállítsa az elemző állapotát."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "A specifikációk változása miatt megszüntettük annak lehetőségét, hogy a `MediaSource.duration` értékét a pufferelt és kódolt keretek legmagasabb prezentációs időbélyege al<PERSON> le<PERSON> beállítani. A csonkolt és pufferelt médiatartalmak implicit eltávolításának támogatását meg fogjuk szüntetni a jövőben. Helyette explicit `remove(newDuration, oldDuration)` műveletet hajtson végre az összes olyan `sourceBuffers` esetében, amelyre igaz a következő: `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Ez a módosítás a következő mérföldkő elérésekor lép érvénybe: {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "A webes MIDI még akkor is engedélyt kér a használatra, ha a SysEx nincs megadva itt: `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "A Notification API többé nem használható nem biztonságos helyek esetében. Fontolja meg az alkalmazás biztonságos eredetre (pl. HTTPS-re) való módosítását. További részletek: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "A Notification API-r<PERSON> <PERSON><PERSON><PERSON> engedélyt többé nem lehet eredeteken átívelő iframe-b<PERSON>l kérelmezni. <PERSON><PERSON><PERSON> inkább egy legfelső szintű kerettől kérjen enged<PERSON>, vagy nyisson meg egy új ablakot."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Partnere egy elavult (D)TLS-verzióról tárgyal. <PERSON><PERSON><PERSON><PERSON>, hogy javítsa ki ezt a problémát."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "A nem biztonságos környezetben lévő WebSQL elavult, ez<PERSON>rt hamarosan eltávolítjuk. Használjon webes tárhelyet vagy indexelt adatbázist."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Ha a(z) `overflow: visible` értéket adja meg az img, video és canvas címkéken, el<PERSON>fordulhat, hogy az elem határain kívül hoznak létre vizuális tartalmat. Lásd: https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "A `paymentManager.instruments` paramétert megszüntettük. Használja helyette az igény szerinti telepítést a fizetéskezelőkhöz."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "A(z) `PaymentRequest` hívás megkerülte a Tartalombiztonsági irányelvek (CSP) `connect-src` utasítását. Ez a megkerülés elavult. Adja hozzá a(z) `PaymentRequest` API-ban szereplő fizetési mód azonosítóját (a[z] `supportedMethods` mezőben) a(z) `connect-src` CSP-utasításhoz."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "A `StorageType.persistent` paramétert megszüntettük. <PERSON>z<PERSON><PERSON><PERSON><PERSON> a szabványosított `navigator.storage` paramétert."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "A `<picture>` fölérendelt elemmel rendelkező `<source src>` elem érv<PERSON>len, ez<PERSON>rt a rendszer figyelmen kívül hagyja. Használja helyette a következőt: `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "A `window.webkitStorageInfo` paramétert megszüntettük. Használja he<PERSON>ette a szabványosított `navigator.storage` paramétert."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Le vannak tiltva azok az alerőforrás-k<PERSON><PERSON>sek, amelyek U<PERSON>-jei beágyazott hitelesítési adatokat tartalmaznak (pl. `**********************/`)."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "A `DtlsSrtpKeyAgreement` korl<PERSON><PERSON><PERSON><PERSON>t eltávolítottuk. Olyan `false` értéket adott meg ennél a korlátozásnál, amelyet a rendszer úgy értelmez, hogy az eltávolított `SDES key negotiation` metódust szeretné használni. Ezt a funkciót eltávolítottuk. Használjon helyette egy olyan s<PERSON>, amely támogatja a `DTLS key negotiation` műveletet."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "A `DtlsSrtpKeyAgreement` korl<PERSON><PERSON><PERSON><PERSON>t eltávolítottuk. Olyan `true` értéket adott meg ennél a korl<PERSON><PERSON>z<PERSON>l, amely nem járt semmily<PERSON> hat<PERSON>, de a kód rendben tartása érdekében eltávolíthatja."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` felismerve. A `Session Description Protocol` e dialektusa többé nem támogatott. Használja helyette a következőt: `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "A `Plan B SDP semantics`, amelyet `{sdpSemantics:plan-b}` attribútummal rendelkez<PERSON> `RTCPeerConnection` összeállításá<PERSON>z hasz<PERSON>ak, a webes platformról véglegesen törölt `Session Description Protocol` egy r<PERSON>, nem sza<PERSON><PERSON> ve<PERSON>. Az `IS_FUCHSIA` használatával történő összeállításkor még rendelkezésre áll, de elképzeléseink szerint a lehető leghamarabb törölni fogjuk. Ne támaszkodjon rá. Az állapotot megtekintheti itt: https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Az `rtcpMuxPolicy` lehetőséget megszüntettük, és el fogjuk távolítani."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "A `SharedArrayBuffer` objektum megköveteli az eltérő eredetek elkülönítését. További részletek: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "A felhasználói aktiválás n<PERSON> `speechSynthesis.speak()` függvényt megszüntettük, és el fogjuk távolítani."}, "core/lib/deprecations-strings.js | title": {"message": "Megszüntetett funkció has<PERSON>"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "A `SharedArrayBuffer` használatának folytatásához a bővítményeknél engedélyezni kell az eltérő eredetek elkülönítését. További információ: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "A {PH1} szolgáltatóspecifikus. Haszná<PERSON><PERSON> he<PERSON>ette a szabványos {PH2} megoldást."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "Az `XMLHttpRequest` esetében az UTF-16 kódolást nem támogatja a válasz-JSON."}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "A fő szálon futó szin<PERSON>ron `XMLHttpRequest` függvényt megszüntettük, mert hátr<PERSON><PERSON> hatással van a végfelhasználói élményre. További információ: https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "A `supportsSession()` paramétert megszüntettük. Haszná<PERSON>ja he<PERSON>ette az `isSessionSupported()` függvényt, és ellenőrizze inkább a feloldott logikai értéket."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Fő szál akadályozásának időtartama"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Gyorsítótár-TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Le<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Időtar<PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Elem"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "<PERSON><PERSON><PERSON> el<PERSON>"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnName": {"message": "Név"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "K<PERSON><PERSON>sek"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON> t<PERSON>"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Kezdés ideje"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Eltöltött idő"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Átvitel<PERSON> mé<PERSON>"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potenci<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potenci<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Akár {wastedBytes, number, bytes} KiB megtakarítás"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 talált elem}other{# talált elem}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potenciálisan {wastedMs, number, milliseconds} ms megtakarí<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokumentum"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Első releváns le<PERSON>"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Betűtípus"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Az interakciótól a következő vizuális válaszig eltelt idő"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Magas"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Alacsony"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Közepes"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Első interakciótól számított max. potenciális k<PERSON>"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Média"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "Szkript"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} mp"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Harmadik féltől származó"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Összes"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Hiba történt az oldalbetöltés nyomának rögzítése során. Futtassa újra a Lighthouse szolgáltatást. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Időtúllépés a hibaelhárító protokoll kezdeti kapcsolatára való várakozás során."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "A Chrome nem gyűjtött képernyőképeket az oldal betöltése során. <PERSON><PERSON><PERSON><PERSON>, hogy van-e látható tartalom az oldalon, majd próbálja újra futtatni a Lighthouse szolgáltatást. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "A DNS-szerverek nem tudták értelmezni a megadott domaint."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "A szükséges {artifactName} begyűjtő hibába ütközött: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Belső Chrome-hiba történt. Indítsa újra a Chrome-ot, és próbálja újra futtatni a Lighthouse szolgáltatást."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "A kötelező {artifactName} nem futott le."}, "core/lib/lh-error.js | noFcp": {"message": "Az oldal nem jelenített meg semmilyen tartalmat. Gondoskodjon arról, hogy a böngészőablak legyen az előtérben a betöltés során, majd próbálja ú<PERSON>. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Az oldal nem jelení<PERSON>tt meg olyan tartalmat, amely <PERSON> vizuális tartalom betöltési idejének (LCP) minősül. Győződjön meg arról, hogy az oldal érvényes LCP-el<PERSON>et tartalmaz, majd pr<PERSON><PERSON><PERSON><PERSON>zon újból. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "A megadott oldal nem HTML formátumú ({mimeType} MIME-típusként jelent meg)."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ez a Chrome-verzió túl régi a(z) „{featureName}” támogatásához. A teljes eredmények megtekintéséhez használjon újabb verziót."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "A Lighthouse nem tudta megfelelően betölteni a kért oldalt. Győződjön meg arról, hogy a helyes URL-t teszteli, és a szerver megfelelően válaszol az összes kérelemre."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "A Lighthouse nem tudta megfelelően betölteni a kért URL-t, mert az oldal nem válaszol."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "A megadott URL-hez nem tartozik érvényes biztonsági tanúsítvány. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "A Chrome közbeiktatott képernyő megjelenítésével megakadályozta az oldal betöltését. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "A Lighthouse nem tudta megbízhatóan betölteni a kért oldalt. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e. (Részletek: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "A Lighthouse nem tudta megb<PERSON>zhatóan betölteni a kért oldalt. <PERSON><PERSON><PERSON><PERSON>, hogy a helyes URL-t teszteli-e, és hogy a szerver minden kérésre megfelelően reagál-e. (Állapotkód: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Túl sok<PERSON>ig tartott az oldal betöltése. Csökkentse az oldalbetöltési időt a jelentésben leírt lehetőségeket követve, majd futtassa újra a Lighthouse-t. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "A DevTools protokoll válaszára való várakozás túllépte a megengedett időt. (Módszer: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "A tartalom lekérése túllépte a megengedett időt"}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON>, a megadott URL érvénytelen."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Az oldal MIME-típusa XHTML: A Lighthouse nem támogatja kifejezetten ezt a dokumentumtípust."}, "core/user-flow.js | defaultFlowName": {"message": "Felhasz<PERSON><PERSON><PERSON><PERSON> útvonal ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Na<PERSON><PERSON><PERSON><PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Pillanatkép-jelentés ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Időtartam-jelentés ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Az összes jelentés"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kate<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Kisegítő lehetőségek"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Teljesítmény"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progresszív webes alkalmazás"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "Keresőoptimalizálás"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "A Lighthouse-folyamatjelentés értelmezése"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "A folyamatok értelmezése"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "A navigációs jelentések használatával a következőket teheti:…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "A pillanatkép-jelentések használatával a következőket teheti:…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "<PERSON>z időtartam-jelentések használatával a következőket teheti:…"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "pont<PERSON><PERSON><PERSON> kaphat a <PERSON>-teljesít<PERSON><PERSON><PERSON>;"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "mérheti az oldalbetöltési teljesítménnyel ka<PERSON>csolatos mutatókat (például a legnagyobb vizuális tartalomválaszt és a sebességindexet)."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "értékelheti a progresszív webes alkalmazások képességeit;"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "megtalálhatja a kisegítő lehetőségekkel kapcsolatos problémákat az egyoldalas alkalmazásokban vagy az összetettebb űrlapokon"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "értékelheti az interakciók mögötti rejtett menükkel és UI-elemekkel kapcsolatos bevált módszereket;"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "különböző interakciók esetében mérheti az elrendezésmozgást és a JavaScript végrehajtási idejét."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "teljesítményre vonatkozó lehetőségeket fedezhet fel a hosszú életű oldalak és az egyoldalas alkalmazásokkal kapcsolatos élmények javítása érdekében;"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> hat<PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} tájékoztat<PERSON> ellenőrz<PERSON>}other{{numInformative} tájékoztat<PERSON> ellenőrz<PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Oldalbetöltés"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "A navigációs jelent<PERSON>ek (pontosan úgy, ahogy az eredeti Lighthouse-jelentések is) az egyes oldalak betöltését elemzik."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navig<PERSON><PERSON><PERSON> jelent<PERSON>}other{{numNavigation} navig<PERSON><PERSON><PERSON> jelent<PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} teljesíthető ellenőrzés}other{{numPassableAudits} teljesíthető ellenőrzés}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} sikeres el<PERSON>}other{{numPassed} sikeres ellen<PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Átlagos"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Hiba"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "mentés;"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON>z oldal rögzített <PERSON>pota"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "A pillanatkép-jelentés az oldalt ad<PERSON>, jellemzően a felhasználói interakciókat követően elemzi."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Pillanatkép-jelentés"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} pillanatkép-jelent<PERSON>}other{{numSnapshot} pillanatkép-jelent<PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Összegzés"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Felhasználói interakciók"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Az időtartam-jelentések a tetszőleges, jellemzően felhasználó interakciókat tartalmazó időtartamokat elemzik."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Időtartam-jelentés"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} időtartam-jelentés}other{{numTimespan} időtartam-jelentés}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthouse felhasználóimunkafolyamat-jelentés"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Animált tartalmak esetében csökkentheti a processzorhasználatot az [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) seg<PERSON><PERSON>égével, amikor a tartalom nem látható a képernyőn."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Vegye fontolóra az összes [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) komponens WebP-formátumban való megjelenítését és megfelelő tartalékképek megadását más böngészők számára. [További információ](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "A képeket az [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) címkével adja meg, hogy automatikusan késleltetve töltődjenek be. [További információ](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "Ha [szer<PERSON><PERSON><PERSON>n jeleníti meg az AMP-elrendezéseket](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/), hasz<PERSON><PERSON><PERSON> olyan eszközöket, mint az [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Nézzen utána az [AMP dokumentációjában](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/), hogy az összes stílus támo<PERSON>ott-e."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Az [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) elem támogatja az [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) attribú<PERSON>ot annak mega<PERSON>, hogy mely képtartalmat használja a képernyő mérete alapján. [További információ](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Ha nagy listák megjelenítés<PERSON>, fontolja meg a Component Dev Kit (CDK) és a virtuális görgetés használatát. [További információ](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "A JavaScript-csomagok méretének minimálisra csökkentése érdekében alkalmazzon [útvonalszintű kódfelbontást](https://web.dev/route-level-code-splitting-in-angular/). Ezenkívül vegye fontolóra a tartalmak előzetes gyorsítótárba helyezésé<PERSON> az [Angular service worker](https://web.dev/precaching-with-the-angular-service-worker/) hasz<PERSON>latával."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Az Angular CLI használata esetén gondoskodjon arról, hogy a buildek létrehozása éles módban történjen. [További információ](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Az Angular CLI használata esetén vegye fel a forrás-hozzárendeléseket az éles buildbe, hogy meg tudja vizsgálni a csomagjait. [További információ](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "A navigáció gyorsítása érdekében töltse be előre az útvonalakat. [További információ](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Fontolja meg a Component Dev Kit (CDK) `BreakpointObserver` segédprogramjának használatát a képek töréspontjainak kezeléséhez. [További információ](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Fontolja meg a GIF-fájlok olyan szolgáltatóhoz való feltöltését, amely lehetővé teszi a fájlok HTML5-videóként történő beágyazását."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Adja meg a `@font-display` <PERSON><PERSON><PERSON><PERSON><PERSON>, amikor té<PERSON>j<PERSON><PERSON>z egyéni betűtípusokat határoz meg."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Vegye fontolóra a [WebP képformátumok konvertált képstílussal](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) való konfigurálását a webhelyen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Telepítsen olyan [Drupal-modult](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), amellyel késleltetve töltheti be a képeket. Egy ilyen modul javíthatja a teljesítményt a képernyőn kívül eső képek késleltetett betöltésével."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Érdemes lehet valamilyen (például az [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg)) modult használni a kritikus CSS- és JavaScript-elemek beágyazásához, illetve a tartalmak JavaScripten keresztüli, lehetőség szerint aszinkron módon történő betöltéséhez. Fontos, hogy az ilyen jellegű modulok által nyújtott optimalizációk működésképtelenné tehetik webhelyét, ezért valószínűleg módosítania kell majd a meglévő kódokat."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "A témák, a modulok és a szerver specifikációi mind befolyásolják a szerver válaszidejét. Érdemes lehet jobban optimalizált témát keresnie, megfelelő optimalizáló modult választania és/vagy nagyobb teljesítményű szerverre váltania. Alkalmazásszolgáltató szerverei a PHP-műveletkódok és a memória gyorsítótárazásával (pl. Redis vagy Memcached) csökkenthetik az adatbázisok lekérdezési idejét, illetve az alkalmazások optimalizált logikai hálózatával gyorsabban előkészíthetik az oldalakat."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "<PERSON>rde<PERSON> lehet [reszponzív képstílusokat](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) has<PERSON><PERSON><PERSON><PERSON>, melyek csökkentik az oldalon betöltött képek méretét. Ha a Views funkciót használja ahho<PERSON>, hogy egy oldalon több tartalmi elemet jelení<PERSON>en meg, fontolja meg az oldalak számozását, amivel korlátozhatja az egy adott oldalon megjelenő tartalmi elemek számát."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Győződjön meg róla, hogy beka<PERSON>olta a „CSS fájlok összegyűjtése” lehetőséget az „Adminisztráció » Beállítás » Fejlesztés” oldalon. Speciális összesítési lehetőségeket is beállíthat [további modulokon](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) keresztül, hogy összefűzéssel, minimalizálással és a CSS-stílusok tömörítésével felgyorsítsa webhelyét."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Győződjön meg róla, hogy beka<PERSON>olta a „JavaScript-fájlok összesítése” lehetőséget az „Adminisztráció » Beállítás » Fejlesztés” oldalon. Speciális összesítési lehetőségeket is beállíthat [további modulokon](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) keresztül, hogy összefűzéssel, minimalizálással és a JavaScript-tartalmak tömörítésével felgyorsítsa webhelyét."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Érdemes lehet eltávolítani a nem használt CSS-szab<PERSON>ly<PERSON>t, és csak a szükséges Drupal-könyvtárakat csatolni a releváns oldalhoz vagy összetevőhöz. További részleteket a [Drupal dokumentációjában](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) találhat. A felesleges CSS-t elhelyező csatolt könyvtárak azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős téma vagy modul a stíluslap URL-je alapján azon<PERSON>ítható, ha a CSS-összesítés ki van kapcsolva az Ön Drupal webhelyén. Keressen olyan témákat vagy modulokat, amelyeknek több stíluslapj<PERSON>ban is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a témák vagy modulok csak az oldalon ténylegesen használt stíluslapokat állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Érdemes lehet eltávolítani a nem használt JavaScript-tartalmakat, és csak a szükséges Drupal-függvénytárakat csatolni a releváns oldalhoz vagy összetevőhöz. További részleteket a [Drupal dokumentációjában](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) találhat. A felesleges JavaScriptet elhelyező csatolt függvénytárak azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős téma vagy modul a szkript URL-je alapján azonosítható, ha a JavaScript-összesítés ki van kapcsolva az Ön Drupal webhelyén. Keressen olyan témákat vagy modulokat, amelyeknek több szkriptjében is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a témák vagy modulok csak az oldalon ténylegesen használt szkripteket állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Állítsa be a „Böngésző és proxy gyorsítótár maximális <PERSON>lettart<PERSON>” lehetőséget az „Adminisztráció » Beállítás » Fejlesztés” oldalon. A Drupal gyorsítótárról és a jobb teljesítmény érdekében történő optimalizálásról [itt olvashat részleteket](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Érdemes lehet olyan [modult](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) hasz<PERSON>ln<PERSON>, amely minőségromlás nélkül, automatikusan optimalizálja és csökkenti az oldalon keresztül feltöltött képek méretét. Ezen kívül győződjön meg róla, hogy azokat a natív [reszponzív képstílusokat](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) hasz<PERSON><PERSON><PERSON> minden, a webhelyen megjelení<PERSON><PERSON> kép esetén, amely<PERSON> a <PERSON>upal (Drupal 8 vagy újabb) rendszeréből származnak."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Megadhat előcsatlakozási vagy a DNS előzetes lekérésére vonatko<PERSON>ó jelzéseket, ha telepít és beállít egy olyan [modult](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), amely lehetővé teszi felhasználói ügynöki jelzések használatát."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Győződjön meg róla, hogy azokat a natív [reszponzív képstílusokat](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) has<PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> a <PERSON> (Drupal 8 vagy ú<PERSON>bb) rendszeréből származnak. Használja a reszponzív képstílusokat, ha megtekintési módokon, megtekintéseken vagy a WYSIWYG-szerkesztővel feltöltött képeken keresztül jeleníti meg a képmezőket."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Az [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON><PERSON><PERSON>val bekapcsolhatja a(z) `Optimize Fonts` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével automatikusan bekapcsolhatja a(z) `font-display` CSS-függvényt, hogy a szöveg olvasható legyen a felhasználók számára, mialatt a webes betűtípusokat betölti a rendszer."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON>latával bekapcsolhatja a(z) `Next-Gen Formats` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével képeket WebP-formátumba konvertálhat."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) használatával bekapcsolhatja a(z) `<PERSON>zy Load Images` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével késleltetheti a képernyőn kívüli képek betöltését, amíg nincs rájuk szükség."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) használatával bekapcsolhatja a(z) `Critical CSS` és a(z) `Script Delay` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON><PERSON> segí<PERSON>égével késleltetheti a nem kritikus JS/CSS betöltését."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Az [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) használatával gyorsítótárazhatja tartalmait globá<PERSON> h<PERSON>, így csökkentheti az első bájt betöltéséhez szükséges időt."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON>latával bekapcsolhatja a(z) `Minify CSS` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével automatikusan kicsinyítheti a CSS-t a hálózaton továbbított adatok méretének csökkentése érdekében."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) használatával bekapcsolhatja a(z) `Minify Javascript` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével automatikusan kicsinyítheti a JS-t a hálózaton továbbított adatok méretének csökkentése érdekében."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Az [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON><PERSON><PERSON>val bekapcsolhatja a(z) `Remove Unused CSS` funkciót, amely seg<PERSON><PERSON>t a probléma megoldásában. A funkció azonosítja azokat a CSS-oszt<PERSON>t, amelyeket jelenleg a webhely egyes oldalai has<PERSON>ak, és eltávolít minden más osztályt a fájl kis méretének fenntartása érdekében."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) használatával bekapcsolhatja a(z) `Efficient Static Cache Policy` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével beállíthatja az ajánlott értékeket a gyorsítótárazási fejlécben a statikus elemeknél."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON>latával bekapcsolhatja a(z) `Next-Gen Formats` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével képeket WebP-formátumba konvertálhat."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Az [Ezoic Leap](https://pubdash.ezoic.com/speed) használatával bekapcsolhatja a(z) `Pre-Connect Origins` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével automatikusan hozzáadhat `preconnect` erőforrástippeket, hogy korai kapcsolatokat hozhasson létre harmadik felekhez tartozó fontos forrásokkal."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Az [E<PERSON> Leap](https://pubdash.ezoic.com/speed) szolgáltatás használatával bekapcsolhatja a(z) `Preload Fonts` és a(z) `Preload Background Images` funkci<PERSON>t, am<PERSON><PERSON> segítségével hozzáadhat `preload` linkeket olyan források prioritásos betöltése érdekében, melyeket az aktuális oldalbetöltés egyébként későbbre sorolt."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Az [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) hasz<PERSON>latával bekapcsolhatja a(z) `Resize Images` be<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, amely segítségével átméretezhet képeket az adott eszköznek megfelelő méretűre, csökkentve ezzel a hálózaton továbbított adatok méretét."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Fontolja meg a GIF-fájlok olyan szolgáltatóhoz való feltöltését, amely lehetővé teszi a fájlok HTML5-videóként történő beágyazását."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Érdemes olyan [bőv<PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) vagy szol<PERSON>t használnia, amely automatikusan az optimális formátumba konvertálja a feltöltött képeket."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Telepítsen [késleltetett betöltést biztosí<PERSON>ó <PERSON>-bővítményt](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), amely lehetőséget ad a képernyőn kívül eső képek késleltetett betöltésére; vagy v<PERSON><PERSON> olyan sablonra, amely rendelkezik ezzel a funkcióval. A Joomla 4.0-tól kezdődően az összes új kép [automatikusan](https://github.com/joomla/joomla-cms/pull/30748) megkapja a(z) `loading` attribútumot a core-tól."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Szá<PERSON>-bővítmény segíthet a [kritikus elemek beágyazásában](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) és a [kevésbé fontos források](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) késleltetésében. Fontos, hogy az ilyen jellegű beépülő modulok által nyújtott optimalizációk működésképtelenné tehetik a sablonok vagy más modulok funkcióit, ezért körültekintően tesztelni kell majd őket."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "A sablonok, a bővítmények és a szerver specifikációi mind befolyásolják a szerver válaszidejét. Érdemes lehet jobban optimalizált sablont keresnie, megfelelő optimalizáló bővítményt választania és/vagy nagyobb teljesítményű szerverre váltania."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Fontolja meg kivonatok megjelenítését a cikk-kategóriákban (pl. a read more linken keresztül), az oldalanként megjelenített bejegyzések számának csökkentését, a hosszabb bejegyzések több oldalra tördelését, valamint a hozzászólások késleltetett betöltését valamilyen beépülő modul segítségével."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Számos [Jo<PERSON><PERSON>-b<PERSON>v<PERSON>t<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) gyorsíthatja webhelyét a CSS-stílusok összefűzésével, minimalizálásával és tömörítésével. Egyes sablon<PERSON> is nyújtanak ilyen funkciókat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Számos [Jo<PERSON><PERSON>-bőv<PERSON>t<PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) gyorsíthatja webhelyét a szkriptek összefűzésével, minimalizálásával és tömörítésével. Egyes sab<PERSON> is nyújtanak ilyen funkciókat."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Fontolja meg a nem használt CSS-t betö<PERSON><PERSON> [Joomla-bőv<PERSON>t<PERSON>nyek](https://extensions.joomla.org/) lecserélését vagy számuk csökkentését. A felesleges CSS-t elhel<PERSON>z<PERSON> bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős téma vagy bővítmény a stíluslap URL-je alapján azonosítható. Keressen olyan bővítményeket, amelyeknek több stíluslapj<PERSON>ban is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt stíluslapokat állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Fontolja meg a nem használt JavaScriptet betöltő [Joomla-bőv<PERSON>tmények](https://extensions.joomla.org/) lecserélését vagy számuk csökkentését. A felesleges JavaScriptet elhelyező bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) eszköze is segíthet. A felelős bővítmény a szkript URL-je alapján azonosítható. Keressen olyan bővítményeket, amelyeknek több szkriptjében is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt szkripteket állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "A Joomla böngészős gyorsítótárazásáról [itt olvashat részleteket](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Fontolja meg olyan [képoptimal<PERSON><PERSON><PERSON><PERSON> bővít<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) has<PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON>el minőségromlás nélkül tömöríthetők a képek."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Ha tartalmaiban reszponzív képeket szeretne elhelyezni, <PERSON><PERSON><PERSON> lehet [reszponzív képekkel kapcsolatos beépülő modult](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) használni."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "A szövegtömörítést a Gzip oldaltömörítés bekapcsolásával engedélyezheti a Joomlában (Rendszer > Globális konfiguráció > Kiszolgáló)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Ha a JavaScript-tartalmakat nem teszi <PERSON>, fontolja meg a [baler](https://github.com/magento/baler) met<PERSON><PERSON> has<PERSON>t."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Tiltsa le a Magento beépí<PERSON>tt [JavaScript-csomagolási és -kicsiny<PERSON><PERSON>si](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) <PERSON><PERSON><PERSON>j<PERSON><PERSON>, és fontolja meg hely<PERSON> a [baler](https://github.com/magento/baler/)met<PERSON><PERSON>."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Adja meg a `@font-display` elemet, ha [egyedi bet<PERSON>t<PERSON>t határoz meg](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Ha ki szeretné használni az újabb képformátumok előnyeit, keressen harmadik felektől származó bővítményeket a [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) webhelyén."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "A webes platformok [késleltetett betöltés](https://web.dev/native-lazy-loading) funkciójának használata érdekében vegye fontolóra a termék- és katalógussablonok módosítását."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Hasz<PERSON><PERSON><PERSON> a Magento [Varnish-integr<PERSON><PERSON>j<PERSON><PERSON>](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Engedélyezze a „Minify CSS Files” (CSS-fájlok kicsinyítése) beállítást az üzlet fejlesztői beállításaiban. [További információ](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Hasz<PERSON><PERSON><PERSON> a [Tersert](https://www.npmjs.com/package/terser) a statikus tartalomtelepítés összes JavaScript-tartalmának minimalizálásához, és tiltsa le a beépített minimalizáló funkciót."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "<PERSON>iltsa le a Magento be<PERSON>í<PERSON>tt [JavaScript-csomagolási funkcióját](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Ha optimalizálni szeretné a képeket, keressen harmadik felektől származó bővítményeket a [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) webhelyén."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Úgy adhat hozzá előcsatlakozási vagy a DNS előzetes lehívására vonatkozó tippeket, hogy [módosítja valamelyik téma elrendezését](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>` címkéket [valamelyik téma elrendezésének módosításával](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html) adhat hozz<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Használja a(z) `next/image` összetevőt a(z) `<img>` helyett a képformátum automatikus optimalizálásához. [További információ](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Használja a(z) `next/image` összetevőt a(z) `<img>` a képek automatikus késleltetett betöltéséhez. [További információ](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "A(z) `next/image` összete<PERSON><PERSON> hasz<PERSON>lat<PERSON> a „prioritást” igaz értékre állíthatja az LCP-kép előtöltése érdekében. [További információ](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Használja a(z) `next/script` összetevőt a nem kritikus fontosságú, harmadik félhez tartozó szkriptek késleltetéséhez. [További információ](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Használja a(z) `next/image` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy mindig biztosítsa a képek megfelelő méretezését. [További információ](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Fontolja meg a(z) `PurgeCSS` beállítását a(z) `Next.js` konfigurációban a nem használt szabályok stíluslapokból való eltávolításához. [További információ](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Használja a(z) `Webpack Bundle Analyzer` eszközt nem használt JavaScript-kód <PERSON>z. [További információ](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)."}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Fontolja meg a(z) `Next.js Analytics` használatát az alkalmazás valós teljesítményének méréséhez. [További információ](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurálja a gyorsítótárazást állandó tartalmakhoz és `Server-side Rendered` (SSR) oldalakhoz. [További információ](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "A képminőség beállításához használja a(z) `next/image` összetevőt a(z) `<img>` helyett. [Tov<PERSON><PERSON>i információ](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Használja a(z) `next/image` összetevőt a megfelelő `sizes` beállításához. [További információ](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Engedélyezze a tömörítést a Next.js szerveren. [Tov<PERSON>bbi információ](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Használja a(z) `nuxt/image` <PERSON><PERSON><PERSON><PERSON>v<PERSON>t, és állítsa be a következőt: `format=\"webp\"`. [További információ](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Használja a(z) `nuxt/image` ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, és állíts<PERSON> be a következőt a képernyőn kívüli képeknél: `loading=\"lazy\"`. [Tov<PERSON><PERSON><PERSON> információ](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Használja a(z) `nuxt/image` összetevőt, és adja meg a következőt az LCP-képhez: `preload`. [További információ](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Használja a(z) `nuxt/image` <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, és adjon meg explicit `width` és `height` paramétert. [További információ](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Használja a(z) `nuxt/image` öss<PERSON><PERSON>v<PERSON>t, és állítsa be a megfelelő `quality` paramétert. [További információ](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Használja a(z) `nuxt/image` ö<PERSON><PERSON><PERSON><PERSON><PERSON>t, és állítsa be a megfelelő `sizes` paramétert. [További információ](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[C<PERSON><PERSON><PERSON><PERSON> le az animált GIF-eket videókra](https://web.dev/replace-gifs-with-videos/) a gyorsabban betöltődő weboldalak érdekében, és fontolja meg modern fájlformátumok (pl. [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) vagy [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)) has<PERSON><PERSON><PERSON><PERSON><PERSON>, amely<PERSON><PERSON> a tömörítési hatékonyságot több mint 30%-kal javíthatja a jelenlegi legmodernebb videokodekkel, a VP9-cel szemben."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Érdemes olyan [bővítményt](https://octobercms.com/plugins?search=image) v<PERSON>y s<PERSON>t hasz<PERSON>ln<PERSON>, amely automatikusan az optimális formátumba konvertálja a feltöltött képeket. A [WebP veszteségmentes képek](https://developers.google.com/speed/webp) mérete 26%-kal kisebb a PNG-knél, és 25-34%-kal kisebb a hasonló JPEG-képeknél azonos SSIM minőségi index mellett. Érdemes megfontolni az [AVIF](https://jakearchibald.com/2020/avif-has-landed/) következő generációs képformátum használatát is."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Fontolja meg [késleltetett képbetöltési bővítmény](https://octobercms.com/plugins?search=lazy) telepí<PERSON>sét, amely lehetőséget ad a képernyőn kívül eső képek késleltetett betöltésére, v<PERSON><PERSON> v<PERSON> olyan té<PERSON>, amely rendelkezik ezzel a funkcióval. Fontolja meg az [AMP-bővítmény](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) használatát is."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Számos bővítmény segíthet a [kritikus elemek beágyazásában](https://octobercms.com/plugins?search=css). Ezek a bővítmények elronthatják más bővítmények működését, ezért végezzen alapos tesztelést."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "A témák, a bővítmények és a szerver specifikációi mind befolyásolják a szerver válaszidejét. Érdemes lehet jobban optimalizált témát keresnie, megfelelő optimalizáló bővítményt választania és/vagy nagyobb teljesítményű szerverre váltania. Az October CMS a(z) [`Queues`](https://octobercms.com/docs/services/queues) használatát is lehetővé teszi a fejlesztőknek az időigényes feladatok (pl. e-mail-küldés) feldolgozásának késleltetéséhez. Ez drámaian felgyorsítja a webes kéréseket."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Fontolja meg kivonatok megjelenítését a bejegyzéslistákban (pl. a[z] `show more` gombbal), az oldalanként megjelenített bejegyzések számának csökkentését, a hosszabb bejegyzések több oldalra tördelését, valamint a hozzászólások késleltetett betöltését bővítmény segítségével."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Szá<PERSON> olya<PERSON> [b<PERSON>v<PERSON><PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=css) l<PERSON><PERSON>zik, amely felgyorsíthatja webhelyét a stílusok összefűzésével, minimalizálásával és tömörítésével. Felgyorsíthatja a fejlesztést, ha a minimalizálást buildfolyamat használatával előre elvégzi."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Szá<PERSON> olya<PERSON> [bőv<PERSON><PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=javascript) létezik, amely felgyorsíthatja webhelyét a szkriptek összefűzésével, minimalizálásával és tömörítésével. Felgyorsíthatja a fejlesztést, ha a minimalizálást buildfolyamat használatával előre elvégzi."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Fontolja meg a webhelyen nem használt CSS-t bet<PERSON><PERSON><PERSON> [bővítmények](https://octobercms.com/plugins) ellenőrzését. A szükségtelen CSS-t hozzáadó bővítmény azonosításához futtassa a Chrome fejlesztői eszközök [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) mutatóját. A felelős téma vagy bővítmény a stíluslap URL-je alapján azonosítható. Keressen olyan stíluslapokkal rendelkező bővítményt, amelyben sok a piros a kódlefedettségben. Ideális esetben a bővítmények csak a weboldalon ténylegesen használt stíluslapokat állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Fontolja meg annak a [bővítménynek](https://octobercms.com/plugins?search=javascript) az eltávolítását, amely nem használt JavaScriptet tölt be a weboldalon. A szükségtelen JavaScriptet hozzáadó bővítmény azonosításához futtassa a Chrome fejlesztői eszközök [kódlefedettség](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) mutatóját. A felelős téma vagy bővítmény a szkript URL-je alapján azonosítható. Keressen olyan szkriptekkel rendelkező bővítményt, amelyben sok a piros a kódlefedettségben. Ideális esetben a bővítmények csak a weboldalon ténylegesen használt szkripteket állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "További információ a [szükségtelen hálózati kérések megakadályozásáról a HTTP Cache segítségével](https://web.dev/http-cache/#caching-checklist). Számos [bővítmény](https://octobercms.com/plugins?search=Caching) használható a gyorsítótárazás felgyorsításához."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Fontolja meg olyan [képoptimal<PERSON><PERSON><PERSON><PERSON> bővítmé<PERSON>](https://octobercms.com/plugins?search=image) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, am<PERSON>el minőségromlás nélkül tömöríthetők a képek."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "A képeket közvetlenül a médiakezelőbe töltse fel, hogy biztosan minden szükséges képméret rendelkezésre álljon. Fontolja meg az [átméretez<PERSON><PERSON> szű<PERSON>](https://octobercms.com/docs/markup/filter-resize) vagy [képátméretező bővítmény](https://octobercms.com/plugins?search=image) használatát annak biztosításához, hogy az optimális méretű képeket használja."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "A szövegtömörítést a webszerver konfigurációjában engedélyezheti."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Ha sok ismétl<PERSON>d<PERSON> elemet jelenít meg az oldalon, font<PERSON>ja meg „windowing” függv<PERSON>yt<PERSON><PERSON> (pl. `react-window`) használatát annak érdekében, hogy csökkentse a létrehozott DOM-csomópontok számát. [Tov<PERSON><PERSON><PERSON> inform<PERSON>](https://web.dev/virtualize-long-lists-react-window/). Ezenkívül csökkentse a szükségtelen újrarenderelések számát a [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), a [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) vagy a [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) segítségével, valamint [hagyja ki az effektusokat](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), amíg bizonyos függőségek nem módosulnak, amennyiben az `Effect` hook használatával javítja a futásidejű teljesítményt."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Ha a React Routert használja, csökkentse minimálisra a `<Redirect>` komponens használatát az [útválasztási navigációban](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Ha szerveroldali renderelést alkalmaz bármilyen React-összetevőnél, fontolja meg a következők valamelyikének hasz<PERSON>t: `renderToPipeableStream()` vagy `renderToStaticNodeStream()`. <PERSON><PERSON><PERSON><PERSON> engedélyezheti az ügyfél sz<PERSON>, hogy a markupot ne egyben, hanem részletekben kapja meg és hidratálja. [Tov<PERSON>bbi információ](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Ha a buildrendszere automatikusan kicsinyíti a CSS-fájlokat, akkor ügyeljen arra, hogy az alkalmazás éles buildjét telepítse. Ezt a React Developer Tools bővítménnyel ellenőrizheti. [További információ](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Ha a buildrendszere automatikusan kicsinyíti a JS-fájlokat, akkor ügyeljen arra, hogy az alkalmazás éles buildjét telepítse. Ezt a React Developer Tools bővítménnyel ellenőrizheti. [További információ](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Ha nem szerveroldali renderelést alkalmaz, [ossza fel a JavaScript-csomagokat](https://web.dev/code-splitting-suspense/) a `React.lazy()` függvény használatával. Máskülönben harmadik fél könyvtá<PERSON>ának (pl. [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)) használatával bontsa fel a kódot."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Használja a React DevTools Profilert, amely a Profiler API segítségével méri az összetevők megjelenítési teljesítményét. [További információ](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Fontolja meg a GIF-fájlok olyan szolgáltatóhoz való feltöltését, amely lehetővé teszi a fájlok HTML5-videóként történő beágyazását."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Vegye fontolóra a [Teljesítménylabor](https://wordpress.org/plugins/performance-lab/) beépülő modul használatát a feltöltött JPEG-képek WebP formátumba való automatikus konvertálásához (ahol ez támogatott)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Telepítsen [lusta betöltést biztosító WordPress-bővítményt](https://wordpress.org/plugins/search/lazy+load/), amely lehetőséget ad a képernyőn kívül eső képek késleltetett betöltésére – vagy v<PERSON> olyan té<PERSON>, amely rendelkezik ezzel a funkcióval. Fontolja meg az [AMP-bővítmény](https://wordpress.org/plugins/amp/) használatát is."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Számos WordPress-bővítmény segíthet a [kritikus elemek beágyazásában](https://wordpress.org/plugins/search/critical+css/) és a [kevésbé fontos források](https://wordpress.org/plugins/search/defer+css+javascript/) késleltetésében. Fontos, hogy az ilyen jellegű bővítmények által nyújtott optimalizációk működésképtelenné tehetik a témák vagy más bővítmények funkcióit, ezért valószínűleg módosítania kell majd a meglévő kódot."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "A témák, a bővítmények és a szerver specifikációi mind befolyásolják a szerver válaszidejét. Érdemes lehet jobban optimalizált témát kere<PERSON>nie, megfelelő optimalizáló bővítményt választania és/vagy nagyobb teljesítményű szerverre váltania."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Fontolja meg kivonatok megjelenítését a bejegyzéslistákban (pl. a more címkével), az oldalanként megjelenített bejegyzések számának csökkentését, a hosszabb bejegyzések több oldalra tördelését, valamint a hozzászólások lusta betöltését bővítmény segítségével."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Számos [WordPress-bővítmény](https://wordpress.org/plugins/search/minify+css/) gyorsíthat webhelyén a stíluslapok egyesítésével, minimalizálásával és tömörítésével. Ha lehetséges, a minimalizálást érdemes buildfolyamat használatával előre elvégezni."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Számos [WordPress-bővítmény](https://wordpress.org/plugins/search/minify+javascript/) gyorsíthat webhelyén a szkriptek egyesítésével, minimalizálásával és tömörítésével. Ha lehetséges, a minimalizálást érdemes buildfolyamat használatával előre elvégezni."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Fontolja meg a nem használt CSS-t betölt<PERSON> [WordPress-bővítmények](https://wordpress.org/plugins/) lecserélését, vagy számuk csökkentését. A felesleges CSS-t elhelyez<PERSON> bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developer.chrome.com/docs/devtools/coverage/) eszköze is segíthet. A felelős téma vagy bővítmény a stíluslap URL-je alapján azonosítható. Keress<PERSON> olyan bővítményeket, amelyeknek több stíluslapj<PERSON>ban is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt stíluslapokat állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Fontolja meg a nem használt JavaScriptet betöltő [WordPress-bővítmények](https://wordpress.org/plugins/) lecserélését vagy számuk csökkentését. A felesleges JavaScriptet elhelyező bővítmények azonosításában a Chrome DevTools [kódlefedettség](https://developer.chrome.com/docs/devtools/coverage/) eszköze is segíthet. A felelős téma vagy bővítmény a szkript URL-je alapján azonosítható. Keressen olyan bővítményeket, amelyeknek több szkriptjében is sok piros szín szerepel a kódlefedettségi listán. Ideális esetben a bővítmények csak az oldalon ténylegesen használt szkripteket állítják sorba."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Tov<PERSON><PERSON>i információ a [WordPress böngészős gyorsítótárazásáról](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Fontolja meg olyan [képoptimalizáló WordPress-bővítmény](https://wordpress.org/plugins/search/optimize+images/) has<PERSON><PERSON><PERSON><PERSON><PERSON>, mellyel minőségromlás nélkül tömöríthetők a képek."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "A képeket közvetlenül a [Media Library](https://wordpress.org/support/article/media-library-screen/) fel<PERSON><PERSON><PERSON> töltse fel, hogy biztosan minden szükséges képméret rendelkezésre álljon, e<PERSON><PERSON><PERSON> pedig innen sz<PERSON>rja be <PERSON>, vagy használja az Image Widgetet, hogy biztosan az optimális képméreteket alkalmazza (a reszponzív töréspontok esetében is) `Full Size` képeket csak akkor használjon, ha méretük megfelel a felhasználás módjának. [További információ](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "A szövegtömörítést a webszerver konfigurációjában engedélyezheti."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "A képek WebP-formátumba való konvertálásához engedélyezze az „Imagify” paramétert a „WP Rocket” képoptimalizálási lapján."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "A javaslat javításához engedélyezze a [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) funkciót a WP Rocket eszközben. Ez a funkció késlelteti a képek betöltését addig, amíg a látogató le nem görget az oldalon, és ténylegesen meg nem tekinti őket."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "A javaslat kezeléséhez engedélyezze a [Távolítsa el a nem használt CSS-kódot](https://docs.wp-rocket.me/article/1529-remove-unused-css) és a [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (JavaScript betöltése késleltetve) funkciót a „WP Rocket” eszközben. Ezek a funkciók ennek megfelelően úgy optimalizálják a CSS- és JavaScript-fájlokat, hogy azok nem akadályozzák az oldal megjelenítését."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "A probléma megoldásához engedélyezze a [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (CSS-fájlok minimalizálása) beállítást a „WP Rocket” eszközben. A webhely CSS-fájljaiban található szóközöket és megjegyzéseket a rendszer eltávolítja, hogy a fájl mérete kisebb, a letöltése pedig gyorsabb legyen."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "A probléma megoldásához engedélyezze a [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (JavaScript-fájlok minimalizálás) beállítást a „WP Rocket” eszközben. Az üres területeket és megjegyzéseket a rendszer eltávolítja majd a JavaScript-fájlokból, ho<PERSON> mé<PERSON><PERSON><PERSON> kisebb, betöltésük pedig gyorsabb legyen."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "A probléma megoldásához engedélyezze a [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (<PERSON><PERSON> hasz<PERSON>lt CSS eltávolítása) beállítást a „WP Rocket” eszközben. A funkció használatával csökkenti az oldal méretét az összes, nem használt CSS- és stíluslap eltávolításával, miközben az egyes oldalaknál csak a használt CSS-t tartja meg."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "A probléma megoldásához kapcsolja be a [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (JavaScript-végrehajtás késleltetése) beállítást a „WP Rocket” eszközben. A beállítással javítja az oldal betöltését azáltal, hogy késlelteti a szkriptek végrehajtását a felhasználói beavatkozásig. Ha a webhely rendelkezik iframe-elemekkel, akkor használhatja a WP Rocket [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (iframe-ekhez és videókhoz készített LazyLoad) megoldását, valamint a [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (YouTube-iframe hely<PERSON><PERSON><PERSON><PERSON> el<PERSON> k<PERSON>) megoldást is."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "A képek tömörítéséhez engedélyezze az „Imagify” paramétert a „WP Rocket” képoptimalizálási lapján, majd futtassa a Tömeges optimalizálás folyamatot."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "A „WP Rocket” eszköz [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (DNS-kérelmek előzetes lekérése) funkciójával hozz<PERSON>adhatja a „dns-preFetch” para<PERSON><PERSON><PERSON>, és felgyorsíthatja a külső domainekkel fenntartott kapcsolatot. A „WP Rocket” emellett automatikusan hozzáadja a „preconnect” szót a [Google Fonts-domainhez](https://docs.wp-rocket.me/article/1312-optimize-google-fonts), illetve az [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (CDN engedélyezése) funkción keresztül hozzáadott minden CNAME paramétert."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "A betűtípusokkal kapcsolatos probléma megoldásához engedélyezze a [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (<PERSON><PERSON> CSS eltávolítása) beállítást a „WP Rocket” eszközben. Webhelye kritikus fontosságú betűtípusai előre betöltődnek prioritással."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Számológép megtekintése"}, "report/renderer/report-utils.js | collapseView": {"message": "Nézet összecsukása"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Kezdeti navigáció"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Kritikus elérési út maximális várakozási ideje:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "JSON másolása"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "<PERSON><PERSON><PERSON><PERSON> téma be-/kikapcsolása"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Teljes jelentés nyomtatása"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Összefoglalás nyomtatása"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Mentés HTML-ként"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Mentés JSON-ként"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Megnyitás a megtekintőben"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON>!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Jelentési hiba: nincs ellenőrzési információ"}, "report/renderer/report-utils.js | expandView": {"message": "Nézet kibontása"}, "report/renderer/report-utils.js | footerIssue": {"message": "Probléma <PERSON>"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboradatok"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "Az aktuá<PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/)-elemzése emulált mobilhálózaton. Az értékek becsültek és változhatnak."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> elemzendő elemek"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Lehetőség"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> megtakarí<PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Sikeresen teljesített ellenőrzések"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Első oldalbetöltés"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Egy<PERSON>i <PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-verzió"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Korlátlan CPU-/memóriateljesítmény"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU-korlátozás"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "Eszköz"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Hálózatkorlátozás"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Felhasználói ügynök (hálózat)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Egyetlen oldalbetöltés"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ez az adat egyetlen oldalbetöltésből származik, nem pedig számos munkamenet összesítéséből."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Lassú 4G emulálása"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Ismeretlen"}, "report/renderer/report-utils.js | show": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "A következővel kapcsolatos naplóbejegyzések megjelenítése:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "K<PERSON>dr<PERSON>zlet összecsukása"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kibont<PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Harmadik féltől származó források megjelenítése"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "A környezettől származik"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "A Lighthouse-futtatást befolyásoló problémák fordultak elő:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Az értékek becsültek és változhatnak. A [teljesítménypontszám kiszámítása](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) közvetlenül e mutatók alapján történik."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Eredeti nyom megtekintése"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "<PERSON><PERSON>m me<PERSON>ek<PERSON>"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Fastruktúra megtekintése"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Átment az ellenőrzéseken – figyelmeztetésekkel"}, "report/renderer/report-utils.js | warningHeader": {"message": "Figyelmeztetések: "}, "treemap/app/src/util.js | allLabel": {"message": "Összes"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "Minden szkript"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Lefedettség"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Ismétlődő modulok"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Forrásf<PERSON><PERSON><PERSON> mé<PERSON>e b<PERSON>"}, "treemap/app/src/util.js | tableColumnName": {"message": "Név"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Táblázat <PERSON>/megjelenítése"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "<PERSON><PERSON> nem has<PERSON> b<PERSON>"}}