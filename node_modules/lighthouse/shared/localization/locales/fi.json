{"core/audits/accessibility/accesskeys.js | description": {"message": "Pääsyavaimien avulla käyttäjät voivat nopeasti kohdistaa tiettyyn sivun osaan. <PERSON><PERSON> sivulla siirtyminen onnistuu, jokaisen pääsyavaimen on oltava yksilöllinen. [Lue lisää pääsyavaimista](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "`[accesskey]`-ar<PERSON>t eivät ole yksilöllisiä"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]`-ar<PERSON>t ovat yksilöllisiä."}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON>-`role` tukee tiettyä `aria-*`-mä<PERSON>ritteiden osaa. Vastaavuusjärjestyksen sekoittaminen mitätöi `aria-*`-määritteet. [Lue lisää ARIA-määritteiden ja niiden roolien yhdistämisestä](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "`[aria-*]`-määritteet eivät vastaa rooleja"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "`[aria-*]`-m<PERSON><PERSON><PERSON><PERSON>t vastaavat roolejaan"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Jos elementin nimi ei ole est<PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanovat sen kohdalla geneerisen nimen, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [<PERSON><PERSON>, miten voit helpottaa komentoelementtien käyttöä](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementeillä (`button`, `link` ja `menuitem`) ei ole esteettömiä nimiä"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementeillä (`button`, `link` ja `menuitem`) on esteettömät nimet"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Näytönlukuohjelmat ja muut avustavat teknologiat toimivat arvaama<PERSON>masti, kun `aria-hidden=\"true\"` asete<PERSON>an dokumentin kohdassa `<body>`. [<PERSON><PERSON>, miten `aria-hidden` vaikuttaa dokumentin tekstiosaan](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "`[aria-hidden=\"true\"]` on doku<PERSON><PERSON> koh<PERSON> `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "`[aria-hidden=\"true\"]` ei ole dokumentin koh<PERSON> `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "<PERSON><PERSON> `[aria-hidden=\"true\"]`-element<PERSON><PERSON> on tarkennettavia alaosia, näytönlukuohjelmat ja muut avustavat teknologiat eivät löydä niitä. [<PERSON><PERSON>, miten `aria-hidden` vaikuttaa kohdistettaviin elementteihin](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "`[aria-hidden=\"true\"]`-<PERSON><PERSON><PERSON><PERSON> on tarkennettavia alaosia"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "`[aria-hidden=\"true\"]`-elementeissä ei ole tarkennettavia alaosia"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "<PERSON><PERSON>n nimi ei ole est<PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [Lue lisää syöttökenttien tunnisteista](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ARIA-syötekenttien nimet eivät ole esteettömiä"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ARIA-syötekenttien nimet ovat esteettömiä"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Jos elementin nimi ei ole est<PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [<PERSON><PERSON>, miten voit nimetä `meter`-elementtejä](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "ARIA-elementeillä (`meter`) ei ole esteettömiä nimiä"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "ARIA-elementeillä (`meter`) on esteettömät nimet"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "<PERSON><PERSON> element<PERSON> (`progressbar`) nimi ei ole <PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON><PERSON>t sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [<PERSON><PERSON> lisä<PERSON> `progressbar`-elementtien nimeämisestä](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "ARIA-elementeillä (`progressbar`) ei ole esteettömiä nimiä"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "ARIA-elementeillä (`progressbar`) on esteettömät nimet"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON><PERSON> ARIA-<PERSON><PERSON><PERSON><PERSON> on pakollisia määritteitä, jotka kuvaavat elementin tilaa näytönlukuohjelmille. [Lue lisää rooleista ja pakollisista määritteistä](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]`-<PERSON><PERSON><PERSON><PERSON> ei ole kaikkia vaadittuja `[aria-*]`-mä<PERSON>ritteitä"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]`-<PERSON><PERSON><PERSON><PERSON> on kaikki vaaditut `[aria-*]`-m<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Voidakseen suorittaa esteettömyyteen liittyvät toiminnot joidenkin alatason ARIA-roolien on kuuluttava tiettyihin ylätason rooleihin. [Lue lisää rooleista ja pakollisista alatason elementeistä](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "ARIAn `[role]` sisältämät elementit edellyttävät alatasoilta tiettyä elementtiä (`[role]`), mutta se puuttuu osalta tai kaikilta alatasoilta."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "ARIAn `[role]` sisältämät elementit edellyttävät alatasoilta tiettyä elementtiä (`[role]`), joka on kaikilla alatasoilla."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Voidakseen suorittaa esteettömyyteen liittyvät toiminnot joid<PERSON>kin alatason ARIA-roolien on kuuluttava tiettyihin ylätason rooleihin. [Lue lisää ARIA-rooleista ja pakollisesta ylätason elementistä](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]`-elementit eivät sisälly niiden pakolliseen ylätason elementtiin"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]`-elementit sisältyvät niiden pakolliseen ylätason elementtiin"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Voidakseen suorittaa esteettömyyteen liittyvät toiminnot ARIA-r<PERSON><PERSON><PERSON> on oltava kelvolliset arvot. [Lue lisää kelvollisista ARIA-rooleista](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "`[role]`-a<PERSON><PERSON><PERSON> eivät ole kelvollisia"}, "core/audits/accessibility/aria-roles.js | title": {"message": "`[role]`-a<PERSON><PERSON><PERSON> ovat k<PERSON>"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "<PERSON><PERSON> pä<PERSON>/pois-kentän nimi ei ole est<PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [Lue lisää päälle/pois-kentistä](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ARIAn päälle/pois-kenttien nimet eivät ole esteettömiä"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ARIAn päälle/pois-kenttien nimet ovat esteettömiä"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Jos elementin vihjetekstin nimi ei o<PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON><PERSON> sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [<PERSON><PERSON>, miten voit nimetä `tooltip`-elementtejä](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "ARIA-elementeillä (`tooltip`) ei ole esteettömiä nimiä"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "ARIA-elementeillä (`tooltip`) on esteettömät nimet"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "<PERSON><PERSON> element<PERSON> (`treeitem`) nimi ei ole <PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON><PERSON>t sanovat sen kohdalla geneerisen ni<PERSON>, jolloin näytönlukuohjelmien käyttäjät eivät voi käyttää sitä. [Lue lisää `treeitem`-elementtien merkitsemisestä](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "ARIA-elementeillä (`treeitem`) ei ole esteettömiä nimiä"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "ARIA-elementeillä (`treeitem`) on esteettömät nimet"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Avustustekniikat (kuten näytönluk<PERSON>hjelmat) eivät voi tulkita ARIA-määritteitä, jois<PERSON> on virheelliset arvot. [Lue lisää kelvollisista ARIA-määritteiden arvoista](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "`[aria-*]`-m<PERSON><PERSON>ritteiden arvot eivät ole kelvollisia"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "`[aria-*]`-m<PERSON><PERSON><PERSON>teiden arvot ovat kelvollisia"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Avustustekniikat (kuten näytönluk<PERSON>h<PERSON>lmat) eivät voi tulkita ARIA-määritteitä, joil<PERSON> on virheelliset nimet. [Lue lisää kelvollisista ARIA-määritteistä](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "`[aria-*]`-määritteet eivät ole kelvollisia tai sisältävät kirjoitusvirheitä"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "`[aria-*]`-m<PERSON><PERSON>rit<PERSON>t ovat kelvollisia eivätkä sisällä kirjoitusvirheitä"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Hylätyt elementit"}, "core/audits/accessibility/button-name.js | description": {"message": "Kun painikkeen nimi ei ole est<PERSON>, n<PERSON><PERSON>ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>t ilmoittavat sen painikke<PERSON>, jolloin se on hyödytön näytönlukuohjelmia tarvitseville käyttäjille. [<PERSON><PERSON>, miten painikkeista voi tehdä saavutettavampia](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "Painikkeiden nimet eivät ole esteettömiä"}, "core/audits/accessibility/button-name.js | title": {"message": "Painikkeiden nimet ovat esteettömiä"}, "core/audits/accessibility/bypass.js | description": {"message": "Tapojen lisääminen toistuvan sisällön ohittamiseen auttaa näppäimistön käyttäjiä siirtymään sivulla tehokkaammin. [Lue lisää ohituksista](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Sivu ei sisällä ots<PERSON>, ohituslinkkiä tai maamerkin aluetta"}, "core/audits/accessibility/bypass.js | title": {"message": "Sivu sisältää otsikon, ohituslinkin tai maamerkin alueen"}, "core/audits/accessibility/color-contrast.js | description": {"message": "<PERSON><PERSON><PERSON> k<PERSON><PERSON> on monelle vaikea tai mahdoton lukea. [Lue lisää riittävän värikontrastin lisäämisestä](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "Taustan ja etualan värien kont<PERSON>uhde ei ole riittävä."}, "core/audits/accessibility/color-contrast.js | title": {"message": "Taustan ja etualan värien k<PERSON> on riittävä"}, "core/audits/accessibility/definition-list.js | description": {"message": "Kun määritelmäluetteloita ei ole merkitty kunno<PERSON>, näytönlukuohjelmien tuottama sisältö voi olla sekavaa tai epätarkkaa. [<PERSON><PERSON>, miten määritelmäluettelot jäsennetään o<PERSON>](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>`-elementit eivät sisällä vain oikein jär<PERSON> `<dt>`- ja `<dd>`-ryhmi<PERSON> ja `<script>`-, `<template>`- tai `<div>`-elementtejä."}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>`-elementit sisältävät vain oikein jär<PERSON> `<dt>`- ja `<dd>`-ryhmi<PERSON> ja `<script>`-, `<template>` tai `<div>`-elementtejä"}, "core/audits/accessibility/dlitem.js | description": {"message": "Määritelm<PERSON> luettelo<PERSON>dat (`<dt>` ja `<dd>`) on yhdistettävä ylätason `<dl>`-<PERSON><PERSON><PERSON>, jotta n<PERSON>lukuohjelmat voivat varmasti lukea ne oikein. [<PERSON><PERSON>, miten määritelmäluettelot jäsennetään oikein](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Määritelmien luettelo<PERSON>htia ei ole y<PERSON>tty `<dl>`-elementeillä"}, "core/audits/accessibility/dlitem.js | title": {"message": "Määritelmien l<PERSON>lo<PERSON>dat on yhdistetty `<dl>`-elementeillä"}, "core/audits/accessibility/document-title.js | description": {"message": "<PERSON>mi antaa n<PERSON>uo<PERSON>jelmaa k<PERSON>ä<PERSON> yleiskuvan sivusta, ja hakukoneen käyttäjille nimi on tärkeä oleellisten sivujen löytämiseen hakutuloksista. [Lue lisää dokumenttien otsikoista](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentissa ei ole `<title>`-element<PERSON><PERSON>"}, "core/audits/accessibility/document-title.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> on `<title>`-element<PERSON>"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Tarkentamista tukevilla elementeillä on oltava yksilöllinen `id`, jotta avustava teknologia havaitsee ne. [<PERSON><PERSON>, miten voit korjata päällekkäisiä `id`-merkintöjä](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tarkennettavien elementtien `[id]`-määritteet eivät ole yksilöllisiä"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, tarkennettavien elementtien `[id]`-määritteet ovat yksilöllisiä"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ARIA-tunnisteen on oltava y<PERSON><PERSON><PERSON><PERSON><PERSON>, jotta avustavat teknologiat eivät jätä muita esiintymiä huomioimatta. [<PERSON><PERSON>, miten voit korjata päällekkäisiä ARIA-tunnuksia](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ARIA-tunnist<PERSON>t eivät ole yksilöllisiä"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ARIA-tun<PERSON><PERSON>t ovat y<PERSON>il<PERSON>llisiä"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "<PERSON><PERSON> on useita tunnisteit<PERSON>, näytön<PERSON><PERSON>h<PERSON>lmat ja muut avustavat teknologiat saattavat viitata niihin hämmentävästi käyttäen ensimmäistä, viimeistä tai jokaista tunnistetta. [Lue lisää lomaketunnisteiden käyttämisestä](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Lomakekentillä on useita tunnisteita"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Lomakekentillä ei ole useita tunnisteita"}, "core/audits/accessibility/frame-title.js | description": {"message": "Näytön<PERSON><PERSON><PERSON><PERSON><PERSON> käyttäj<PERSON> saavat tietää kehysten sisällöt vain kehysten nimien avulla. [Lue lisää kehysten nimistä](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "`<frame>`- tai `<iframe>`-element<PERSON><PERSON><PERSON> ei ole nimeä"}, "core/audits/accessibility/frame-title.js | title": {"message": "`<frame>`- tai `<iframe>`-element<PERSON><PERSON><PERSON> on nimi"}, "core/audits/accessibility/heading-order.js | description": {"message": "Loogisesti järjestetyt ja kaikki tasot käsittävät otsikot kertovat sivun semanttisesta rakenteesta, jolloin sen selaaminen ja ymmärtäminen avustavilla teknologioilla on helpompaa. [Lue lisää otsikkojärjestyksestä](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Otsikkoelementit eivät ole laskevassa järjestyksessä"}, "core/audits/accessibility/heading-order.js | title": {"message": "Otsikkoelementit ovat laskevassa järjestyksessä"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Jos sivulla ei ole `lang`-m<PERSON><PERSON><PERSON><PERSON><PERSON>, n<PERSON><PERSON>ön<PERSON>uo<PERSON><PERSON>lma arvioi kieleksi oletuskielen, jonka käyttäjä valitsi ottaessaan näytönlukuoh<PERSON>lman käyttöön. Jos oletuskieli ei ole käytössä sivulla, näytönlukuohjelma voi ilmoittaa sivun tekstin vä<PERSON>rin. [Lue lisää `lang`‐määritteestä](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-element<PERSON><PERSON> ei ole `[lang]`-määritettä"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-<PERSON><PERSON><PERSON> on `[lang]`-mä<PERSON>rite"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Kelvo<PERSON>en [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ‑määritteen ilmoittaminen elementeille auttaa näytönlukuohjelmaa kertomaan tekstin o<PERSON>. [Lue lisää `lang`‐määritteen käyttämisestä](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-elementin `[lang]`-m<PERSON><PERSON><PERSON><PERSON> arvo ei ole kelvollinen"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-elementin `[lang]`-m<PERSON><PERSON><PERSON><PERSON> arvo on kelvollinen"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informatiivisilla elementeillä pitäisi olla lyhyt ja kuvaileva vaihtoehtoinen teksti. Koristeelliset elementit voidaan ohittaa tyhjällä Alt-määritteellä. [Lue lisää `alt`‐määritteestä](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Kuvaelementeillä ei ole `[alt]`-määritteitä"}, "core/audits/accessibility/image-alt.js | title": {"message": "Kuvaelementeillä on `[alt]`-määritteet"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Kun `<input>`-pain<PERSON><PERSON><PERSON> käytetään kuva<PERSON>, vaihtoehtoisen tekstin lisääminen voi auttaa näytönlukuoh<PERSON>lman käyttäjiä ymmärtämään painikkeen tarkoituksen. [Lue lisää kuvan vaihtoehtoisesta tekstistä](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "`<input type=\"image\">`-elementeissä ei ole `[alt]`-tekstiä"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "`<input type=\"image\">`-<PERSON><PERSON><PERSON><PERSON> on `[alt]`-teksti"}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ett<PERSON> avustustekniikat (kuten näytönluk<PERSON>hjelmat) ilmoittavat lomakkeiden oh<PERSON>imista oikein. [Lue lisää lomake-elementtien tunnisteista](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Lomakkeiden elementeillä ei ole niihin liittyviä tunnisteita"}, "core/audits/accessibility/label.js | title": {"message": "Lomake-elementeillä on niihin liittyvät tunnisteet"}, "core/audits/accessibility/link-name.js | description": {"message": "Linkkiteksti (ja vaihtoehtoinen teksti kuvia varten, kun niitä käytetään linkkeinä), joka on erott<PERSON><PERSON>, yks<PERSON><PERSON><PERSON><PERSON> ja tarkennetta<PERSON>, parantaa n<PERSON>ytönlukuohjelmaa käyttävien navigointikokemusta. [<PERSON><PERSON>, miten voit mahdollistaa linkkien käytön](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Linkkien nimet eivät ole helposti erottuvia"}, "core/audits/accessibility/link-name.js | title": {"message": "Linkkien nimet ovat helposti erottuvia"}, "core/audits/accessibility/list.js | description": {"message": "Näytönlukuohjelmat ilmoittavat luettelot tietyillä tavoilla. Kelvollinen luettelorakenne tukee näytönlukuohjelman tuottamaa sisältöä. [Lue lisää oikeasta luettelorakenteesta](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Lu<PERSON>lot eivät sisällä ainoastaan `<li>`-elementtejä ja skriptiä tukevia elementtejä (`<script>` ja `<template>`)"}, "core/audits/accessibility/list.js | title": {"message": "<PERSON><PERSON><PERSON> sisältävät ainoastaan `<li>`-elementtejä ja skriptiä tukevia elementtejä (`<script>` ja `<template>`)"}, "core/audits/accessibility/listitem.js | description": {"message": "Näytönlukuohjelmat edellyttävät, että luettelo<PERSON>hdat (`<li>`) sisältyvät ylätason elementteihin `<ul>`, `<ol>` tai `<menu>`, jotta ne voidaan ilmoittaa oikein. [Lue lisää oikeasta luettelorakenteesta](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (`<li>`) eivät sisälly yl<PERSON> `<ul>`-, `<ol>`- tai `<menu>`-element<PERSON><PERSON>."}, "core/audits/accessibility/listitem.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (`<li>`) sisältyvät ylätason `<ul>`-, `<ol>`- tai `<menu>`-element<PERSON><PERSON>."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Käyttäjät eivät odota sivun päivittyvän automaattisesti, ja päivittäminen siirtää kohdistuksen takaisin sivun yläreunaan. Tämä voi tehdä käytöstä turhauttavaa tai sekavaa. [Lue lisää refresh-metatagin käyttämisestä](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokumentissa on käytössä `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokumentti ei käytä `<meta http-equiv=\"refresh\">`-tagia"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Zoomauksen poistaminen käytöstä aiheuttaa ongelmia heikkonäköisille käyttäjille, jotka tarvitsevat näytön suurennusta nähdäkseen verkkosivun sisällön kunnolla. [Lue lisää viewport-metatagin käyttämisestä](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` on käytössä `<meta name=\"viewport\">`-elementissä tai `[maximum-scale]`-määrite on pienempi kuin 5"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` ei ole käyt<PERSON> `<meta name=\"viewport\">`-elementissä, ja `[maximum-scale]`-määrite on vähintään 5"}, "core/audits/accessibility/object-alt.js | description": {"message": "Näytönlukuohjelmat eivät voi kääntää sisältöä, joka ei ole tekstiä. Vaihtoehtoisen tekstin lisääminen `<object>`-elementteihin auttaa näytönlukuohjelmia esittämään sisällön merkityksen käyttäjille. [Lue lisää `object`-elementin vaihtoehtoisesta tekstistä](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>`-elementeissä ei ole vaihtoehtoista tekstiä."}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>`-<PERSON><PERSON><PERSON><PERSON> on vaihtoehtoista tekstiä."}, "core/audits/accessibility/tabindex.js | description": {"message": "Navigointijärjestys on eksplisiittinen, jos arvo on suurempi kuin 0. <PERSON><PERSON><PERSON> on teknisesti käypä, se tekee usein kokemuksesta turhauttavaa avustustekniikkaa tarvitseville käyttäjille. [Lue lisää `tabindex`‐määritteestä](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Joidenkin elementtien `[tabindex]`-arvo on suurempi kuin 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Yhdenkään elementin `[tabindex]`-arvo ei ole suurempi kuin 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Näytönlukuohjelmissa on ominaisuuksia, jotka tekevät taulukoissa siirtymisestä helpompaa. Voit parantaa näytönlukuoh<PERSON>lman käyttäjien kokemusta varmistamalla, että `[headers]`-määritettä käyttävät `<td>`-solut viittaavat vain toisiin soluihin samassa taulukossa. [Lue lisää `headers`‐määritteestä](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "`[headers]`-määritettä käyttävät `<table>`-elementin solut viittaavat elementtiin (`id`), joka ei ole samassa taulukos<PERSON>."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "`[headers]`-määritettä käyttävät `<table>`-elementin solut viittaavat soluihin samassa taulu<PERSON>sa"}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Näytönlukuohjelmissa on ominaisuuksia, jotka tekevät taulukoissa siirtymisestä helpompaa. Voit parantaa näytönlukuohjelmaa käyttävien kokemusta varmistamalla, että taulukoiden otsikot viittaavat aina johonkin solujoukkoon. [Lue lisää taulukoiden otsikoista](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "`<th>`-elementit ja elementit, joissa on `[role=\"columnheader\"/\"rowheader\"]`, eivät sisällä niissä kuvattuja datasoluja"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "`<th>`-elementit ja elementit, joissa on `[role=\"columnheader\"/\"rowheader\"]`, sisältävät niissä kuvatut datasolut"}, "core/audits/accessibility/valid-lang.js | description": {"message": "Kelvo<PERSON>en [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ‑määritteen ilmoittaminen elementeille auttaa varmistama<PERSON>, että näytönlukuohjelma ääntää tekstin oikein. [<PERSON>e lisä<PERSON> `lang`‐määritteen käyttämisestä](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "`[lang]`-määritteiden arvot eivät ole kelvollisia"}, "core/audits/accessibility/valid-lang.js | title": {"message": "`[lang]`-m<PERSON><PERSON><PERSON><PERSON>ill<PERSON> on kelvollinen arvo"}, "core/audits/accessibility/video-caption.js | description": {"message": "Kun videossa on tekstitykset, kuurot ja heikkokuuloiset saavat videon tiedot paremmin. [Lue lisää videoiden tekstityksistä](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementit (`<video>`) eivät sisällä elementtiä (`<track>`), jossa on `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementit (`<video>`) sisältävät elementin (`<track>`), jossa on `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Nykyinen arvo"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tunnus"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` auttaa käyttäjiä lähettämään lomakkeita nopeammin. Voit helpottaa lomakkeiden täyttämistä, jos otat ominaisuuden käyttöön lisäämällä `autocomplete`-määritteelle kelvollisen arvon. [<PERSON>e lisää siitä, miten `autocomplete` on käytössä lomakkeissa](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>`-element<PERSON><PERSON><PERSON> ei ole tarvittavia `autocomplete`-määritteitä"}, "core/audits/autocomplete.js | manualReview": {"message": "Edellyttää manuaalista tarkistusta"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Tarkista tunnusten järjestys"}, "core/audits/autocomplete.js | title": {"message": "`<input>`-<PERSON><PERSON><PERSON><PERSON> on käytössä tarvittava `autocomplete`"}, "core/audits/autocomplete.js | warningInvalid": {"message": "`autocomplete`-tunnukset: \"{token}\" on virheellinen ({snippet})"}, "core/audits/autocomplete.js | warningOrder": {"message": "Tarkista tunnusten järjestys: {tokens} ({snippet})"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Edellyttää toimenpiteitä"}, "core/audits/bf-cache.js | description": {"message": "Monet siirtymiset suoritetaan siirtymällä takaisin edelliselle sivulle tai eteenpäin. Siirtymisvälimuisti (bfcache) voi nopeuttaa palaamissiirtymisiä. [Lue lisää bfcachesta](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 epäonnistumisen syy}other{# epäonnistumisen syytä}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>y"}, "core/audits/bf-cache.js | failureTitle": {"message": "Sivu esti siirtymisv<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>i"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Toimenpiteet eivät ole mahdollisia"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Odottaa selaimen tukea"}, "core/audits/bf-cache.js | title": {"message": "Sivu ei estänyt siirtymisvälimuistin palauttamista"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Chromen laajennukset heikensivät tämän sivun latausnopeutta. Yritä tarkastaa sivu incognito-tilassa tai Chrome-profi<PERSON><PERSON>, johon ei ole lisätty laajennuksia."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Prosessoriaika yhteensä"}, "core/audits/bootup-time.js | description": {"message": "Suosittelemme lyhentämään JS:n jäsentämiseen, kääntämiseen ja suorittamiseen kuluvaa aikaa. Pienempien JS-resurssien jakeleminen voi auttaa. [<PERSON><PERSON>, miten voit lyhentää JavaScriptin suoritusaikaa](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Vähennä JavaScriptin suoritta<PERSON>en kuluvaa aikaa"}, "core/audits/bootup-time.js | title": {"message": "JavaScriptin suoritta<PERSON>en kuluva aika"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON><PERSON>, kopioidut JavaScript-moduulit paketeista pienentääksesi verkkotoiminnan tarpeetonta tavunkulutusta. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Poista kaksoismoduulit JavaScript-paketeista"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Suuret GIFit eivät ole tehokas tapa jaella animoitua sisältöä. Voit pienentää ladattavien tavujen määrää jakelemalla animaatioita MPEG4- tai WebM-muodossa ja staattisia kuvia PNG- tai WebP-muodossa. [Lue lisää tehokkaista videomuodoista](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "<PERSON>le animaatiosisältöä videomuodossa"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Polyfill- ja transform-merkintöjen avulla vanhat selaimet voivat käyttää uusia JavaScript-ominaisuuksia. Monia ei kuitenkaan tarvita nykyaikaisissa selaimissa. Käytä JavaScript-paketissasi nykyaikaista käyttöönottostrategiaa, jossa havaitaan moduuli/ei-moduuli-ominaisuus, minkä avulla vähennetään nykyaikaisiin selaimiin lähetetyn koodin määrää mutta säilytetään silti vanhojen selaimien tuki. [Lue lisää modernin JavaScriptin käyttämisestä](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> Java<PERSON>in näyttämistä nykyaikaisilla selaimilla"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "WebP:n ja AVIF:n kaltaiset kuvamuodot pakkautuvat usein paremmin kuin PNG tai JPEG, mikä nopeuttaa lataamista ja kuluttaa vähemmän dataa. [Lue lisää moderneista kuvamuodoista](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "<PERSON><PERSON> kuvat se<PERSON> sukupolven muodoissa"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Suosittelemme lykkäämään poissa näkyvistä olevien ja piilotettujen kuvien lataamista, kunnes kaikki kriittiset resurssit on ladattu. [Lue lisää poissa näkyvistä olevien kuvien latauksen lykkäämisestä](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Lykkää kuvien la<PERSON>, jos ne eivät ole näky<PERSON>ä"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resurssit estävät sivun ensimmäisen renderöinnin. Suosittelemme jakelemaan kriittiset JS- ja CSS-osat sivuun upotettuina ja lykkäämään kaikkien ei-kriittisten JS- tai tyyliosien lataamista. [Kat<PERSON>, miten voit poistaa renderöinnin estäviä resursseja](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Poista renderöinnin estävät resurssit"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Suuret verkkoresurssit aiheuttavat kuluja käyttäjille ja liittyvät vahvasti pitkiin latausaikoihin. [Lue lisää resurssien koon pienentämisestä](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Yhteenlaskettu koko oli {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Vältä valtavia verkkoresursseja"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Välttää valtavia verkkoresursseja"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "CSS-tiedostojen pienentäminen voi auttaa pienentämään verkkoresurssien kokoa. [<PERSON><PERSON>, miten voit pienentää CSS-tiedostoja](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Pienennä CSS-tiedostoja"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "JavaScript-tiedostojen pienentäminen voi auttaa pienentämään resurssien kokoa ja lyhentämään skriptin jäsentämiseen kuluvaa aikaa. [Kat<PERSON>, miten voit pienentää JavaScript-tiedostoja](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Pienennä JavaScript-tiedostoja"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Vähennä tyylisivujen käyttämättömiä sääntöjä ja lykkää sellaista CSS:ää, jota ei käytetä sivun yläosan sisältöön, niin vähennät verkkotoiminnan tavujen kulutusta. [Lue lisää käyttämättömän CSS:n vähentämisestä](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Vähennä käyttämätöntä CSS:ää"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Vähennä käyttämätöntä JavaScriptiä ja lykkää skriptien lataamista, kunnes niiden on vähennettävä verkkotoiminnan tavujen kulutusta. [Lue lisää käyttämättömän JavaScriptin vähentämisestä](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Vähennä käyttämätöntä JavaScriptiä"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Pitkä välimuistin käyttöikä voi nopeuttaa sivun lataamista, kun käyttäjä avaa sen uudelleen. [Lue lisää tehokkaista välimuistikäytännöistä](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 resurssi löydetty}other{# resurssia löydetty}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Käytä tehokasta välimuistikäytäntöä staattisten resurssien jakelemiseen"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Käyttää tehokasta välimuistikäytäntöä staattisten resurssien käsittelyyn"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimoidut kuvat latautuvat nopeammin ja kuluttavat vähemmän mobiilidataa. [Lue lisää kuvien tehokkaasta koodaamisesta](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "<PERSON><PERSON><PERSON> kuvat teho<PERSON>i"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Todelliset mitat"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Ilmoitetut mitat"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "<PERSON><PERSON> olivat n<PERSON>ttyä kokoa suurempia"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "<PERSON><PERSON> olivat n<PERSON><PERSON> kokoon nähden sopivia"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Näytä sopivan kokoisia kuvia, jotta voit vähentää mobiilidatan kulutusta ja lyhentää latausaikoja. [<PERSON><PERSON>, miten voit muuttaa kuvien kokoa](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Määritä kuvien koko oikein"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Tekstipohjaiset resurssit on hyvä pakata ennen jakelua (gzip, deflate tai brotli), jotta ladattavien tavujen määrä voidaan minimoida. [Lue lisää tekstin pakkaamisesta](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "<PERSON><PERSON> te<PERSON>tin pakka<PERSON> k<PERSON>yttöön"}, "core/audits/content-width.js | description": {"message": "<PERSON><PERSON> sovel<PERSON><PERSON> leveys ei vastaa näkymän leveytt<PERSON>, sovelluksesi ei välttämättä ole mobiilinäytöille optimoitu. [<PERSON><PERSON>, miten voit mukauttaa sisällön koon näkymään](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koko, {innerWidth} px, ei vastaa ikkunan kokoa, {outerWidth} px."}, "core/audits/content-width.js | failureTitle": {"message": "Sisällön koko ei vastaa nä<PERSON>mää"}, "core/audits/content-width.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koko on näkymän mukainen"}, "core/audits/critical-request-chains.js | description": {"message": "Alla olevat kriittiset pyyntöketjut kertovat, minkä resurssien lataaminen priorisoidaan. Suosittelemme parantamaan sivun latausaikaa lyhentämällä ketjuja, pienentämällä resurssien latauskokoa ja lykkäämällä tarpeettomien resurssien lataamista. [<PERSON><PERSON>, miten voit välttää kriittisten pyyntöjen ketjuttamisen](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 ketju löydetty}other{# ketjua löydetty}}"}, "core/audits/critical-request-chains.js | title": {"message": "Vältä kriittisten pyyntöjen ketjuttamista"}, "core/audits/csp-xss.js | columnDirective": {"message": "Sääntö"}, "core/audits/csp-xss.js | columnSeverity": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | description": {"message": "Vahva Content Security Policy ‑k<PERSON>ytäntö (CSP) vähentää merkittävästi cross-site scripting (XSS) ‑hyökkäyksien riskiä. [<PERSON><PERSON>, miten voit estää XSS:n käyttämällä CSP:tä](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Sivu sisältää <meta>-tagissa määritetyn CSP:n. Harkitse CSP:n siirtämistä HTTP-otsikkoon tai toisen tiukan CSP:n määrittämistä HTTP-otsikkoon."}, "core/audits/csp-xss.js | noCsp": {"message": "Täytäntöönpanotilasta ei löytynyt CSP:tä"}, "core/audits/csp-xss.js | title": {"message": "Varmista, että CSP tehoaa XSS-hyökkäyksiä vastaan"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Käytöst<PERSON> poistaminen / varoitus"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Käytöstä poistetut rajapinnat poistetaan aikanaan selaimesta. [Lue lisää käytöstä poistetuista rajapinnoista](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 varoitus löydetty}other{# varoitusta löydetty}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Käyttää käytöstä poistettuja sovellusliittymiä"}, "core/audits/deprecations.js | title": {"message": "Välttää käytöstä poistettuja sovellusliittymiä"}, "core/audits/dobetterweb/charset.js | description": {"message": "Merkistökoodausilmoitus vaaditaan. Sen voi tehdä `<meta>`-tagilla HTML:n ensimmäisen 1 024 tavun sisällä tai HTTP:n vastauksen otsikon sisältötyyppi-kohdassa. [Lue lisää merkistö<PERSON><PERSON><PERSON>sen ilmoittamisesta](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Merkistöilmoitus puuttuu tai näkyy liian myöhään HTML:ssä"}, "core/audits/dobetterweb/charset.js | title": {"message": "Merkistö määritelty oikein"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Dokumenttityypin määrittäminen estää selainta siirtymästä quirks-tilaan. [Lue lisää doctype-ilmoituksesta](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Dokumenttityypin nimen on oltava merkkijono `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokumentin sisältämä `doctype` käynnistää tämän: `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokumentin täytyy sisältää dokumenttityyppi"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "Oletettu publicId-arvo on tyhjä merkkijono"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "Oletettu systemId-arvo on tyhjä merkkijono"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokumentin sisältämä `doctype` käynnistää tämän: `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Sivulta puuttuu HTML-tied<PERSON>otyyppi, mi<PERSON><PERSON> k<PERSON><PERSON>ist<PERSON><PERSON> quirks-tilan"}, "core/audits/dobetterweb/doctype.js | title": {"message": "<PERSON><PERSON><PERSON> on HTML-dokumenttityyppi"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Arvo"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "<PERSON>uri DOM lisää muistin k<PERSON>, piden<PERSON><PERSON><PERSON> [tyylilaskelm<PERSON>](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) ja aiheuttaa työläitä [as<PERSON><PERSON><PERSON> uudelle<PERSON>ok<PERSON>tuks<PERSON>](https://developers.google.com/speed/articles/reflow). [<PERSON><PERSON>, miten voit välttää liian suuren DOM:n](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 elementti}other{# elementtiä}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Vältä liian suurta DOM:ää"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "DOM:n enimmäissyvyys"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "DOM-elementit yhteensä"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "<PERSON><PERSON><PERSON> <PERSON><PERSON> maks<PERSON>"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Välttää liian suurta DOM:ää"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, jotka pyytävät sijainnin käyttöoikeutta ilman asiayhteyttä, saavat käyttäjät epäluuloisiksi tai hämmentävät heitä. Kokeile sen sijaan yhdistää pyyntö käyttäjätoimintoon. [Lue lisää maantieteellisen sijainnin luvasta](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Pyytää maantieteellistä sijaintia sivun latauksessa"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Välttää maantieteellisen sijainnin pyytämistä sivun latauksessa"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Ongelmat<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Chrome DevToolsin `Issues`-paneeliin kirjatut virheet viittaavat ratkaisemattomiin ongelmiin. Ne voivat johtua epäonnistuneista verkkopyynnöistä, riittämättömistä tietoturva-asetuksista ja muista selainongel<PERSON>a. Katso lisätietoja kustakin ongelmasta avaamalla Chrome DevToolsin Ongelmat-paneeli."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> k<PERSON>in Chrome DevToolsin `Issues`-paneeliin"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "<PERSON><PERSON> l<PERSON>hteit<PERSON> koskevan käytännön estämä"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Resursseja kuluttavat raskaat mainokset"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Ei ongelmia Chrome DevToolsin `Issues`-paneelissa"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Versio"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Kaikki APIn JavaScript-kirjastot havaittiin sivulla. [Lue lisää tästä JavaScript-kirjaston havaitsemisen diagnostiikkatarkastuksesta](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Havaitut JavaScript-kirjastot"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Hitaiden yhteyksien käyttäjien kohdalla `document.write()`-komennolla dynaamisesti lisätyt ulkoiset skriptit voivat hidastaa sivun latausta kymmenillä sekunneilla. [Kat<PERSON>, miten voit välttää document.write()-komentoa](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Vältä: `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Vältetty: `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, jotka pyytävät lupaa ilmoitusten lähettämiseen ilman asiayhteyttä, saavat käyttäjät epäluuloisiksi tai hämmentävät heitä. Kokeile sen sijaan yhdistää pyyntö käyttäjäeleisiin. [Lue lisää ilmoitusluvan pyytämisestä vastuullisesti](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Pyytää ilmoitusten käyttöoikeutta sivun latauksessa"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Välttää ilmoitusten käyttöoikeuden pyytämistä sivun latauksessa"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokolla"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 tarjoaa monia etuja HTTP/1.1:een verra<PERSON>, mukaan lukien binaariot<PERSON>ot ja kanavo<PERSON>. [Lue lisää HTTP/2:sta](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 pyyntöä ei tehty HTTP/2:n kautta}other{# pyyntöä ei tehty HTTP/2:n kautta}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Käytä HTTP/2:ta"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "<PERSON>un kannattaa ehkä merkitä kosketus- ja vieritystapahtumien seurainten arvoksi `passive` sivun vieritystoiminnan parantamiseksi. [Lue lisää passiivisten tapahtumaseurainten käyttöönotosta](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ei käytä passiivisia seuraimia vieritystoiminnan parantamiseen"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Käyttää passiivisia seuraimia vieritystoiminnan parantamiseen"}, "core/audits/errors-in-console.js | description": {"message": "Konsoliin kirjatut virheet viittaavat ratkaisemattomiin ongelmiin. Ne voivat johtua epäonnistuneista verkkopyynnöistä ja muista selainongelmista. [Lue lisää virheistä konsolin diagnostiikkatarkastuksesta](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kir<PERSON><PERSON> k<PERSON>in"}, "core/audits/errors-in-console.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ei kirjattu selain<PERSON>itä"}, "core/audits/font-display.js | description": {"message": "Hyödynnä `font-display` CSS ‑ominaisuutta, jotta voit varmistaa tekstin näkymisen käyttäjille verkkofonttien latautuessa. [Lue lisää: `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Varmista, että teksti pysyy näkyvissä verkkofontin la<PERSON> aikana"}, "core/audits/font-display.js | title": {"message": "Kaikki teksti pysyy näkyvissä verkkofontin <PERSON> aikana"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Lighthouse ei voinut automaattisesti tarkistaa lähteen ({fontOrigin}) arvoa: `font-display`.}other{Lighthouse ei voinut automaattisesti tarkistaa lähteen ({fontOrigin}) arvoja: `font-display`.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "<PERSON><PERSON><PERSON><PERSON> (todellinen)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Kuvasuhde (näkyvä)"}, "core/audits/image-aspect-ratio.js | description": {"message": "<PERSON><PERSON> mit<PERSON>iden tulisi täsmätä luonnolliseen kuvasuhteeseen. [Lue lisää kuvasuhteesta](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ku<PERSON>, joiden ku<PERSON> on virheellinen"}, "core/audits/image-aspect-ratio.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> ku<PERSON>, joiden ku<PERSON> on oikea"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Todellinen koko"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Näkyvä koko"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Odotettu koko"}, "core/audits/image-size-responsive.js | description": {"message": "<PERSON><PERSON> luonnollisten mittojen pitäisi olla suhteutettu näytön kokoon ja piksel<PERSON>n, jotta kuva on mah<PERSON><PERSON><PERSON><PERSON> selkeä. [Lue lisää responsiivisten kuvien lisäämisestä](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Näyttää kuvat alhaisella resoluutiolla"}, "core/audits/image-size-responsive.js | title": {"message": "Näyttää kuvat sopivalla resoluutiolla"}, "core/audits/installable-manifest.js | already-installed": {"message": "<PERSON><PERSON><PERSON> on jo as<PERSON><PERSON>u"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Tarvittavan kuvakkeen lataaminen teknisistä tiedoista ei onnistunut"}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>y"}, "core/audits/installable-manifest.js | description": {"message": "Service worker ‑teknologia tuo sovelluksen käyttöön monia progressiivisen web-sovelluksen ominaisuuksia, kuten offline-k<PERSON>ytön, aloitusnäytölle lisäämisen ja ilmoitukset. Jos service worker ja manifesti on otettu käytt<PERSON>ön o<PERSON>, selaimet voivat aktiivisesti suositella käyttäjille sovelluksesi lisäämistä aloitusnäytölle, mikä voi lisätä aktivoitumisten määrää. [Lue lisää manifestin asennusvaatimuksista](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 syy}other{# syytä}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Web app manifest ja service worker e<PERSON><PERSON> tä<PERSON> asennettavuusvaatimuksia"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Play <PERSON><PERSON><PERSON> ‑sovelluksen URL ja Play Kaupan tunnus eivät täsmää"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Sivu ladataan incognito-ikkunassa"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Manifestin \"display\"-o<PERSON><PERSON><PERSON><PERSON>n on oltava \"standalone\", \"fullscreen\" tai \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Man<PERSON><PERSON><PERSON><PERSON> on \"display_override\"-<PERSON><PERSON><PERSON><PERSON>, ja ensimmäisen tue<PERSON><PERSON> on oltava \"standalone\", \"fullscreen\" tai \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestin nouto ei onnistunut, se on tyhjä tai ei jäsenneltävissä"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifestin URL muuttui manifestin noudon aikana"}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestiin ei sisälly \"name\"- ta<PERSON> \"short_name\"-kentt<PERSON>ä"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestissa ei ole sopivaa kuva<PERSON>ta, si<PERSON><PERSON> on oltava vähintään {value0} pik<PERSON>in kokoinen PNG-, SVG- tai WebP-tiedosto, kokomäärite on lisättävä ja mahdollisen tarkoitusmääritteen on oltava \"any\"."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuvakkeista ei ole vähintään {value0} pik<PERSON><PERSON> kokoinen PNG-, SVG- tai WebP-tied<PERSON><PERSON>, kun tarkoitusmääritettä ei ole lisätty tai se on \"any\"."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Lada<PERSON>u kuvake oli tyhjä tai vioittunut"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "<PERSON><PERSON> <PERSON> tun<PERSON>ta"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Sivulta puuttuu manifestin <link>-URL"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Vastaavaa service workeria ei havaittu. <PERSON><PERSON> on ehkä päivitettävä sivu ja tark<PERSON>, katta<PERSON><PERSON> nykyisen sivun service workerin laajuus manifestin ja aloitus-URL:in."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Service workeria ei voitu tark<PERSON>, koska manifestistä puuttuu \"start_url\"-kenttä"}, "core/audits/installable-manifest.js | noErrorId": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tunnusta \"{errorId}\" ei tunnistettu"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Sivu ei ole peräisin turvallisesta lähteestä"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Sivu ei lataudu pääkehyksessä"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Sivu ei toimi ilman verkkoyhteyttä"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA on poistettu ja asennettavuustarkist<PERSON> nolla<PERSON>."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Määritettyä sovellusalustaa ei tueta Androidilla"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifestin määritys prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications on tuettu vain Chromen betaversioissa ja vakaissa versioissa Androidilla."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse ei havainnut service workeria. Yritä uudelleen Chromen uudemmalla versiolla."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Manifestin URL-kaava ({scheme}) ei ole <PERSON>in tukema."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "<PERSON>ife<PERSON><PERSON>-URL ei kelpaa"}, "core/audits/installable-manifest.js | title": {"message": "Web app manifest ja service worker t<PERSON>yttäv<PERSON> asennettavuusvaatimukset"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "Manifestissa oleva URL sisältää käyttäjänimen, salasanan tai portin"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Sivu ei toimi ilman verkkoyhteyttä. Tämä sivu ei enää ole ladattavissa Chrome 93:n vakaan version jälkeen (julkaistaan elokuussa 2021)."}, "core/audits/is-on-https.js | allowed": {"message": "Sallit<PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "Suojaamaton URL-osoite"}, "core/audits/is-on-https.js | columnResolution": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | description": {"message": "Kaik<PERSON> sivustot on suojattava HTTPS:llä, vaikka ne eivät käsittelisi arkaluontoista dataa. Vältä myös [yhdistelmäsisältöä](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content) eli alkupyyntöön vastaamista HTTPS:llä ja joidenkin resurssien lataamista HTTP:llä. HTTPS estää tunkeutujia peukaloimasta sovelluksesi ja sen käyttäjien välistä toimintaa tai seuraamasta sitä passiivisesti. HTTPS:ää edellytetään HTTP/2:ssa ja monien uusien verkkoalustojen rajapinnoissa. [Lue lisää HTTPS:stä](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 suojaamaton pyyntö löytyi}other{# suojaamatonta pyyntöä löytyi}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Ei käytä HTTPS:ää"}, "core/audits/is-on-https.js | title": {"message": "Käyttää HTTPS:ää"}, "core/audits/is-on-https.js | upgraded": {"message": "Siirretty automaattisesti HTTPS:<PERSON><PERSON>n"}, "core/audits/is-on-https.js | warning": {"message": "<PERSON><PERSON><PERSON> kanssa"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "<PERSON>äm<PERSON> on näkymän suurin renderöity sisältö. [Lue lisää Sivun latautumisaika ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | title": {"message": "<PERSON><PERSON><PERSON> sis<PERSON>"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "CLS-v<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/layout-shift-elements.js | description": {"message": "Nämä DOM-elementit tuottavat suurimman osan sivun CLS-arvosta. [<PERSON><PERSON>, miten voit parantaa CLS:ää](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Vältä suuria as<PERSON><PERSON><PERSON> mu<PERSON>ia"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Laiskasti latautuvat sivun yläosan kuvat renderöidään sivulla my<PERSON>, mikä voi viivästyttää sivun latautumisaikaa. [Lue lisää optimaalisesta laiskasta latautumisesta](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> lataut<PERSON>jan kuva latautui laiskasti"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "<PERSON>vun latautumisajan kuva ei latautunut laiskasti"}, "core/audits/long-tasks.js | description": {"message": "Näyttää pääsäikeen pitkäkestoisimmat tehtävät, mistä on apua syöttöviiveen pahimpien aiheuttajien tunnistamisessa. [<PERSON><PERSON>, miten voit välttää pitkät pääsäikeen tehtävät](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# pitkäkestoinen tehtävä löydetty}other{# pitkäkestoista tehtävää löydetty}}"}, "core/audits/long-tasks.js | title": {"message": "Vältä pitkäkestoisia pääsäikeen tehtäviä"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Suosittelemme lyhentämään JS:n jäsentämiseen, kääntämiseen ja suorittamiseen kuluvaa aikaa. Pienempien JS-resurssien jakeleminen voi auttaa. [<PERSON><PERSON>, miten voit minimoida pääsäikeestä aiheutuvaa työtä](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)."}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimoi pääsäikeen työkuorma"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimoi pääsäikeen työkuorman"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Tavoittaakseen maksimimäärän käyttäjiä sivustojen tulee toimia kaikilla tärkeillä selaimilla. [Lue lisää yhteensopivuudesta eri selainten kanssa](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Sivusto toimii eri selaimilla"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Varmista, että yksittäisiin sivuihin voi täsmälinkittää URL-osoitteella ja että URL-osoitteet ovat ainutlaatuisia, jotta jaettavuus somessa paranee. [Lue lisää täsmälinkkien lisäämisestä](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "<PERSON><PERSON> sivulla on URL-osoite"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Napautuksilla navigoitaessa siirtymien tulee olla saumattomia, vaikka verkko olisi hidas. Näin syntyy vaikutelma toimivuudesta. [Lue lisää sivusiirtymistä](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Verkko ei estä sivujen välisiä siirtymiä"}, "core/audits/maskable-icon.js | description": {"message": "Peitettävällä kuvakkeella varmistetaan, että kuva täyttää koko muodon ilman vaakapalkkien käyttöä, kun sovellusta asennetaan laitteelle. [Lue lisää peitettävistä manifestikuvakkeista](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Teknisissä tiedoissa ei ole peitettävää kuvaketta"}, "core/audits/maskable-icon.js | title": {"message": "Teknisissä tiedoissa on peitettävä kuvake"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulatiivinen asettelumuutos mittaa näkymässä olevien elementtien liikettä. [Lue lisää Kumulatiivinen asettelumuutos ‐mittarista](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaktiosta seuraavaan renderöintiin ‐mittari mittaa sivun responsiivisuutta eli sitä, kuinka pian sivu vastaa näkyvästi käyttäjän palautteeseen. [Lue lisää Interaktiosta seuraavaan renderöintiin ‐mittarista](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Ensimmäinen sisällön renderöinti kertoo, milloin ensimmäinen tekstikohde tai kuva renderöidään. [Lue lisää Ensimmäinen sisällön renderöinti ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "Ensimmäinen merkityksellinen renderöinti kertoo, milloin sivun ensisijainen sisältö tulee näkyviin. [Lue lisää Ensimmäinen merkityksellinen renderöinti ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Interaktiivisuutta edeltävä aika tarkoittaa aikaa, joka sivulla kestää siihen, että se on täysin interaktiivinen. [Lue lisää Interaktiivisuutta edeltävä aika ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Suurimman sisällön renderöinti mittaa suurimman tekstikohteen tai kuvan renderöintiaikaa. [Lue lisää Sivun latautumisaika ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Käyttäjien ensitoiminnon suurin mahdollinen viive on sama kuin pisimmän tehtävän kesto. [Lue lisää Suurin mahdollinen ensimmäisen toiminnon viive ‑mittarista](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Nopeusindeksi kertoo, kuinka nopeasti sivun sisältö tulee näkyviin. [Lue lisää Nopeusindeksi-mittarista](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Kaikkien FCP:n ja interaktiivisuutta edeltävän ajan väliset ajanjaksot yhteenlaskettuna, kun tehtävän pituus on yli 50 ms (ilmoitettu millisekunteina). [Lue lisää Estoaika yhteensä ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Verkon meno-paluuajoilla (RTT) on suuri vaikutus suorituskykyyn. Jos RTT lähtöpaikkaan on pitkä, se on merkki siitä, että käyttäjää lähellä olevien palvelimien suorituskyvyssä on parantamisen varaa. [Lue lisää meno-paluuajasta](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Verkon meno-paluuajat"}, "core/audits/network-server-latency.js | description": {"message": "Palvelimen viiveet voivat vaikuttaa verkon suorituskykyyn. Jos <PERSON>ikan palvelimen viive on korkea, se on merkki siitä, ett<PERSON> palvelin on ylikuormittunut tai sen taustasuorituskyky on huono. [Lue lisää palvelimen vastausajasta](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Palvelimen taustaviiveet"}, "core/audits/no-unload-listeners.js | description": {"message": "`unload` ei k<PERSON><PERSON><PERSON><PERSON> l<PERSON>, ja sen kuuntelu voi estää selaimen optimoinnin, esim. Back-Forward-vä<PERSON>uistin toiminnan. Käytä sen sijaan `pagehide`- tai `visibilitychange`-tapahtumia. [Lue lisää tapahtumaseurainten tyhjentämisestä](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Re<PERSON><PERSON><PERSON><PERSON> (`unload`) kuuntelijan"}, "core/audits/no-unload-listeners.js | title": {"message": "Ohittaa (`unload`) kuuntelijat"}, "core/audits/non-composited-animations.js | description": {"message": "Sommittelemattomat animaatiot voivat olla huonolaatuisia ja nostaa CLS:ää. [<PERSON><PERSON>, miten voit välttää sommittelemattomia animaatioita](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)."}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animaatioelementti löydetty}other{# animaatioelementtiä löydetty}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "filter-omaisuus voi siirtää pikseleitä"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON><PERSON><PERSON> on toinen yhteensopimaton animaatio"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Tehosteen sommittelutila on muu kuin replace"}, "core/audits/non-composited-animations.js | title": {"message": "Vältä sommittelemattomia animaatioita"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "transform-o<PERSON><PERSON><PERSON><PERSON> riippuu kentän koosta"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Ei-tuettu CSS-omaisuus: {properties}}other{Ei-tuetut CSS-omaisuudet: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Te<PERSON><PERSON><PERSON> on ei-tuettuja ajoitusparametreja"}, "core/audits/performance-budget.js | description": {"message": "Pidä verkkopyyntöjen määrä ja koko tehokkuusbudjetissa määritettyjen tavoitteiden rajoissa. [Lue lisää tehokkuusbudjeteista](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 pyyntö}other{# pyyntöä}}"}, "core/audits/performance-budget.js | title": {"message": "Tehok<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/preload-fonts.js | description": {"message": "Esilataa `optional` fonttia ensikertalaisten kävijöiden saataville. [Lue lisää fonttien esilataamisesta](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, joissa on `font-display: optional`, ei ole esiladattu"}, "core/audits/preload-fonts.js | title": {"message": "Fontit, joissa on `font-display: optional`, on esiladattu"}, "core/audits/prioritize-lcp-image.js | description": {"message": "<PERSON><PERSON>-elementti on lisätty dynaamisesti sivulle, sinun pitää esiladata kuva LCP:n parantamiseksi. [Lue lisää LCP-elementtien esilataamisesta](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Esilataa sivun lata<PERSON> kuva"}, "core/audits/redirects.js | description": {"message": "Uudelleenohjaukset viivästyttävät sivun lataamista. [<PERSON><PERSON>, miten voit välttää sivujen uudelleenohjaukset](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Vältä useita uudelleenohjauksia"}, "core/audits/resource-summary.js | description": {"message": "<PERSON><PERSON> haluat as<PERSON>a sivuresurssien määrälle ja koolle budjetin, lisää budget.json-tiedosto. [Lue lisää tehokkuusbudjeteista](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 pyyntö • {byteCount, number, bytes} KiB}other{# pyyntöä • {byteCount, number, bytes} Ki<PERSON>}}"}, "core/audits/resource-summary.js | title": {"message": "Pid<PERSON> pyyntöjen määrät alhaisina ja siirtojen koot pieninä"}, "core/audits/seo/canonical.js | description": {"message": "Ensisijaiset linkit ehdottavat, mitä URL-osoitteita näyttää hakutuloksissa. [Lue lisää ensisijaisista linkeistä](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Useita ristiriitaisia URL-osoitteita ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Virheellinen URL-osoite ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Vii<PERSON>a toiseen `hreflang`-sijaintiin ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Ei ole absoluuttinen URL-osoite ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Osoittaa verkkotunnuksen juuri-URL-osoitteeseen (kotisivulle) sitä vastaavan sisältösivun sijaan"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentissa ei ole kelvo<PERSON> `rel=canonical`-määritettä"}, "core/audits/seo/canonical.js | title": {"message": "Dokumentissa on kelvollinen `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Linkki ei indeksoitavissa"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Hakukoneet voivat käyttää linkeissä `href`-attribuutteja sivustojen indeksointia varten. Varmista, että ankkurielementtien `href`-attribuutti linkittää sopivaan kohteeseen, jotta useammat sivuston sivut ovat löydettävissä. [<PERSON><PERSON>, miten voit tehdä linkeistä indeksoitavia](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Linkit eivät ole indeksoitavissa"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Linkit ovat indeksoitavissa"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "<PERSON><PERSON> luku<PERSON> te<PERSON>ti"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Fonttikoko"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% sivun tekstistä"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Alle 12 pikselin kirjasinkoot ovat liian pieniä luettavaksi ja edellyttävät mobiilivierailijoiden zoomaavan nipistämällä voidakseen lukea. Pyri siihen, että >60 % sivun tekstistä on ≥12 px. [Lue lisää helppolukuisesta fonttikoosta](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} lukukelpoista tekstiä"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "<PERSON><PERSON><PERSON> on lukukelvotonta, koska näkymän sisällönkuvauskenttää ei ole optimoitu mobiilinäytöille."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentissa ei käytetä lukukelpoisia fonttikokoja"}, "core/audits/seo/font-size.js | legibleText": {"message": "Lukukelpoista tekstiä"}, "core/audits/seo/font-size.js | title": {"message": "Dokumentti käyttää lukukelpoisia fonttikokoja"}, "core/audits/seo/hreflang.js | description": {"message": "hreflang-linkit kertovat hakukoneille, mikä sivuversio niiden pitäisi lisätä tietyn kielen tai alueen hakutuloksiin. [Lue lisää: `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentissa ei ole kelvollista `hreflang`-elementtiä"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Suhteellinen href-arvo"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumentissa on kelvollinen `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Odottamat<PERSON> k<PERSON>i"}, "core/audits/seo/http-status-code.js | description": {"message": "Epäonnistuneita HTTP-tilakoodeja sisältäviä sivuja ei välttämättä indeksoida oikein. [Lue lisää HTTP-tilakoodeista](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Sivun HTTP-til<PERSON>oodi on epäonnistunut"}, "core/audits/seo/http-status-code.js | title": {"message": "Sivun HTTP-<PERSON><PERSON><PERSON><PERSON> on onnistunut"}, "core/audits/seo/is-crawlable.js | description": {"message": "Hakukoneet eivät voi sisällyttää sivujasi hakutu<PERSON>iin, jos niillä ei ole lupaa indeksoida niitä. [Lue lisää indeksointirobottien säännöistä](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> in<PERSON> on estetty"}, "core/audits/seo/is-crawlable.js | title": {"message": "<PERSON>vun indeksointia ei ole estetty"}, "core/audits/seo/link-text.js | description": {"message": "Kuvailevat linkkitekstit auttavat hakukoneita ymmärtämään sisältöäsi. [<PERSON><PERSON>, miten voit helpottaa linkkien käyttöä](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 linkki löydetty}other{# linkkiä löydetty}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Linkeissä ei ole kuvaavaa tekstiä"}, "core/audits/seo/link-text.js | title": {"message": "Linkeissä on kuvailevaa tekstiä"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Suorita [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) ja [Structured Data Linter](http://linter.structured-data.org/) vahvistaaksesi strukturoidun datan. [Lue lisää strukturoidusta datasta](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturoitu data on kelvollinen"}, "core/audits/seo/meta-description.js | description": {"message": "Hakutuloks<PERSON>n voidaan lisätä sisällönkuvauskenttiä, joissa kuvaillaan sivun sisältöä lyhyesti. [Lue lisää sisällönkuvauskentästä](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Kuvausteksti on tyhjä."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentissa ei ole sisällönkuvauskenttää"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumentissa on sisällönkuvauskenttä"}, "core/audits/seo/plugins.js | description": {"message": "Hakukoneet eivät voi indeksoida liitännäisten sisältöä, ja monet laitteet rajoittavat liitännäisten käyttöä tai eivät tue niitä. [Lue lisää liitännäisten välttämisestä](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentti käyttää laajennuksia"}, "core/audits/seo/plugins.js | title": {"message": "Dokumentti välttää laajennuksia"}, "core/audits/seo/robots-txt.js | description": {"message": "Jos robots.txt-tied<PERSON><PERSON><PERSON> on muo<PERSON>ilt<PERSON> v<PERSON>, indeksointirobotit eivät välttämättä ymmärrä, miten haluat sivustosi indeksoitavan. [Lue lisää robots.txt-tiedostoista](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Robots.txt-pyyntö palautti HTTP-tilan: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{1 virhe löydetty}other{# virhettä löydetty}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse ei voinut ladata robots.txt-tiedostoa"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt ei ole kelvollinen"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt on kelvollinen"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktiivisten elementtien, kuten painik<PERSON>iden ja linkkien, on oltava tarpeeksi suuria (48 x 48 px) ja niiden ympärillä on oltava tarpeeksi tilaa, jotta niiden napauttaminen onnistuu helpost<PERSON> ni<PERSON>, etteivät ne ole muiden elementtien päällä. [Lue lisää napautuskohteista](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} o<PERSON>an k<PERSON><PERSON>a <PERSON>"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Napautuskohteet ovat liian pieni<PERSON>, koska näkymän sisällönkuvauskenttää ei ole optimoitu mobiilinäytöille"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Napautuskohteet eivät ole sopivan kokoisia"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Päällekkä<PERSON> kohde"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "Napautuskohteet ovat sopivan koko<PERSON>a"}, "core/audits/server-response-time.js | description": {"message": "Varmista, että päädokumentin palvelimen vasteaika on ly<PERSON>t, koska kaikki muut pyynnöt ovat riippuvaisia siitä. [Lue lisää Ensimmäistä tavua edeltävä aika ‐mittarista](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Päädokumentti käytti {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Lyhennä palvelimen vasteaikaa alussa"}, "core/audits/server-response-time.js | title": {"message": "Palvelimen vasteaika alussa oli ly<PERSON>t"}, "core/audits/service-worker.js | description": {"message": "Service worker ‑teknologia tuo sovelluksen käyttöön monia progressiivisen web-sovelluksen ominaisuuksia, kuten offline-käytön, aloitusnäytölle lisäämisen ja ilmoitukset. [Lue lisää Service Workereista](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "Service worker hall<PERSON><PERSON> si<PERSON><PERSON>, mutta <PERSON> (`start_url`) ei l<PERSON>, koska luetteloa ei voitu jäsentää kelvollisena JSONina."}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Service worker hallitsee si<PERSON>a, mutta `start_url` ({startUrl}) ei ole workerin toiminta-al<PERSON><PERSON> ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "Service worker hallits<PERSON> si<PERSON><PERSON>, mutta o<PERSON> (`start_url`) ei l<PERSON>, koska luette<PERSON>a ei noudettu."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on ainakin yksi service worker, mutta sivu ({pageUrl}) ei kuulu sen toiminta-al<PERSON><PERSON><PERSON>."}, "core/audits/service-worker.js | failureTitle": {"message": "<PERSON>i rekister<PERSON>i service workeria, jonka hallinn<PERSON>a sivu ja `start_url` ovat"}, "core/audits/service-worker.js | title": {"message": "Rekisteröi service workerin, jonka hall<PERSON><PERSON>a sivu ja `start_url` ovat"}, "core/audits/splash-screen.js | description": {"message": "Teeman sisältävä aloitussivu varmistaa laadukkaan kokemu<PERSON>en, kun käyttäjä avaa sovelluksen aloitusnäytöltään. [Lue lisää aloitusnäytöistä](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Yksilöityä aloitusnäyttöä ei ole määritetty"}, "core/audits/splash-screen.js | title": {"message": "Yksilöity aloitusnäyttö määritetty"}, "core/audits/themed-omnibox.js | description": {"message": "Voit muokata selaimen osoitepalkkia sivustosi teeman mukaiseksi. [Lue lisää osoitepalkin teemasta](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "<PERSON>i aseta osoitepalkin tee<PERSON>äri<PERSON>"}, "core/audits/themed-omnibox.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (asiakkaiden menestystarinat)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (mark<PERSON><PERSON><PERSON>)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (some)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "<PERSON><PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Osa kolmansien osapuolten upotuksista on ladattavissa laiskasti. Kannattaa ehkä korvata ne fasadeilla, kunnes niitä tarvitaan. [Lue lisää kolmansien osapuolten lykkäämisestä fasadilla](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# vaihtoehtoinen fasadi saatavilla}other{# vaihtoehtoista fasadia saatavilla}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Osa kolm<PERSON>ien osapuolten sisällöstä on ladattavissa laiskasti fasadin avulla"}, "core/audits/third-party-facades.js | title": {"message": "Lataa kolmansien osapuolten sisältöä laiskemmin fasadeilla"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/third-party-summary.js | description": {"message": "<PERSON><PERSON><PERSON> osapuolen koodi voi vaikuttaa lataustehokkuuteen merkittävästi. <PERSON><PERSON><PERSON> tarpeettomien kolmannen osapuolen palveluntarjoajien määrää ja yritä ladata kolmannen osapuolen koodi sen jälkeen, kun sivun ensisijainen lataus on valmis. [<PERSON><PERSON>, miten voit minimoida kolmannen osapuolen vaikutuksen](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "<PERSON><PERSON><PERSON> osapuolen koodi esti pääsäikeen {timeInMs, number, milliseconds} ms:n ajan"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Vähennä kolmannen osapuolen koodin vaikutusta"}, "core/audits/third-party-summary.js | title": {"message": "Minimoi kolmannen osapuolen k<PERSON>tö"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Arvo"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "<PERSON><PERSON> a<PERSON>, niin voit seurata sivustosi nopeutta. Kun sivusto toimii o<PERSON>, sen latausa<PERSON> on lyhyt ja se vastaa käyttäjän syötteisiin nopeasti. [Lue lisää tehokkuusbudjeteista](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/unsized-images.js | description": {"message": "Lisää kuvaelementeille kiinteä leveys ja pituus, jotta as<PERSON>lu muuttuu vähemmän ja CLS paranee. [<PERSON><PERSON>, miten voit valita kuvan mitat](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Kuvaelementeiltä puuttuu kiinteä `width` ja `height`"}, "core/audits/unsized-images.js | title": {"message": "Kuvaelementeillä on kiinteä `width` ja `height`"}, "core/audits/user-timings.js | columnType": {"message": "Tyyppi"}, "core/audits/user-timings.js | description": {"message": "Suosittelemme käyttämään sovelluksen kehittämisessä User Timing APIa mittaamaan todellista toimivuutta tärkeiden käyttökokemusten aikana. [Lue lisää User Timing ‑merkeistä](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 käyttäjän ajankäyttömerkintä}other{# käyttäjän ajankäyttömerkintää}}"}, "core/audits/user-timings.js | title": {"message": "User Timing ‑merkinnät ja ‑mitat"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "`<link rel=preconnect>` <PERSON><PERSON><PERSON><PERSON> osoitteelle {securityOrigin}, mutta selain ei käyttänyt sitä. Varmista, että käytät eri lähteiden `crossorigin`-määritettä oikein."}, "core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> lisäämään sivulle `preconnect`- tai `dns-prefetch` ‑resurssivihjeitä, joiden avulla yhteydet tärkeisiin kolmannen osapuolen alkuperiin voidaan muodostaa ajoissa. [<PERSON><PERSON>, miten voit muodostaa yhteyden pakollisiin alkuperiin](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Muodosta yhteydet pakollisiin kohteisiin etuk<PERSON>"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "`<link rel=preconnect>`-yhteyksiä löytyi enemmän kuin kaksi. Niitä tulee käyttää säästeliäästi ja vain tärkeim<PERSON>in alkuperiin."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "`<link rel=preconnect>` l<PERSON><PERSON><PERSON> osoitteelle {securityOrigin}, mutta selain ei käyttänyt sitä. Valitse `preconnect` vain tärkeille alkuperille, joita sivu pyytää varmasti."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Latausta edeltävä `<link>` l<PERSON><PERSON><PERSON> osoitteelle {preloadURL}, mutta selain ei käyttänyt sitä. Varmista, että käytät eri lähteiden `crossorigin`-määritettä oikein."}, "core/audits/uses-rel-preload.js | description": {"message": "Suos<PERSON>lemme k<PERSON>yttämään `<link rel=preload>`-tag<PERSON>, jotta voit priorisoida resursseja, joiden noutamista pyydetään sivun lataamisen myöhemmässä vaiheessa. [Lue lisää tärkeiden pyyntöjen esilataamisesta](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Lataa tärkeät p<PERSON>ynnöt etukäteen"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Kartan URL"}, "core/audits/valid-source-maps.js | description": {"message": "Lähdekartat muuntavat pienennetyn koodin alkuperäiseksi lähdekoodiksi. Tämä auttaa kehittäjiä jäljittämään ja korjaamaan virheet tuotantovaiheessa. Lisätietoja saa myös Lighthousesta. Sinun kannattaa ehkä ottaa lähdekartat käyttöön, jotta voit hyödyntää näitä mahdollisuuksia. [Lue lisää lähdekartoista](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Suuresta ensimmäisen osapuolen JavaScript-kirjastosta puuttuu lähdekarttoja"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Suuresta JavaScript-tiedostosta puuttuu lähdekartta"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Varoitus: 1 puuttuva kohde (`.sourcesContent`)}other{Varoitus: # puuttuvaa kohdetta (`.sourcesContent`)}}"}, "core/audits/valid-source-maps.js | title": {"message": "<PERSON><PERSON><PERSON> on kelvollisia lähdekarttoja"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` optimoi sovelluksesi mobiilinäyttöjen kokoja varten ja lisäksi estää [käyttäjän syötteen 300 millisekunnin viiveen](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Lue lisää viewport-metatagin käyttämisestä](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "`<meta name=\"viewport\">`-tagia ei löytynyt"}, "core/audits/viewport.js | failureTitle": {"message": "`<meta name=\"viewport\">` ‑tagi, jossa `width` tai `initial-scale`, puuttuu"}, "core/audits/viewport.js | title": {"message": "`<meta name=\"viewport\">` ‑tagi, jossa `width` tai `initial-scale`, löytyy"}, "core/audits/work-during-interaction.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> on s<PERSON><PERSON><PERSON> esto, joka tapahtuu Interaktiosta seuraavaan renderöintiin ‐mittauksen aikana. [Lue lisää Interaktiosta seuraavaan renderöintiin ‐mittarista](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms k<PERSON><PERSON><PERSON> tapahtumassa \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON><PERSON> kohde"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimoi työ tärkeän interaktion aikana"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Syötteen viive"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Esityksen viive"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Käsittelyaika"}, "core/audits/work-during-interaction.js | title": {"message": "Minimoi työn tärkeän interaktion aikana"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Näillä voit parantaa ARIA:n käyttöä sovellukses<PERSON>si, mikä voi tehdä avustusteknologiaa (kuten näytönlukuohjelmaa) käyttävien kokemuksesta paremman."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Näillä voit antaa vaihtoehtoista sisältöä äänelle ja videolle. Tämä voi parantaa kuulo- tai näkörajoitteisten käyttäjien kokemusta."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "Ääni ja video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Näissä kohdissa kerrotaan yleisistä esteettömyyden parhaista käytännöistä."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Parhaat käytännöt"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Nämä tarkistukset tuovat esiin kohtia, jois<PERSON> voit [parantaa verkkosovelluksesi esteettömyyttä](https://developer.chrome.com/docs/lighthouse/accessibility/). Vain pieni joukko esteettömyysongelmia voidaan havaita automaattisesti, joten myös manuaalista testaamista suositellaan."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Nämä kohteet koskevat al<PERSON>, joita automaattinen testaustyökalu ei voi testata. Lue lisää [saavutettavuustarkistuksen tekemisen](https://web.dev/how-to-review/) oppaastamme."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Näillä voit parantaa sisältösi luettavuutta."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Näillä voit parantaa tul<PERSON>, joita eri al<PERSON>iden käyttäjät tekevät sisällöstäsi."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Kansainvälistyminen ja lokal<PERSON>ointi"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Näillä voit parantaa sovelluksen ohjainten semantiikkaa. Tämä voi parantaa avustusteknologiaa (kuten näytönlukuohjelmaa) käyttävien kokemusta."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON> ja tun<PERSON><PERSON>t"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Nämä ovat tilaisuuksia parantaa näppäimistöllä siirtymistä sovelluksessasi."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Näillä voit parantaa taulukko- tai listadatan lukukokemusta avustusteknologian (esim. näytö<PERSON><PERSON>) kanssa."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tauluk<PERSON> ja luettelot"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Parhaat käytännöt"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> ja turvallisuus"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Käyttökokemus"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Tehokkuusbudjetit määrittävät sivuston tehokkuuden standardit."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budjetit"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Lisätietoja sovelluksen toiminnasta. Luvut eivät [su<PERSON><PERSON> vaikuta](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) tehokkuusprosenttiin."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostiikka"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Tehokkuuden tärkein osa-alue on se, kuinka nopeasti pikselit renderöidään näytölle. Tärkeimmät mittarit ovat ensimmäinen sisällön renderöinti ja ensimmäinen merkityksellinen renderöinti."}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Ensimmäistä renderöintiä koskevat parannusehdotukset"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Nämä ehdotukset voivat auttaa sivua latautumaan nopeammin. Ne eivät [suoraan vaikuta](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) tehokkuusprosenttiin."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Suositukset"}, "core/config/default-config.js | metricGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "<PERSON>nna la<PERSON><PERSON>, jotta sivu on responsiivisempi ja käytettävissä mahdollisimman pian. Tärkeimmät mittarit ovat interaktiivisuutta edeltävä aika ja nopeusindeksi."}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Y<PERSON><PERSON>t parannusehdotukset"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Näillä testeillä vahvistetaan progressiivisen web-sovelluksen ominaisuudet. [Katso ohjeita hyvään progressiiviseen web-sovellukseen](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Normaali [PWA Checklist](https://web.dev/pwa-checklist/) sisältää nämä kohdat, mutta Lighthouse ei tarkista niitä automaattisesti. Ne eivät vaikuta tulokseesi, mutta on tärkeää, että tarkistat kohdat manuaalisesti."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Asennettavissa"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "PWA optimoitu"}, "core/config/default-config.js | seoCategoryDescription": {"message": "Näillä testeill<PERSON> var<PERSON>, että sivusi seuraa hakukoneoptimoinnin perusneuvoja. On myös monia muita tekijöitä, joita <PERSON> ei ota täällä huomioon mutta jotka voivat vaikuttaa hakusijoitukseesi, esim. [<PERSON><PERSON><PERSON> suori<PERSON>](https://web.dev/learn-core-web-vitals/) ‑tulokset. [Lue lisää Google Haun perusteista](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Käytä näitä lisätarkistustyökaluja sivustollasi tarkistaaksesi kaikki hakukoneoptimoinnin parhaat käytännöt."}, "core/config/default-config.js | seoCategoryTitle": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Muotoile HTML niin, ett<PERSON> indeksointirobottien on helpompi ymmärtää sovelluksen sisältöä."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Parhaat sisältökäytännöt"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Indeksointiroboteilla on oltava pääsy sovellukseen, jotta se voi näkyä hakutuloksissa."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Indeksointi ja hakemistoon lisääminen"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "Varmista, että sivut ovat mobiiliystävällisiä, jotta käyttäjien ei tarvitse nipistää tai lähentää sisältösivuja lukeakseen niitä. [<PERSON><PERSON>, miten voit tehdä sivuista mobiiliystävällisiä](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobiiliystävällinen"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Testatun laitteen CPU näyttää olevan hitaampi kuin Lighthouse olettaa. Tämä voi vaikuttaa tuloksiin negatiivisesti. Lue lisää [sopivan CPU-hidastuskertoimen kalibroinnista](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON>vun lataus ei ehkä onnistu o<PERSON>, koska testi-URL ({requested}) oh<PERSON><PERSON><PERSON> uudelleen osoitteeseen {final}. <PERSON><PERSON><PERSON> su<PERSON>an toista URL-osoitetta."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Sivu ei ehtinyt latautua loppuun aikarajan sisällä. Tuloksista voi puuttua osa."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Selaimen vä<PERSON>uistin tyhjentäminen aikakatkaistiin. Tarkista sivu uudelleen ja il<PERSON>ita vir<PERSON>, jos on<PERSON> jatkuu."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{<PERSON><PERSON><PERSON><PERSON>n sijaintiin on voitu tallentaa dataa, joka vaikuttaa lataukseen: {locations}. Tarkista sivu incognito-ikkunassa, jotta voit estää kyseisten resurssien vaikutuksen tuloksiisi.}other{<PERSON><PERSON><PERSON><PERSON> sijainteihin on voitu tallentaa dataa, joka vaikuttaa lataukseen: {locations}. Tarkista sivu incognito-ikkunassa, jotta voit estää kyseisten resurssien vaikutuksen tuloksiisi.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Alkuperädatan tyhjentäminen aikakatkaistiin. Tarkista sivu uudelleen ja il<PERSON>ita vir<PERSON>, jos on<PERSON> jatkuu."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Vain GET-p<PERSON><PERSON><PERSON><PERSON> kautta ladattavat sivut voivat käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Välimuistiin voi tallentaa vain sivuja, joilla on tilakoodi 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome havaitsi välimuistissa yrityksen suorittaa JavaScript."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "AppBanneria pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Merkinnät estävät siirtymisvälimuistin käytön. Ota se käyttöön paikallisesti tällä laitteella osoitteessa chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Komentorivi estää siirtymisvälimuistin k<PERSON>ytön."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Riittämätön muisti estää siirtymisvälimuistin k<PERSON>ön."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Delegaatti ei tue siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Esirenderöijä estää siirtymisvälimuistin k<PERSON>ön."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON><PERSON>a ei voi tallentaa välimuistiin, koska sill<PERSON> on BroadcastChannel-esiintymä, jolla on rekisteröityjä kuuntelijoita."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> on cache-control:no-store-o<PERSON><PERSON><PERSON>, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Vä<PERSON>uisti t<PERSON><PERSON><PERSON> ta<PERSON>."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "<PERSON>vu poiste<PERSON>in v<PERSON>, jotta toinen sivu voitiin tallentaa välimuistiin."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "Liitännäisiä sisältävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "FileChooser APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "File System Access APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Medialaitteen välittäjää käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Mediasoitin oli käynnissä si<PERSON>ryttäessä pois."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "MediaSession APIa käyttävät ja toiston tilan asettavat sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "MediaSession APIa käyttävät ja toimintokäsittelijöitä asettavat sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Näytönlukuoh<PERSON>lma estää siirtymisvälimuistin k<PERSON>ö<PERSON>."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "SecurityHandleria käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Serial APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "WebAuthetication APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "WebBluetooth APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "WebUSB APIa käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Dedicated workeria tai workletia käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokumentti ei latautunut kokonaan ennen pois siirtymistä."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "Sovellusbanneri oli pä<PERSON>llä pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Salasanat oli päällä Chromessa pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-t<PERSON><PERSON> oli kesken pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer oli näkyvissä pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Messaging APIa käyttävät laajennukset estävät siirtymisvälimuistin k<PERSON>ytön."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Pitkään yhdistettyinä olleiden laajennusten pitäisi katkaista yhteys ennen siirtymisvälimuistiin siirtymistä."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Pitkään yhdistettynä ollut laajennus yritti lähettää viestejä siirtymisvälimuistissa oleville kehyksille."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Laajennukset estävät siirtymisvälimuistin käytön."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "<PERSON>vun modaalivalintaikkuna (esim. lomakkeen uudelleenlähetys tai http-salasanavalintaikkuna) oli näkyvissä pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Offline-sivu oli n<PERSON> pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "<PERSON><PERSON><PERSON> lo<PERSON> muistut<PERSON>palkki oli näkyvissä pois si<PERSON>ryttäessä."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Lupapyyntöjä oli aktiivisena pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Ponnahdusikkunoiden estotoiminto oli päällä pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot olivat n<PERSON>sä pois siirryttäessä."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "<PERSON><PERSON><PERSON><PERSON> katsoi sivun ha<PERSON> ja esti ponnahdusik<PERSON>."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Service worker a<PERSON><PERSON><PERSON><PERSON>, kun sivu oli si<PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Dokumenttivirhe estää siirtymisvälimuistin k<PERSON>ytön."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "bfcache ei voi tallentaa FencedFramesia käyttäviä sivuja."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "<PERSON>vu poiste<PERSON>in v<PERSON>, jotta toinen sivu voitiin tallentaa välimuistiin."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON><PERSON>, joille on my<PERSON><PERSON>tty pääsy median striima<PERSON>seen, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "Portaaleja käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "IdleManageria käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> on avoin IndexedDB-yhteys, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Käytetyt APIt eivät olleet soveltuvia."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> la<PERSON>ennus lisää JavaScriptin, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> la<PERSON>ennus lisä<PERSON> ty<PERSON>, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON><PERSON> virhe."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Keepalive-pyyntö estää siirtymisvälimuistin k<PERSON>ytön."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "Näppäimistön lukitusta käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | loading": {"message": "Sivu ei latautunut kokonaan ennen pois siirtymistä."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, joiden pää<PERSON>urssilla on cache-control:no-cache, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, joiden pääresurssilla on cache-control:no-store, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "<PERSON><PERSON><PERSON>inen peruttiin ennen kuin sivu voitiin palauttaa siirtymisvälimuistista."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Sivu poiste<PERSON><PERSON> v<PERSON>, koska aktiivinen verkkoyhteys vastaanotti liikaa dataa. Chrome rajoittaa välimuistissa olevien sivujen vastaanottaman datan määrää."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON><PERSON>, joiden fetch()- tai XHR-pyyntö on kesken, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "<PERSON><PERSON> poiste<PERSON>, koska aktiiviseen verkkopyyntöön liittyi u<PERSON>lleenoh<PERSON>us."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "<PERSON>vu pois<PERSON><PERSON><PERSON> v<PERSON>, koska verk<PERSON>yht<PERSON> oli auki liian pit<PERSON>n. Chrome r<PERSON> a<PERSON>a, jonka välimuist<PERSON> oleva sivu voi vastaanottaa dataa."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON><PERSON>, joilla ei ole hyväksyttävää vastausotsikkoa, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "<PERSON>irtyminen tapahtui muussa kuin pääkehyksessä."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> on keskeneräisiä indeksoituja DB-tapahtumia, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "<PERSON><PERSON><PERSON>, joiden verkkopyyntö on kesken, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "<PERSON><PERSON><PERSON>, joiden verkonhakupyyntö on kesken, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "<PERSON><PERSON><PERSON>, joiden verkkopyyntö on kesken, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "<PERSON><PERSON><PERSON>, joiden XHR-verkkopyyntö on kesken, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "PaymentManageria käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "Pikkuruutua käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | portal": {"message": "Portaaleja käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | printing": {"message": "Tulostus UI:n näyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "`window.open()` a<PERSON><PERSON> sivu<PERSON>, ja to<PERSON><PERSON> v<PERSON><PERSON><PERSON> on vii<PERSON><PERSON> si<PERSON>en, tai sivu avasi ikku<PERSON>."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Siirtymisvälimuist<PERSON> olevan sivun <PERSON><PERSON> kaatui."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Siirtymisvälimuist<PERSON> olevan sivun <PERSON>int<PERSON><PERSON><PERSON> lo<PERSON>."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Äänentallennuslupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Anturilupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Taustasynkronointia tai hakulupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "MIDI-lupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Ilmoituslupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON><PERSON>, jotka ovat pyytä<PERSON>t pääsyä tallennustilaan, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Videontallennuslupia pyytäneet sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Välimuistiin voi tallentaa vain sivuja, joiden URL-malli on HTTP/HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "Service worker p<PERSON><PERSON><PERSON> si<PERSON>, kun se oli si<PERSON>."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Service worker y<PERSON><PERSON> l<PERSON>ttä<PERSON> siirtymisvälimuistissa olevalle sivulle tämän: `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "ServiceWorkerin rekis<PERSON>ö<PERSON>ii<PERSON>, kun sivu oli si<PERSON>."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Sivu poiste<PERSON>in si<PERSON>ymisvälimuistista service workerin aktivoitumisen takia."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome käynnistyi uudelleen ja tyhjensi siirtymisvälimuistin."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "SharedWorkeria käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "SpeechRecognizeria käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "SpeechSynthesisia käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "<PERSON><PERSON><PERSON> oleva if<PERSON>e al<PERSON>, joka ei on<PERSON>."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON><PERSON>, joid<PERSON> on cache-control:no-cache, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON><PERSON>, joid<PERSON> al<PERSON> on cache-control:no-store, eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | timeout": {"message": "<PERSON>vu ylitti en<PERSON><PERSON><PERSON><PERSON> si<PERSON> ja van<PERSON>."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Sivu a<PERSON>kat<PERSON>stiin, kun se oli siirtymässä siirtymisvälimuistiin (tämä johtuu luultavasti pitkäkestoisista sivun piilotuksen käsittelijöistä)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Sivun pääkehyksessä on tyhjennyksen käsittelijä."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "<PERSON><PERSON><PERSON> alakehyksessä on tyhjennyksen käsittelijä."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "<PERSON><PERSON> on muutt<PERSON>t k<PERSON>yttäjäagentin ohitusotsik<PERSON>a."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON><PERSON>, joille on annettu lupa tallentaa videota tai ääntä, eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "WebDatabasea käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webHID": {"message": "WebHID:iä käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "WebLocksia käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "WebNfc:tä käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "WebOTPServiceä käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "WebRTC:tä käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webShare": {"message": "WebSharea käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "WebSocketia käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "WebTransportia käyttävät sivut eivät voi käyttää siirtymisvälimuistia."}, "core/lib/bf-cache-strings.js | webXR": {"message": "WebXR:ää käyttävät sivut eivät voi tällä hetkellä käyttää siirtymisvälimuistia."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Harkitse https:- ja http:-URL-o<PERSON><PERSON><PERSON> kaavojen (joita \"strict-dynamicia\" tukevat selaimet ohittavat) lisäämistä, niin voit saavuttaa taaksepäinyhteensopivuuden vanhempien selainten kanssa."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "CSP3:sta lähtien disown-opener on poistettu käytöstä. Käytä sen sijaan Cross-Origin-Opener-Policy-ylätunnistetta."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "CSP2:sta lähtien referrer on poistettu käytöstä. Käytä sen sijaan Referrer-Policy-ylätunnistetta."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "CSP2:sta lähtien reflected-xss on poistettu käytöstä. Käytä sen sijaan X-XSS-Protection-ylätunnistetta."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Puuttuva base-uri sallii injektoitujen <base>-tagien määrittää kaikkien suhteellisten URL-osoitteiden (esim. skriptien) pohja-URL-osoitteen hyökkääjän hallitsemaan verkkotunnukseen. <PERSON><PERSON> kannattaa valita base-urin arvoksi \"none\" tai \"self\"."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Puuttuva object-src sallii riskialttiita skriptejä suorittavien liitännäisten injektoinnin. <PERSON>un kannattaa määrittää object-src:n arvo<PERSON>i \"none\", jos <PERSON><PERSON><PERSON><PERSON>."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "script-src-s<PERSON><PERSON><PERSON><PERSON> puuttuu. Tämä mahdollistaa vaarallisten skrip<PERSON> su<PERSON>n."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Unohditko puolipisteen? Näyttää siltä, että {keyword} on sääntö eikä a<PERSON>."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Noncesien on käytettävä base64-merkistöä."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Noncesien on oltava vähintään kahdeksan merkkiä."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Vältä tavallisten URL-mallien ({keyword}) käyttämistä tässä säännössä. Tavalliset URL-mallit sallivat skriptit, jotka ovat peräisin riskialttiista verkkotunnuksista."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Vältä tavallisten jokerimerkkien ({keyword}) käyttämistä tässä säännössä. Tavalliset jokerimerkit sallivat skriptit, jotka ovat peräisin riskialttiista verkkotunnuksista."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Raportointimääränpään voi määrittää vain report-to-s<PERSON><PERSON><PERSON>ö<PERSON> kautta. Vain Chromium-pohjaiset selaimet tukevat kyseistä sääntöä, joten on suositeltavaa käyttää myös report-uri-sääntöä."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Raportointimääränpäätä määrittävää CSP:tä ei ole. Tämän takia on hankala säilyttää CSP:tä ajan mittaa ja seurata vaurioita."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Is<PERSON>nnän sallitut luettelot ovat usein ohitettavissa. <PERSON>un kannattaa ehkä käyttää kertakäyttöisiä CSP-nonceja tai ‑hasheja \"strict-dynamic\"-koodin lisäksi."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Tuntematon CSP-sääntö."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> siltä, ett<PERSON> {keyword} on virheellinen avainsana."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inlinen\" avulla voidaan suorittaa vaarallisia sivun sisäisiä skriptejä ja tapahtumien käsittelijöitä. Harkitse skriptien salliminen yksitellen käyttämällä CSP-nonceja tai ‑hasheja."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Harkits<PERSON> \"unsafe-inlinen\" (jonka nonceja/hasheja tukevat selaimet ohittavat) lisäämistä, niin voit saavuttaa taaksepäinyhteensopivuuden vanhempien selainten kanssa."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Jokerimerkki (*) CORS `Access-Control-Allow-Headers` ‑käsittelyssä ei kata valtuutusta."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> URL-osoitteet sisälsivät sekä poistettuja tyhjätilamerkkejä (`(n|r|t)`) että pienempi kuin ‑merkkejä (`<`) on estetty. Poista newline-koodit ja koodaa pienempi kuin ‑merkkejä esimerkiksi elementin attribuuttiarvoista, jos haluat ladata näitä resursseja."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` on poistettu käytöstä, käytä sen sijaan standardoitua APIa: Navigation Timing 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` on poistettu käytöstä, käytä sen sijaan standardoitua APIa: <PERSON><PERSON>."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` on poistettu käytöstä, käytä sen sijaan standardoitua APIa: `nextHopProtocol` Navigation Timing 2:ssa."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "`(0|r|n)` ‑merkin sisältävät evästeet hylätään lyhentämisen sijaan."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Saman alkuperän käytännön lieventäminen määrittämällä `document.domain` on poistettu käytöstä eikä näin ollen ole käytössä oletuksena. Tämä käytöstäpoistovaroitus koskee eri lähteistä peräisin olevaa pääsyoikeutta, joka on otettu käyttöön asetuksella `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Kohteen {PH1} käynnistäminen eri lähteistä peräisin olevista iframeista on poistettu käytöstä ja poistetaan kokonaan tuleva<PERSON>udessa."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "`disableRemotePlayback` ‑attribuuttia pitäisi käyttää oletusarvoisen Cast-integraation käytöstä poistamiseen `-internal-media-controls-overlay-cast-button` ‑valits<PERSON>n sijaan."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} on poistettu käytöstä. Käytä sen sijaan tätä: {PH2}."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "T<PERSON>mä on esimerkki käännetystä viestistä, joka kertoo käytöstä poistamiseen liittyvästä ongelmasta."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Saman alkuperän käytännön lieventäminen määrittämällä `document.domain` on poistettu käytöstä eikä näin ollen ole käytössä oletuksena. Jos haluat jatkaa tämän ominaisuuden käyttöä, poista käytöstä alkuperään sidotut agenttiklusterit lähettämällä `Origin-Agent-Cluster: ?0` ‑otsikko dokumentin ja kehysten HTTP-vastauksen mukana. Lue lisää osoitteesta https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` on poistettu käytöstä ja poistetaan kokonaan. Käytä sen sijaan tätä: `Event.composedPath()`."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "<PERSON><PERSON><PERSON><PERSON> (`Expect-CT`) on poistettu käytöstä ja poistetaan kokonaan. Chrome edellyttää Certificate Transparencya kaikille 30.4.2018 jälkeen myönnetyille julkisesti luotetuille varmenteille."}, "core/lib/deprecations-strings.js | feature": {"message": "Lisätietoa löydät ominaisuuden tilasivulta."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` ja `watchPosition()` eivät enää toimi suojaamattomien alkuperien kohdalla. <PERSON><PERSON> haluat käyttää tätä ominaisuutta, suos<PERSON><PERSON><PERSON> vai<PERSON> sovelluksen turvalliseen alkuperään, esim. HTTPS:ään. Lue lisää osoitteesta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` ja `watchPosition()` on poistettu käytöstä suojaamattomien alkuperien kohdalla. <PERSON><PERSON> haluat käyttää tätä ominaisuutta, suos<PERSON><PERSON><PERSON> vai<PERSON> sovelluksen turvalliseen alkuperään, esim. HTTPS:ään. Lue lisää osoitteesta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ei enää toimi suojaamattomien alkuperien kohdalla. <PERSON><PERSON> halu<PERSON> käyttä<PERSON> tätä ominaisu<PERSON>, suos<PERSON><PERSON><PERSON> vaihta<PERSON> sovelluksen turvalliseen alkuperään, esim. HTTPS:ään. Lue lisää osoitteesta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` on poistettu käytöstä. Valitse sen sijaan `RTCPeerConnectionIceErrorEvent.address` tai `RTCPeerConnectionIceErrorEvent.port`."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Ka<PERSON>piaan alkuperän ja satunnaisen datan määrä service worker ‑tapahtumasta (`canmakepayment`) on poistettu käytöstä, ja se poistetaan seuraavista: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Verkkosivusto pyysi aliresurssia verkosta, johon sillä oli pääsy vain käyttäjiensä oikeutetun verkkoaseman vuoksi. Nämä pyynnöt altistavat ei-julkisia laitteita ja palvelimia näkyville internetiin, mikä lisää sivustojen välisten pyyntöjen väärennykseen (CSRF) liittyvän hyökkäyksen ja/tai tietovuodon riskiä. Pienentääkseen riskiä Chrome poistaa käytöstä ei-julkisille aliresursseille osoitetut pyynnöt, jos ne ovat peräisin suojaamattomista konteksteista, ja alkaa estää niitä."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS:ää ei voi ladata `file:` ‑URL-osoitteista, elleivät ne pääty `.css` ‑tiedostotunnisteeseen."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Kohteen `SourceBuffer.abort()` k<PERSON><PERSON><PERSON><PERSON> kohteen `remove()` asynkronisen välin poistamiseen on poistettu käytöstä määritysmuutoksen vuoksi. <PERSON><PERSON> poistetaan tulevaisuudessa. <PERSON><PERSON><PERSON> sen sijaan tap<PERSON> `updateend`. `abort()` on tarkoitettu ainoastaan asynkronisen medialiitteen tai nollausjäsentäjän tilan keskeytykseen."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Kohteen `MediaSource.duration` asettaminen puskuroidun koodatun kehyksen korkeinta esitysaikaleimaa alemmas on poistettu käytöstä määritysmuutoksen vuoksi. Lyhennetyn puskuroidun median implisiittisen poiston tuki poistetaan tulevaisuudessa. Tee sen sijaan eksplisiittinen `remove(newDuration, oldDuration)` kaikkien `sourceBuffers` ‑kohte<PERSON> kohdalla, joissa `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "<PERSON><PERSON><PERSON><PERSON> muutos tulee voi<PERSON>an, kun versionumero {milestone} saavutetaan."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI pyytää k<PERSON>, vaikka `MIDIOptions` ei ole määrittänyt sysexiä."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Notification APIa ei saa enää käyttää suojaamattomista alkuperistä. Suosittelemme vaihtamaan sovelluksen turvalliseen alkuperään, esim. HTTPS:ään. Lue lisää osoitteesta https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Notification APIn lupaa ei välttämättä enää pyydetä eri lähteistä peräisin olevalta iframelta. Suosittelemme sen sijaan pyytämään lupaa ylätason kehykseltä tai avaamaan uuden ikkunan."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Yhteistyökumppanisi neuvottelee vanhentuneesta (D)TLS-versiosta. Pyydä yhteistyökumppaniasi korjaamaan tämä."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL on poistettu käytöstä suojaamattomissa konteksteissa ja se poistetaan pian. Käytä verkkotallennustilaa tai hakemistoon lisättyä tietokantaa."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Jos `overflow: visible` määritetään img-, video- ja canvas-tagei<PERSON>, ne voivat tuottaa visuaalista sisältöä elementtirajojen ulkopuolella. Lue lisää osoitteesta https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` on poistettu käytöstä. Käytä maksujen käsittelijöille sen sijaan heti asenne<PERSON> (JIT) APIa."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest`-kutsu ohitti Content-Security-Policy (CSP) ‑käytännön `connect-src`-säännön. Tämä ohitus on poistettu käytöstä. Lisää maksutavan tunniste `PaymentRequest` APIsta (`supportedMethods`-kentässä) CSP-käytännön `connect-src`-sääntöön."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` on poistettu käytöstä. Valitse sen sijaan standardoitu `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>`, j<PERSON>a on `<picture>`-<PERSON><PERSON><PERSON><PERSON><PERSON>, on virheellinen ja jätetään näin ollen huomiotta. Käytä sen sijaan tätä: `<source srcset>`."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` on poistettu käytöstä. Valitse sen sijaan standardoitu `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joiden URL-osoitteet sisältävät upotettuja kirjautumistietoja (esim. `**********************/`), on estetty."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "<PERSON><PERSON><PERSON> `DtlsSrtpKeyAgreement` on poistettu. <PERSON><PERSON> `false` ‑arvon tä<PERSON>, mik<PERSON> tulkitaan yrityksenä käyttää poistettua `SDES key negotiation` ‑metodia. Täm<PERSON> toiminto on poistettu. Käytä sen sijaan palvelua, joka tukee tätä: `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "<PERSON><PERSON><PERSON> `DtlsSrtpKeyAgreement` on poistettu. <PERSON><PERSON> m<PERSON> `true` ‑arvon tälle raj<PERSON>, jolla ei ollut mit<PERSON>än vai<PERSON>, mutta voit poistaa rajoit<PERSON>sen siisteyden vuoksi."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` havaittu. Tätä `Session Description Protocol` ‑murretta ei enää tueta. Käytä sen sijaan tätä: `Unified Plan SDP`."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, jota käytetään kohteen `RTCPeerConnection` rakentamiseen kohteella `{sdpSemantics:plan-b}`, on vanha, ei-standardiversio kohteesta `Session Description Protocol`, joka on poistettu pysyvästi verkkoalustalta. Se on edelleen käytettävissä, kun `IS_FUCHSIA` on käytössä rakentamisessa, mutta aiomme poistaa sen mahdollisimman pian. Älä enää laske sen varaan. Katso sen tila osoitteesta https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "`rtcpMuxPolicy` ‑vaihtoehto on poistettu käytöstä ja poistetaan kokonaan."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` edellyttää muista lähteistä eristämistä. Lue lisää osoitteesta https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` ilman k<PERSON>j<PERSON>n aktivointia on poistettu käytöstä ja poistetaan koko<PERSON>an."}, "core/lib/deprecations-strings.js | title": {"message": "Käytöstä poistettua ominaisuutta kä<PERSON>tty"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Laajennusten pitäisi ottaa muista lähteistä eristäminen käyttöön, jotta ne voivat jatkaa tämän käyttöä: `SharedArrayBuffer`. Lue lisää osoitteesta https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} on myyjäkohtainen. Käytä sen sijaan {PH2} ‑standardiversiota."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "Vastaus-JSON ei tue UTF-16:ta kohteessa `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Pääsäikeen samanaikainen `XMLHttpRequest` on poistettu k<PERSON>ä, koska sillä oli haitallisia vaikutuksia loppukäyttäjän kokemukseen. Lue lisäohjeita osoitteesta https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` on poistettu käytöstä. <PERSON>ta sen sijaan `isSessionSupported()` käyttöön ja tarkista ratkaistu looginen arvo."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Pääsäikeen estoaika"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Vä<PERSON>ui<PERSON>in k<PERSON>ö<PERSON>"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Hylätyt elementit"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> budjetin"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON> koko"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSize": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Lä<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Al<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "<PERSON><PERSON><PERSON><PERSON> aika"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "<PERSON><PERSON><PERSON> koko"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL-osoite"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Potentiaalinen säästö"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Potentiaalinen säästö"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Potentiaalinen säästö: {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 elementti löydetty}other{# elementtiä löydetty}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Potentiaalinen säästö: {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Do<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "Ensimmäinen merkityksellinen renderöinti"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaktiosta <PERSON>"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "<PERSON><PERSON><PERSON> ma<PERSON> ensimmäisen toiminnon viive"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON> resurs<PERSON>t"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Tyylisivu"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Yhteensä"}, "core/lib/lh-error.js | badTraceRecording": {"message": "<PERSON><PERSON><PERSON> la<PERSON> jäljen tallennuks<PERSON>a tap<PERSON>i virhe. Suorita Lighthouse uudelleen. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Aikakatkaisu: odotetaan yhteyttä virheenkor<PERSON>usprotokollaan"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome ei kerännyt kuvakaappauksia sivun latautumisen aikana. Varmista, että sisältö näkyy sivulla, ja suorita sitten Lighthouse uudelleen. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "DNS-palvelimet eivät voineet ratkaista verkkotunnusta."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "Vaa<PERSON><PERSON> {artifactName}-k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kohtasi virheen: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Tapahtui sisäinen Chrome-virhe. Käynnistä Chrome uudelleen ja yritä suorittaa Lighthouse sen jälkeen."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Vaadittua {artifactName}-ker<PERSON><PERSON><PERSON>imintoa ei suoritettu."}, "core/lib/lh-error.js | noFcp": {"message": "Sivu ei renderöinyt mitään sisältöä. <PERSON>d<PERSON> selainikkuna etualalla latauksen aikana ja yritä uudelleen. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Sivulla ei näkynyt si<PERSON>ältöä, joka täyttää sivun latautumi<PERSON> (LCP) ehdot. Varmista, että sivulla on kelvollinen LCP-elementti, ja yritä sitten uudelleen. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Sivu ei ole HTML-muodossa (MIME-tyyppi on {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Tämä Chrome-versio ei tue ominaisuutta ({featureName}). Käytä uudempaa versiota, jotta näet tulokset kokonai<PERSON>udessa<PERSON>."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse ei pystynyt lata<PERSON>an pyytämääsi sivua luotetta<PERSON>. Varmista, että testaat oikeaa URL-osoitetta ja että palvelin vastaa kunnolla kaikkiin pyyntöihin."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse ei pystynyt lataamaan pyytämääsi URL-osoitetta luo<PERSON>, koska sivu lakkasi vastaa<PERSON>ta."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Ilmoittamasi URL-osoitteen suojausvarmenne ei ole kelvollinen. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome esti sivun lata<PERSON>sen välimainoksella. Varmista, että testaat oikeaa URL-osoitetta ja että palvelin vastaa kunnolla kaikkiin pyyntöihin."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse ei pystynyt lata<PERSON>an pyytämääsi sivua luotettavasti. Varmista, että testaat oikeaa URL-osoitetta ja että palvelin vastaa kunnolla kaikkiin pyyntöihin. (Tiedot: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse ei pystynyt lata<PERSON>an pyytämääsi sivua luotetta<PERSON>ti. Varmista, että testaat oikeaa URL-osoitetta ja että palvelin vastaa kunnolla kaikkiin pyyntöihin. (Tilakoodi: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Sivun lataaminen kesti liian kauan. Lyhennä sivun latausaikaa raportin ehdotusten mukaisesti ja yritä suorittaa Lighthouse sen jälkeen. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "DevTools-<PERSON><PERSON><PERSON><PERSON> odo<PERSON> on ylittänyt sille lasketun ajan. (Tapa: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Resurssisisällön hakeminen on ylittänyt sille varatun ajan"}, "core/lib/lh-error.js | urlInvalid": {"message": "Kirjoittamasi URL-osoite näyttää olevan virheellinen."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Sivun MIME-tyyppi on XHTML: Lighthouse ei erikseen tue tätä dokumenttityyppiä"}, "core/user-flow.js | defaultFlowName": {"message": "K<PERSON>yttökulku ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Navigointiraportti ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kategoriat"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Parhaat käytännöt"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressiivinen web-sovellus"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "T<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Lighthousen käyttökulkuraportin tulkitseminen"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Tietoja käyttökuluista"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Käytä navigointiraportteja näihin tarkoituksiin:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Käytä Snapshotia näihin tarkoituksiin:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Käytä aikajanaraportteja näihin tarkoituksiin:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Nouda Lighthouse-suorituskykyprosentti."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON><PERSON> sivulatauksia sivun latautumisajan ja nopeusindek<PERSON> kaltaisilla mittareilla."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Arvioi progressiivisten web-sovellusten ominaisuuksia."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Löydä esteettömyysongelmia yhden sivun sovelluksista tai monimutkaisista muodoista."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "<PERSON><PERSON><PERSON><PERSON> toim<PERSON>an taakse pii<PERSON>n valikoiden ja UI-elementtien parhaita k<PERSON>äntöjä."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "<PERSON><PERSON><PERSON> as<PERSON><PERSON> muuto<PERSON> ja JavaScriptin suoritusai<PERSON> toimintasarjoissa."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON> voit parantaa pitkäaikaisten sivujen ja yhden sivun sovellusten käyttökokemusta."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON> v<PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informatiivinen tarkastus}other{{numInformative} informatiivista tarkastusta}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON><PERSON><PERSON> la<PERSON>n"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Navigointiraporteissa analysoidaan yksi sivun lataus, aivan kuten alkuperäisissä Lighthouse-raporteissa."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Navigointiraportti"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} navigointiraportti}other{{numNavigation} navigointiraporttia}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} läpäistävissä oleva tarkastus}other{{numPassableAudits} läpäistävissä olevaa tarkastusta}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} tarkast<PERSON> l<PERSON>}other{{numPassed} tarkast<PERSON>a läp<PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Keskimääräinen"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Hyvä"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON><PERSON><PERSON> k<PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Snapshot-raporteissa analysoidaan sivua tietyssä tilassa, y<PERSON><PERSON><PERSON> k<PERSON>äjien to<PERSON><PERSON><PERSON> j<PERSON>."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} til<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}other{{numSnapshot} tilanne<PERSON><PERSON><PERSON><PERSON><PERSON>}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Yhteenveto"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toim<PERSON>a"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Aikaväliraporteissa analysoidaan satunnainen a<PERSON>, joka y<PERSON> sisältää käyttäjien toimintaa."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Ai<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} aikaväliraportti}other{{numTimespan} aikaväliraporttia}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Lighthousen käyttökulkuraportti"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Käytä animoidun sisällön kanssa [`amp-anim`](https://amp.dev/documentation/components/amp-anim/)-o<PERSON><PERSON><PERSON><PERSON><PERSON>, joka minimoi suori<PERSON>äytö<PERSON>, kun sisältö ei ole näytöllä."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Harkitse kaikkien [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponenttien näyttämistä WebP-muodoissa ja lisää varavaihtoehto muita selaimia varten. [<PERSON><PERSON> lisää](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Varmista, ett<PERSON> käyt<PERSON> on [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites), jotta kuvien lataamista lykätään automaattisesti. [<PERSON>e lisää](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON> [AMP-optimoijaa](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) ja muita työkaluja [AMP-asettelujen palvelinpuolen renderöintiin](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON> [AMP-dokumentaatio](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) ja varmista, että kaikkia tyylejä tuetaan."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "[`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponentti tukee [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/)-mä<PERSON>rite<PERSON><PERSON>, jolla määritetään käytettävä kuvasisältö näytön koon perusteella. [<PERSON><PERSON> lisää](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Harkitse virtuaalivieritystä Component <PERSON> (CDK), jos la<PERSON><PERSON> listoja renderöidää<PERSON>. [<PERSON>e lisää](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Minimoi JavaScript-nippu<PERSON><PERSON> koko [reitt<PERSON><PERSON> koodinjak<PERSON>](https://web.dev/route-level-code-splitting-in-angular/). Harkitse myös sisällön tallentamista välimuistiin etukäteen [Angular-service workerilla](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Jos käytät Angular CLI:tä, varmista että versiot luodaan tuotantotilassa. [<PERSON><PERSON> lisää](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Jos käytät Angular CLI:tä, lisää tuotantoversioosi lähdekartat nippujen tarkistamista varten. [Lue lisää](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> reittejä ennalta navigoinnin nopeuttamiseksi. [<PERSON><PERSON> lisä<PERSON>](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Harkitse `BreakpointObserver` ‑avustajan käyttöä Component Dev <PERSON> (CDK) kuvien raja-arvojen hallintaan. [<PERSON><PERSON> lisää](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "GIF kannattaa ehkä ladata p<PERSON>, jonka avulla se <PERSON>aan upottaa HTML5-videona."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Valitse `@font-display`, kun määrität teemaasi yksilöityjä fontteja."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Harkitse [WebP-kuvamuotojen määrittämistä Ku<PERSON> muuntaminen ‑tyylillä](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) sivustollasi."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> [Drupal-moduuli](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), joka voi ladata kuvia tarveohjatusti. Tällaiset moduulit voivat parantaa toimintaa lykkäämällä näytön ulkopuolella olevien kuvien lataamista."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Sinun kannattaa ehkä käyttää moduulia, joka voi tuoda kriittistä CSS:ää tai JavaScriptiä sivun sisälle tai mahdollisesti ladata sisältöä asynkronisesti JavaScriptin avulla (esim. [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg) ‑moduuli). <PERSON><PERSON><PERSON>an, että tämän moduulin suorittamat optimoinnit voivat rikkoa sivustosi, joten sinun on todennäköisesti muutettava koodia."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, moduulit ja palvelinasetukset vaikuttavat kaikki palvelimen vastausaikaan. <PERSON>un kannattaa ehkä etsiä optimoidumpi teema, valita optimointimoduuli tai päivittää palvelimesi. Hosting-palvelimiesi olisi hyvä käyttää PHP-toimintokoodin ja muun sisällön tallentamista välimuistiin, mikä auttaa lyhentämään tietokantojen kyselyaikoja (esim. Redis tai Memcached). Lisäksi niiden tulee käyttää optimoitua sovelluslogiikkaa sivujen nopeampaan valmisteluun."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Sinun kannattaa ehkä käyttää [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (Responsiiviset kuvatyylit) ‑toimintoa sivullasi ladattavien kuvien pienentämiseen. Jos näytät sivulla useita kohteita Viewsin avulla, sinun kannattaa ehkä rajoittaa yhdellä sivulla näkyvien kohteiden määrää ottamalla sivunumerointi käyttöön."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Varmista, ett<PERSON> Aggregate CSS files (Kokoa CSS-tiedostot) ‑toiminto on otettu käyttöön kohdassa Administration > Configuration > Development (Järjestelmänvalvonta > Määritys > Kehitys). Voit myös määrittää lisää koontiasetuksia käyttämällä [lisämoduuleja](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search), mikä voi nopeuttaa sivustosi toimintaa ketjuttamalla, pienentämällä ja pakkaamalla CSS-tyylejä."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Varmista, että Aggregate JavaScript files (Kokoa JavaScript-tiedostot) ‑toiminto on otettu käyttöön kohdassa Administration > Configuration > Development (Järjestelmänvalvonta > Määritys > Kehitys). Voit myös määrittää lisää koontiasetuksia käyttämällä [lisämoduuleja](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search), mikä voi nopeuttaa sivustosi toimintaa ketjuttamalla, pienentämällä ja pakkaamalla JavaScript-sisältöä."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Sinun kannattaa ehkä poistaa käyttämättömät CSS-säännöt ja liittää relevanttiin sivuun tai sivun osaan vain tarvittavat Drupal-kirjastot. Saat lisätietoja [Drupal-dokumentaatiosta](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Voit etsiä tarpeetonta CSS:ää lisääviä liitettyjä kirjastoja tutkimalla [koodin testikattavuutta](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Löydät syynä olevan teeman tai moduulin tarkistamalla tyyliarkin URL-osoitteen, kun CSS-koonti on poistettuna käytöstä Drupal-sivustollasi. Etsi teemoja ja moduuleja, j<PERSON><PERSON> on monia tyyliarkkeja luettelossa ja paljon punaista koodin testikattavuudessa. Teeman tai moduulin pitäisi lisätä tyyliarkki jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Sinun kannattaa ehkä poistaa käyttämätön JavaScript-sisältö ja liittää relevanttiin sivuun tai sivun osaan vain tarvittavat Drupal-kirjastot. Saat lisätietoja [Drupal-dokumentaatiosta](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Voit etsiä tarpeetonta JavaScriptiä lisääviä liitettyjä kirjastoja tutkimalla [koodin testikattavuutta](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Löydät syynä olevan teeman tai moduulin tarkistamalla skriptin URL-osoitteen, kun JavaScript-koonti on poistettuna käytöstä Drupal-sivustollasi. Etsi teemoja ja moduuleja, joil<PERSON> on monia skriptejä luettelossa ja paljon punaista koodin testikattavuudessa. Teeman tai moduulin pitäisi lisätä skripti jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Määrit<PERSON> Browser and proxy cache maximum age (Selaimen ja välityspalvelimen välitysmuistin enimmäisikä) ‑asetus kohdassa Administration > Configuration > Development (Järjestelmänvalvonta > Määritys > Kehitys). <PERSON>e lisää [<PERSON><PERSON><PERSON><PERSON> välimuistista ja toiminnan optimoinnista](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "<PERSON>un kannattaa ehkä käyttää [moduulia](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), joka optimoi ja pienentää sivuston kautta ladattuja kuvia, mutta säilyttää niiden laadun. Varmista lisäksi, että käytät kaikkien sivustolla renderöitävien kuvien kanssa Drupalin natiivia [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (Responsiiviset kuvatyylit) ‑toimintoa, joka on saatavilla Drupalin versiosta 8 alkaen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Voit lisätä preconnect- tai dns-prefetch-resurssivihjeitä asentamalla ja määrittämällä [moduulin](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), joka edesauttaa käyttäjäagentin resurssivihjeiden toimintaa."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Varmista, että käytät Drupalin natiivia [Responsive Image Styles](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) (Responsiiviset kuvatyylit) ‑toimintoa, joka on saatavilla Drupalin versiosta 8 alkaen. Käytä Responsive Image Styles ‑toimintoa, kun renderöit kuvakenttiä hyödyntämällä katselutiloja, näkymiä tai WYSIWYG-muokkaustyökalulla ladattuja kuvia."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Optimize Fonts` käyttöön hyödyntääksesi `font-display` CSS ‑ominaisuutta automaattisesti, jotta voit varmistaa tekstin näkymisen käyttäjille verkkofonttien latautuessa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "K<PERSON>yt<PERSON> [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Next-Gen Formats` käyttöön konvertoidaksesi kuvia WebP-muotoon."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "K<PERSON>yt<PERSON> [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `<PERSON>zy <PERSON>ad Images` käyttöön lykätäksesi näytön ulkopuolisten kuvien lataamista, kunnes niitä tarvita<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "<PERSON><PERSON><PERSON><PERSON> [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Critical CSS` ja `<PERSON><PERSON>t Delay` käyttöön lykätäksesi ei-kriittiset JS/CSS:t."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Käytä [Ezoic Cloud Cachingia](https://pubdash.ezoic.com/speed/caching) sisältösi tallentamiseen välimuistiin maailmanlaajuisessa verkossamme, mikä lyhentää aikaa linkin valinnan ja sisällön ensimmäisen tavun latauksen välillä"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Minify CSS` käyttöön pienentääksesi CSS:ää automaattisesti, jotta voit vähentää verkkoresurssien kokoja."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Minify Javascript` käyttöön pienentääksesi JS:ää automaattisesti, jotta voit vähentää verkkoresurssien kokoja."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Remove Unused CSS` käyttöön saadaksesi apua ongelmaan. Se tunnistaa jokaisen sivustosi sivun käyttämät CSS-luokat ja poistaa kaikki muut, jotta tiedoston koko pysyy pienen<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Efficient Static Cache Policy` käyttöön asettaaksesi suositellut arvot välimuistin otsikossa pysyville sisällöille."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "K<PERSON>yt<PERSON> [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Next-Gen Formats` käyttöön konvertoidaksesi kuvia WebP-muotoon."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "K<PERSON>ytä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Pre-Connect Origins` käyttöön lisätäksesi automaattisesti `preconnect`-materiaalivihjeitä, jotta voit muodostaa aikaisia yhteyksiä tärkeisiin kolmannen osapuolen alkuperiin."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "K<PERSON><PERSON>ä [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Preload Fonts` ja `Preload Background Images` käyttöön lisätäksesi`preload`‑linkit, jotta voit priorisoida nykyisten pyydettyjen resurssien hakemista myöhemmin sivun latauksessa."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "K<PERSON>yt<PERSON> [Ezoic Leapia](https://pubdash.ezoic.com/speed) ja ota `Resize Images` käyttöön muuttaaksesi kuvien kokoa laitteisiin sopivaksi, jotta voit vähentää verkkoresurssien kokoja."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "GIF kannattaa ehkä ladata p<PERSON>, jonka avulla se <PERSON>aan upottaa HTML5-videona."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "<PERSON>un kannattaa ehkä käyttää [laaj<PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) tai palvelua, joka muuntaa ladatut kuvat automaattisesti oikeisiin muotoihin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> [<PERSON><PERSON><PERSON> laisk<PERSON> lataut<PERSON> liitännäinen](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), joka lykkää muiden kuin näytöllä näkyvien kuvien latautumista, tai vaihda malliin, joka tarjoaa tämän ominaisuuden. Joomla 4.0:sta alkaen kaikille kuville lisätään [automaattisesti](https://github.com/joomla/joomla-cms/pull/30748) `loading`-ydinmäärite."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Tietyt Joomla-laajennukset voivat [tuoda tärkeää sisältöä sivun sisälle](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) tai [lykätä vähemmän tärkeiden resurssien lataamista](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Huomaa, että näiden laajennusten suorittamat optimoinnit voivat rikkoa mallien tai muiden laajennusten toimintoja, joten sinun on testattava ne huolellisesti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "<PERSON><PERSON>, laaj<PERSON>nukset ja palvelinasetukset vaikuttavat kaikki palvelimen vastausaikaan. <PERSON>un kannattaa ehkä etsiä optimoidumpi malli, valita optimointilaajennus tai päivittää palvelimesi."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "<PERSON>un kannattaa ehkä näyttää näytteitä artikkeliluokistasi (esim. lue lisää ‑linkillä), vähentää yhdellä sivulla näkyvien artikkelien määrää, jakaa pitkät postaukset useille sivuille tai käyttää laajennusta kommenttien lataamiseen tarveohjatusti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Tietyt [Joomla-laajennukset](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) voivat nopeuttaa sivustosi toimintaa ketjuttamalla, pienentämällä ja pakkaamalla CSS-tyylejä. Nämä toiminnot sisältyvät myös joihinkin malleihin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Tietyt [Joomla-laajennukset](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) voivat nopeuttaa sivustosi toimintaa ketjuttamalla, pienentämällä ja pakkaamalla skriptejä. Nämä toiminnot sisältyvät myös joihinkin malleihin."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "<PERSON>un kannattaa ehkä vähentää tai vaihtaa [<PERSON><PERSON><PERSON>-laajennuksia](https://extensions.joomla.org/), jotka lataavat käyttämätöntä CSS:ää sivullasi. Etsi tarpeetonta CSS:ää lisääviä laajennuksia tutkimalla [koodin testikattavuutta](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Löydät syynä olevan teeman tai laajennuksen tarkistamalla tyyliarkin URL-osoitteen. Etsi laajennuksia, joilla on monia tyyliarkkeja luettelossa ja paljon punaista koodin testikattavuudessa. Laajennuksen pitäisi lisätä tyyliarkki jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "<PERSON>un kannattaa ehkä vähentää tai vaihtaa [<PERSON><PERSON><PERSON>-laajennuksia](https://extensions.joomla.org/), jotka lataavat käyttämätöntä JavaScriptiä sivullasi. Etsi tarpeetonta JS:ää lisääviä laajennuksia tutkimalla [koodin testikattavuutta](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Löydät syynä olevan laajennuksen tarkistamalla skriptin URL-osoitteen. Etsi laajennuksia, joilla on monia skriptejä luettelossa ja paljon punaista koodin testikattavuudessa. Laajennuksen pitäisi lisätä skripti jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "<PERSON>e lis<PERSON> [selaimen välimuistin käyt<PERSON><PERSON><PERSON>](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "<PERSON>un kannattaa ehkä käyttää [kuvaoptimointilaajennusta](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), joka pakkaa kuvat mutta säilyttää niiden laadun."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "<PERSON>un kannattaa ehkä käyttää [responsiivisten kuvien laajennusta](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), jolloin voit käyttää responsiivisia kuvia sisällössäsi."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Voit ottaa tekstin pak<PERSON> käyttöön la<PERSON>amalla Gzip Page Compression (Sivun Gzip-pakkaus) ‑toiminnon päälle Joomlassa. Valitse System > Global configuration > Server (Järjestelmä > Yleiset asetukset > Palvelin)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Jo<PERSON> et vielä kokoa JavaScript-sisältöä nipuiksi, harkitse[niputtajan (baler)](https://github.com/magento/baler) käyttöä."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Poista käytöstä Magenton sisäänrakennettu [JavaScriptin niputus ja pienennys](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ja harkitse sen sijaan [niputtajan](https://github.com/magento/baler/) käyttöä."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Valitse `@font-display`, kun[määrität omia kirjasimia](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Etsi [Magento Marketplacesta](https://marketplace.magento.com/catalogsearch/result/?q=webp) erilaisia kolmannen osapuolen laajennuksia uusien kuvamuotojen hyödyntämiseksi."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "<PERSON>ok<PERSON><PERSON> tuote- ja kuvas<PERSON> niin, että ne hyödyntävät verkkoalustan [lyk<PERSON><PERSON> lataus](https://web.dev/native-lazy-loading) ‑ominaisuutta."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> Ma<PERSON> [Varnish-integraatiota](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "<PERSON><PERSON> kaupan kehittäjäasetuksista käyttöön vaihtoehto Pienennä CSS-tiedostot. [<PERSON><PERSON> lisä<PERSON>](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Pienennä kaikki <PERSON>-sis<PERSON><PERSON><PERSON> [Terserillä](https://www.npmjs.com/package/terser) staattisen julkaisun vaiheesta ja poista sisäänrakennettu pienennysominaisuus käytöstä."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Poista käytöstä Magenton sisäänrakennettu [JavaScript-niputus](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Etsi [Magento Marketplacesta](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) erilaisia kolmannen osapuolen laajennuksia kuvien optimointiin."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Preconnect- tai dns-prefetch-resurssivihjeitä voidaan lisätä [muutt<PERSON><PERSON> teeman as<PERSON><PERSON><PERSON>](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>`-tagit voidaan lisätä [muutt<PERSON><PERSON> teeman as<PERSON><PERSON><PERSON>](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> `next/image`-komponenttia `<img>`-komponentin sijaan kuvan<PERSON><PERSON>un optimoimiseksi automaattisesti. [<PERSON>e lisää](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `next/image`-komponenttia `<img>`-komponentin sijaan kuvien automaattiseen laiskaan latautumiseen. [<PERSON><PERSON> lisää](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON><PERSON> `next/image`-komponent<PERSON> ja aseta prioriteetti to<PERSON><PERSON>, jotta voit esiladata LCP-kuvan. [<PERSON><PERSON> lisää](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Lykkää ei-kriittisten kolmannen osapuolen skriptien la<PERSON>amista suorittamalla `next/script`. [<PERSON>e lisää](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `next/image`-kompo<PERSON><PERSON> ja varmista, että kuvat ovat aina oikean kokoisia. [<PERSON><PERSON> lisä<PERSON>](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON>ta `PurgeCSS` k<PERSON><PERSON>töö<PERSON> m<PERSON> (`Next.js`), jotta voit poistaa tyyliarkkien käyttämättömät säännöt. [Lue lisää](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Valitse `Webpack Bundle Analyzer`, jotta voit havaita käyttämättömän JavaScript-ohjelmakoodin. [Lue lisää](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "<PERSON><PERSON> kannattaa ehkä valita `Next.js Analytics`, jotta voit mitata sovel<PERSON>sen todellista toimivuutta. [<PERSON>e lisää](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Määritä muuttumattomien kohteiden ja `Server-side Rendered` (SSR) ‑sivujen välimuistiin siirtäminen. [Lue lisää](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `next/image`-komponenttia `<img>`-komponentin sijaan kuvan<PERSON>un säätämiseksi. [<PERSON>e lisää](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON> `next/image`, jotta `sizes` voi saada sopivan arvon. [<PERSON><PERSON> lisää](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "<PERSON><PERSON> p<PERSON> Next.js-palvelimellasi. [<PERSON><PERSON> lis<PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja määritä `format=\"webp\"`. [<PERSON><PERSON> lisä<PERSON>](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja määritä `loading=\"lazy\"` muille kuin näytöllä näkyville kuville. [<PERSON><PERSON> lisää](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja määritä `preload` LCP-kuvalle. [<PERSON><PERSON> lisä<PERSON>](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja määritä `width` ja `height` erikseen. [<PERSON><PERSON> lisä<PERSON>](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja aseta oikea `quality`-arvo. [<PERSON><PERSON> lisää](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "<PERSON><PERSON><PERSON><PERSON> `nuxt/image`-komponenttia ja aseta oikea `sizes`-arvo. [<PERSON><PERSON> lisää](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Ko<PERSON>a animoidut GIFit videoilla](https://web.dev/replace-gifs-with-videos/), mikä nopeuttaa verkkosivujen latautumista. Kannattaa myös käyttää nykyaikaisia tiedostomuotoja (esim. [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) tai [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder)), jotka voivat pakata yli 30 % tehokkaammin kuin VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Harkitse [laaj<PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=image) tai palvelua, joka muuntaa ladatut kuvat automaattisesti oikeaan muotoon. [Häviöttömät WebP-kuvat](https://developers.google.com/speed/webp) ovat kooltaan 26 % pienempiä kuin PNG:t ja 25–34 % pienempiä kuin vertailukelpoiset JPEG-kuvat vastaavassa SSIM-laatuindeksissä. Myös [AVIF](https://jakearchibald.com/2020/avif-has-landed/) on mahdollinen seuraavan sukupolven kuvamuoto."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Kannattaa ehkä asentaa [kuvien laiskan latautumisen liitännäinen](https://octobercms.com/plugins?search=lazy), joka lykkää muiden kuin näytöllä näkyvien kuvien latautumista, tai vai<PERSON>a malliin, joka tarjoaa tämän ominaisuuden. Harkitse myös [AMP-liitännäisen](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) käyttöä."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Liitännäisistä (plugin) voi olla apua [tärkeiden osien järjestelemisessä](https://octobercms.com/plugins?search=css). En voivat kuitenkin rikkoa muita liitännäisiä, joten testaa ne huole<PERSON>esti."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, liit<PERSON>nnäiset ja palvelinasetukset vaikuttavat kaikki palvelimen vastausaikaan. Kannattaa ehkä etsiä optimoidumpi teema, valita optimointiliitännäinen tai päivittää palvelin. October CMSin ansiosta [`Queues`](https://octobercms.com/docs/services/queues) voivat auttaa kehittäjiä aikaa vievien tehtävien (esim. sähköpostin lähettämisen) lykkäämisessä. Tämä nopeuttaa verkkopyyntöjä merkittävästi."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Kannattaa ehkä näyttää postauslistalla katkel<PERSON> (esim. `show more`-painikkeella), vähentää yhdellä verkkosivulla näkyvien postauksien määrää, jakaa pitkät postaukset usealle sivulle tai käyttää liitännäistä kommenttien laiskempaan latautumiseen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Monet [liitännäiset](https://octobercms.com/plugins?search=css) voivat nopeuttaa sivustoja ketjuttamalla, pienentämällä ja pakkaamalla tyylejä. Kun tämä tehdään etukäteen rakennusvaiheessa, kehittäminen voi olla nopeampaa."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Monet [liitännäiset](https://octobercms.com/plugins?search=javascript) voivat nopeuttaa sivustojen toimintaa ketjuttamal<PERSON>, pienentämällä ja pakkaamalla skriptiä. Kun tämä tehdään etukäteen rakennusvaiheessa, kehittäminen voi olla nopeampaa."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Kannattaa ehkä tarkistaa [liit<PERSON>nn<PERSON><PERSON><PERSON>](https://octobercms.com/plugins), jotka lataavat käyttämätöntä CSS:ää sivustolla. Löydät tarpeetonta CSS:ää lisäävät liitännäiset testaamalla [kood<PERSON> käytt<PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Tunnista syynä oleva teema tai liitännäinen tyyliarkin URL-osoitteen perusteella. Yritä löytää liitännäiset, joissa on paljon skriptiä ja punaista koodin käyttötasossa. Liitännäisen pitäisi lisätä tyyliarkki vain, jos sitä todella käytetään verkkosivulla."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Kannattaa ehkä tarkistaa [liitännäiset](https://octobercms.com/plugins?search=javascript), jotka lataavat käyttämätöntä JavaScriptiä verkkosivulla. Löydät tarpeetonta JavaScriptiä lisäävät liitännäiset testaamalla [kood<PERSON> käytt<PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) Chromen DevToolsissa. Tunnista syynä oleva teema tai liitännäinen skriptin URL-osoitteen perusteella. Yritä löytää liitännäiset, joissa on paljon skriptiä ja punaista koodin käyttötasossa. Liitännäisen pitäisi lisätä skriptiä vain, jos sitä todella käytetään verkkosivulla."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Lue lisää [tarpeettomien verkkopyyntöjen estämisestä HTTP-välimuistilla](https://web.dev/http-cache/#caching-checklist). Välimuistiin tallentamista voi nopeuttaa eri [liitännäisillä](https://octobercms.com/plugins?search=Caching)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "<PERSON>nna<PERSON>a ehkä käyttää [kuvanoptimointilaajennusta](https://octobercms.com/plugins?search=image), joka pakkaa kuvat niiden laadun s<PERSON>ttäen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Lataa kuvat suoraan mediaty<PERSON><PERSON>ulla, joll<PERSON>n oikeat kuvakoot ovat varmasti saatavilla. Kannattaa varmistaa koon muuttamiseen tarkoitetulla [suodatti<PERSON><PERSON>](https://octobercms.com/docs/markup/filter-resize) tai [liitännäisellä](https://octobercms.com/plugins?search=image), että kuvien koko on optimaalinen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "<PERSON><PERSON> tekstin pak<PERSON>aminen käyttöön palvelimen määrityksistä."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Kanna<PERSON>a ehkä k<PERSON>ytt<PERSON> \"windowing\"-k<PERSON><PERSON><PERSON><PERSON> (esim. `react-window`) DOM-säikeiden määrän vähentämiseksi, jos sivulla renderöidään monia toistuvia elementtejä. [<PERSON><PERSON> lisää](https://web.dev/virtualize-long-lists-react-window/). [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) tai [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) auttavat välttämään tarpeettomia uudelleenrenderöintejä. [<PERSON>ita tehosteet](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) vain siihen asti, kunnes tietyt riippuvuudet ovat muuttuneet, jos käytät `Effect` -kouk<PERSON><PERSON> su<PERSON> toiminnan parantamiseen."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "<PERSON><PERSON> k<PERSON> React Routeria, minimoi `<Redirect>`-komponentin käyttö [reittinavigoinnissa](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "<PERSON><PERSON> render<PERSON>it React-komponent<PERSON><PERSON>, `renderToPipeableStream()` tai `renderToStaticNodeStream()` voi auttaa niin, että asiakas saa vastaanottaa ja aktivoida merkintöjen eri kohtia kerralla k<PERSON><PERSON> sija<PERSON>. [<PERSON><PERSON> lisä<PERSON>](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "<PERSON><PERSON> vers<PERSON>rjestelmäsi kutistaa CSS-tiedostoja automaattisesti, varmista että käyttöönotto koskee sovelluksen tuotantoversiota. Voit tarkistaa tämän Reactin kehittäjätyökalut ‑laajennuksella. [Lue lisää](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "<PERSON><PERSON> ve<PERSON>rjestelmäsi kutistaa JS-tiedostoja automaattisesti, varmista että käyttöönotto koskee sovelluksen tuotantoversiota. Voit tarkistaa tämän Reactin kehittäjätyökalut ‑laajennuksella. [Lue lisää](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "<PERSON><PERSON> et vielä renderöi palvel<PERSON>, [jaa JavaScript-niput osiin](https://web.dev/code-splitting-suspense/) – `React.lazy()` voi auttaa. Jaa muussa tapauksessa koodi osiin kolmannen osapuolen kirjas<PERSON>, esimerkiksi [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Käytä React DevTools Profileria, joka käyttää React-sovellusliittymää, komponent<PERSON> render<PERSON><PERSON> mitta<PERSON>seen. [<PERSON>e lisää.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "GIF kannattaa ehkä ladata p<PERSON>, jonka avulla se <PERSON>aan upottaa HTML5-videona."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Suosittelemme k<PERSON>ä<PERSON>ä<PERSON> [Performance Lab](https://wordpress.org/plugins/performance-lab/) ‐liitännäistä muuntaaksesi lataamasi JPEG-kuvat automaattisesti WebP-muotoon aina kun mahdollista."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON> [WordPressin lazy load ‑laajennus](https://wordpress.org/plugins/search/lazy+load/), joka lykkää näytöllä näkymättömien kuvien lataamista, tai vai<PERSON><PERSON> tee<PERSON>, joka tarjoaa tämän ominaisuuden. Harkitse myös [AMP-laajennuksen](https://wordpress.org/plugins/amp/) käyttöä."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Monet WordPress-laajennukset voivat [tuoda tärkeää materiaalia sivun sisälle](https://wordpress.org/plugins/search/critical+css/) tai [lykätä vähemmän tärkeiden resurssien lataamista](https://wordpress.org/plugins/search/defer+css+javascript/). Hu<PERSON>a, että näiden laajennusten tuomat optimoinnit voivat rikkoa teeman tai laajennusten toimintoja, joten sinun on todennäköisesti muutettava koodia."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "<PERSON><PERSON><PERSON>, la<PERSON><PERSON><PERSON>kset ja palvelinasetukset vaikuttavat kaikki palvelimen vastausaikaan. <PERSON>un kannattaa ehkä etsiä optimoidumpi teema, valita optimointilaajennus tai päivittää palvelimesi."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "<PERSON>un kannattaa ehkä näyttää postausluettelossa katkelmia (esim. more-tagin avulla), näyttää yhdellä sivulla vähemmän postauksia, jakaa pitkät postaukset usealle sivulle tai käyttää kommenttien lazy load ‑laajennusta."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Monet [WordPress-laajennukset](https://wordpress.org/plugins/search/minify+css/) voivat nopeuttaa sivustosi toimintaa yhdistämällä, kutistamalla ja pakkaamalla tyylejä. Tämä kutistaminen voidaan mahdollisesti tehdä jo aiemmin kehitysvaiheen prosessilla."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Monet [WordPress-laajennukset](https://wordpress.org/plugins/search/minify+javascript/) voivat nopeuttaa sivustosi toimintaa yhdistämällä, kutistamalla ja pakkaamalla skriptejä. Tämä kutistaminen voidaan mahdollisesti tehdä jo aiemmin kehitysvaiheen prosessilla."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "<PERSON>un kannattaa ehkä poistaa tai vaihtaa toisiin [WordPress-laajennuksia](https://wordpress.org/plugins/), jotka lataavat sivulla käyttämätöntä CSS:ää. Etsi tarpeetonta CSS:ää lisääviä laajennuksia [tutkimalla koodin testikattavuutta](https://developer.chrome.com/docs/devtools/coverage/) Chromen DevToolsissa. Löydät syynä olevan teeman tai laajennuksen tyylitiedoston URL-osoitteen avulla. Etsi laajennuksia, joilla on monia tyylitiedostoja luettelossa ja paljon punaista koodin testikattavuudessa. Laajennuksen pitäisi lisätä tyylitiedosto jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "<PERSON>un kannattaa ehkä poistaa tai vaihtaa toisiin [WordPress-laajennuksia](https://wordpress.org/plugins/), jotka lataavat sivulla käyttämätöntä JavaScriptiä. Etsi tarpeetonta JS:ää lisääviä laajennuksia [tutkimalla koodin testikattavuutta](https://developer.chrome.com/docs/devtools/coverage/) Chromen DevToolsissa. Löydät syynä olevan teeman tai laajennuksen skriptin URL-osoitteen avulla. Etsi laajennuksia, joilla on monia skriptejä luettelossa ja paljon punaista koodin testikattavuudessa. Laajennuksen pitäisi lisätä skripti jonoon vain, jos sitä todella käytetään sivulla."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON>e lisää [selaimen välimuistin käytöstä WordPressissä](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Harkitse [WordPressin kuvaoptimointilaajennusta](https://wordpress.org/plugins/search/optimize+images/), joka pakkaa kuvat mutta säilyttää niiden laadun."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Lataa kuvat suoraan [media<PERSON><PERSON><PERSON><PERSON><PERSON>](https://wordpress.org/support/article/media-library-screen/), jolloin oikeat kuvakoot ovat varmasti sa<PERSON>, ja lisää ne kuvakirjastosta tai varmista oikeiden kuvakokojen käyttö kuva-widgetillä (myös responsiivisuuden raja-arvojen kohdalla). Älä käytä kuvia, joiden koko on `Full Size`, paitsi jos sivun koko on riittävä. [Lue lisää](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Voit ottaa tekstin pak<PERSON> käyttöön palvelimen määrityksistä."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Ota WP Rocketin kuvan optimointivälileh<PERSON> \"Imagify\" k<PERSON><PERSON>töön, niin voit muuntaa kuvat WebP-muotoon."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON> suositus otta<PERSON> [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) käyttöön WP Rocketissa. Tämä ominaisuus viivästyttää kuvien lataamista, kunnes kävijä vierittää sivua alaspäin ja kuvien näkeminen on tarpeellista."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Hyödynnä suositusta otta<PERSON> [poista käyttämätön CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) ja [lykkää JavaScriptin lataamista](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) käyttöön WP Rocketissa. Nämä ominaisuudet optimoivat CSS- ja JavaScript-tiedostot siten, että ne eivät estä sivun renderöintiä."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "<PERSON><PERSON><PERSON><PERSON> [pienennä CSS-tiedostot](https://docs.wp-rocket.me/article/1350-css-minify-combine) käyttöön WP Rocketissa. Kaik<PERSON> sivustosi CSS-tiedostojen tilat ja kommentit pois<PERSON>, jotta tiedoston koko pienenee ja se latautuu nopeammin."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> on<PERSON> [pienennä JavaScript-tiedostot](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) käyttöön WP Rocketissa. Tyhjät tilat ja kommentit poistetaan JavaScript-tiedostoista, jotta niiden koko pienenee ja ne latautuvat nopeammin."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON><PERSON> on<PERSON> [poista käyttämätön CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) käyttöön WP Rocketissa. Tämä pienentää sivun kokoa poistamalla kaikki käyttämättömät CSS:t ja tyyliarkit ja säilyttää kunkin sivun käyttämän CSS:n."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "<PERSON><PERSON><PERSON><PERSON> ongel<PERSON> [JavaScriptin suorittamisen viive](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) käyttöön WP Rocketissa. Tämä parantaa sivun lataamista viivästyttämällä skriptien suorittamista siihen asti, että käyttäjä tekee jotain. <PERSON><PERSON> sivu<PERSON> on iframe-kehyksiä, voit käyttää myös WP Rocketin [LazyLoadia iframeissa ja videoissa](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) ja [korvata YouTube-iframen esikatselukuvalla](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Ota WP Rocketin kuvan optimointivälile<PERSON><PERSON> \"Imagify\" käyttöön ja pakkaa kuvat suorittamalla joukko-optimointi."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Käytä WP Rocketissa [esihaettuja DNS-pyyntöjä](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests), niin voit lisätä dns-prefetch-resurssivihjeen ja nopeuttaa ulkoisten verkkotunnusten yhteyttä. WP Rocket lisää automaattisesti preconnect-resurssivihjeen [Google Fonts ‐verkkotunnukseen](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) ja mahdollisiin CNAME-hakuihin, jotka on lisätty [ota CDN käyttöön](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) ‐ominaisuuden avulla."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Jotta voit korjata on<PERSON><PERSON> font<PERSON>, ota WP Rocketissa käyttöön [Poista käyttämätön CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css). Sivustosi kriittiset fontit esiladataan tärkeinä."}, "report/renderer/report-utils.js | calculatorLink": {"message": "<PERSON><PERSON> laskin."}, "report/renderer/report-utils.js | collapseView": {"message": "Tiivistä näkymä"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Ensimmäinen navigointi"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Kriittisen polun enimmäisviive:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopioi JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Tumma teema päälle/pois"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Laajennett<PERSON> tulostus"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Tallenna HTML-muodossa"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Tallenna <PERSON>-tiedost<PERSON>"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | errorLabel": {"message": "Virhe!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Raporttivirhe: ei tarkastustietoja"}, "report/renderer/report-utils.js | expandView": {"message": "Laaj<PERSON>na näky<PERSON>ää"}, "report/renderer/report-utils.js | footerIssue": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratoriodata"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysoi nykyisen sivun mobiiliverkon emulaation avulla. Arvot ovat arvioita ja voivat vaihdella."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Lisää manuaalisesti tarkistettavia kohteita"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON> sovellu"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Arvioitu säästö"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Hyväksytyt tarkastukset"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Ensimmäinen sivun lataus"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emuloitu työpöytä"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Ei emulointia"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-versio"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "<PERSON><PERSON> su<PERSON>/muistin teho"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Verkon hidastaminen"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "<PERSON><PERSON>ytö<PERSON> emulointi"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Käyttäjäagentti (verkko)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "<PERSON>ksi sivun lataus"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Tämä data on peräisin yhdestä sivun latauksesta, toisin kuin kenttädata, joka on yhteenveto useista käyttökerroista."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Hidasta 4G-yhteyttä simuloiva rajoitus"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "<PERSON>i tietoa"}, "report/renderer/report-utils.js | show": {"message": "Näytä"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Näytä tähän liittyvät tarkastukset:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Tiivistä koodinpätkä"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Näytä kolmannen osapuolen resurssit"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Ymp<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Lighthousen suorituksessa havaittiin ongelmia:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Arvot ovat arvioita ja voivat vaihdella. [Tehok<PERSON>usprosentti lasketaan](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) suoraan näistä mittareista."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Näytä alkuperäinen jälki"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Näytä jälki"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Läpä<PERSON> tarka<PERSON>uk<PERSON>, mutta sai var<PERSON>"}, "report/renderer/report-utils.js | warningHeader": {"message": "Varoitukset: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON><PERSON> s<PERSON>"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> tavut"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Näytä/piilota taulu<PERSON>ko"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Käyttämättömät tavut"}}