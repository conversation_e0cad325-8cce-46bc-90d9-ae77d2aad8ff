{"core/audits/accessibility/accesskeys.js | description": {"message": "คีย์การเข้าถึงให้ผู้ใช้โฟกัสที่ส่วนหนึ่งของหน้าได้อย่างรวดเร็ว คีย์การเข้าถึงแต่ละรายการต้องไม่ซ้ำกันเพื่อให้ไปยังส่วนต่างๆ ได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับคีย์การเข้าถึง](https://dequeuniversity.com/rules/axe/4.6/accesskeys)"}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "ค่า `[accesskey]` ซ้ำกัน"}, "core/audits/accessibility/accesskeys.js | title": {"message": "ค่า `[accesskey]` ไม่ซ้ำกัน"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "`role` ของ ARIA แต่ละรายการรองรับชุดย่อยของแอตทริบิวต์ `aria-*` ที่เจาะจง หากรายการเหล่านี้ไม่ตรงกันจะทำให้แอตทริบิวต์ `aria-*` ไม่ถูกต้อง [ดูวิธีจับคู่แอตทริบิวต์ ARIA กับบทบาท](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)"}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่ตรงกับบทบาทของตน"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "แอตทริบิวต์ `[aria-*]` ตรงกับบทบาทของตน"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "เมื่อองค์ประกอบไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านองค์ประกอบนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้องค์ประกอบดังกล่าวไม่ได้ [ดูวิธีทําให้องค์ประกอบคําสั่งเข้าถึงได้ง่ายขึ้น](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)"}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "องค์ประกอบ `button`, `link` และ `menuitem` ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-command-name.js | title": {"message": "องค์ประกอบ `button`, `link` และ `menuitem` มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "เทคโนโลยีความช่วยเหลือพิเศษ (เช่น โปรแกรมอ่านหน้าจอ) ทำงานไม่สอดคล้องกันเมื่อตั้งค่า `aria-hidden=\"true\"` ในเอกสาร `<body>` [ดูว่า `aria-hidden` ส่งผลอย่างไรต่อส่วนเนื้อหาของเอกสาร](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)"}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "มี `[aria-hidden=\"true\"]` ปรากฏในเอกสาร `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "ไม่มี `[aria-hidden=\"true\"]` ปรากฏในเอกสาร `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "เอลิเมนต์ที่โฟกัสได้ลำดับต่อลงมาในเอลิเมนต์ `[aria-hidden=\"true\"]` ป้องกันไม่ให้ผู้ใช้เทคโนโลยีความช่วยเหลือพิเศษ (เช่น โปรแกรมอ่านหน้าจอ) ใช้เอลิเมนต์การโต้ตอบเหล่านั้นได้ [ดูว่า `aria-hidden` ส่งผลอย่างไรต่อองค์ประกอบที่โฟกัสได้](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)"}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "เอลิเมนต์ `[aria-hidden=\"true\"]` มีเอลิเมนต์ที่โฟกัสได้ลำดับต่อลงมา"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "เอลิเมนต์ `[aria-hidden=\"true\"]` ไม่มีเอลิเมนต์ที่โฟกัสได้ลำดับต่อลงมา"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "เมื่อช่องป้อนข้อมูลไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านปุ่มนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้ช่องป้อนข้อมูลดังกล่าวไม่ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับป้ายกำกับช่องป้อนข้อมูล](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)"}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "ช่องป้อนข้อมูล ARIA ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "ช่องป้อนข้อมูล ARIA มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "เมื่อองค์ประกอบเครื่องวัดไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านองค์ประกอบนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้องค์ประกอบดังกล่าวไม่ได้ [ดูวิธีตั้งชื่อองค์ประกอบ`meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)"}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "องค์ประกอบ ARIA `meter` ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "องค์ประกอบ ARIA `meter` มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "เมื่อองค์ประกอบ `progressbar` ไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านองค์ประกอบนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้องค์ประกอบดังกล่าวไม่ได้ [ดูวิธีติดป้ายกํากับองค์ประกอบ `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)"}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "องค์ประกอบ ARIA `progressbar` ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "องค์ประกอบ ARIA `progressbar` มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "บทบาท ARIA บางบทบาทกำหนดให้มีแอตทริบิวต์ที่อธิบายสถานะขององค์ประกอบให้โปรแกรมอ่านหน้าจอทราบ [ดูข้อมูลเพิ่มเติมเกี่ยวกับบทบาทและแอตทริบิวต์ที่จำเป็น](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)"}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "`[role]` ไม่มีแอตทริบิวต์ `[aria-*]` ทั้งหมดที่จำเป็น"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "`[role]` มีแอตทริบิวต์ `[aria-*]` ที่จำเป็นทั้งหมด"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "บทบาท ARIA ระดับบนสุดบางบทบาทต้องมีบทบาทย่อยที่เจาะจงเพื่อใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับบทบาทและองค์ประกอบย่อยที่จำเป็น](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)"}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "องค์ประกอบที่มี ARIA `[role]` ที่กำหนดให้องค์ประกอบย่อยต้องมี `[role]` ที่เฉพาะเจาะจงขาดองค์ประกอบย่อยที่จำเป็นดังกล่าวบางส่วนหรือทั้งหมด"}, "core/audits/accessibility/aria-required-children.js | title": {"message": "องค์ประกอบที่มี ARIA `[role]` ที่กำหนดให้องค์ประกอบย่อยต้องมี `[role]` ที่เฉพาะเจาะจงนั้นมีองค์ประกอบย่อยที่จำเป็นทั้งหมด"}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "บทบาท ARIA ย่อยบางบทบาทต้องอยู่ในบทบาทระดับบนสุดที่เจาะจงเพื่อให้ใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับบทบาท ARIA และองค์ประกอบระดับบนสุดที่จำเป็น](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)"}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "`[role]` ไม่ได้อยู่ในองค์ประกอบระดับบนสุดที่กำหนด"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "`[role]` อยู่ในองค์ประกอบระดับบนสุดที่กำหนด"}, "core/audits/accessibility/aria-roles.js | description": {"message": "บทบาท ARIA ต้องมีค่าที่ถูกต้องเพื่อใช้ฟังก์ชันการช่วยเหลือพิเศษตามวัตถุประสงค์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับบทบาท ARIA ที่ถูกต้อง](https://dequeuniversity.com/rules/axe/4.6/aria-roles)"}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "ค่า `[role]` ไม่ถูกต้อง"}, "core/audits/accessibility/aria-roles.js | title": {"message": "ค่า `[role]` ถูกต้อง"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "เมื่อช่องสลับไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านปุ่มนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้ช่องสลับดังกล่าวไม่ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับช่องสลับ](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)"}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "ช่องสลับ ARIA ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "ช่องสลับ ARIA มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "เมื่อองค์ประกอบเคล็ดลับเครื่องมือไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านองค์ประกอบนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้องค์ประกอบดังกล่าวไม่ได้ [ดูวิธีตั้งชื่อองค์ประกอบ`tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)"}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "องค์ประกอบ ARIA `tooltip` ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "องค์ประกอบ ARIA `tooltip` มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "เมื่อองค์ประกอบ `treeitem` ไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านองค์ประกอบนั้นโดยใช้ชื่อทั่วไป ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้องค์ประกอบดังกล่าวไม่ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการติดป้ายกำกับองค์ประกอบ `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)"}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "องค์ประกอบ ARIA `treeitem` ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "องค์ประกอบ ARIA `treeitem` มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "เทคโนโลยีความช่วยเหลือพิเศษ เช่น โปรแกรมอ่านหน้าจอ จะตีความแอตทริบิวต์ ARIA ที่มีค่าไม่ถูกต้องไม่ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับค่าที่ถูกต้องสําหรับแอตทริบิวต์ ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)"}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่มีค่าที่ถูกต้อง"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "แอตทริบิวต์ `[aria-*]` มีค่าที่ถูกต้อง"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "เทคโนโลยีความช่วยเหลือพิเศษ เช่น โปรแกรมอ่านหน้าจอ จะตีความแอตทริบิวต์ ARIA ที่มีชื่อไม่ถูกต้องไม่ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับแอตทริบิวต์ ARIA ที่ถูกต้อง](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)"}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "แอตทริบิวต์ `[aria-*]` ไม่ถูกต้องหรือสะกดผิด"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "แอตทริบิวต์ `[aria-*]` ถูกต้องและสะกดถูกต้อง"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "องค์ประกอบที่ไม่ผ่านการตรวจสอบ"}, "core/audits/accessibility/button-name.js | description": {"message": "เมื่อปุ่มไม่มีชื่อที่เข้าถึงได้ โปรแกรมอ่านหน้าจอจะอ่านปุ่มนั้นว่า \"ปุ่ม\" ซึ่งทำให้ผู้ที่ต้องใช้โปรแกรมอ่านหน้าจอใช้ปุ่มดังกล่าวไม่ได้ [ดูวิธีทําให้ปุ่มเข้าถึงได้ง่ายขึ้น](https://dequeuniversity.com/rules/axe/4.6/button-name)"}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "ปุ่มต่างๆ ไม่มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/button-name.js | title": {"message": "ปุ่มต่างๆ มีชื่อสำหรับการช่วยเหลือพิเศษ"}, "core/audits/accessibility/bypass.js | description": {"message": "การเพิ่มวิธีข้ามผ่านเนื้อหาที่ซ้ำกันช่วยให้ผู้ใช้แป้นพิมพ์ไปยังส่วนต่างๆ ของหน้าได้อย่างมีประสิทธิภาพมากขึ้น [ดูข้อมูลเพิ่มเติมเกี่ยวกับการบล็อกการข้าม](https://dequeuniversity.com/rules/axe/4.6/bypass)"}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "หน้าเว็บไม่มีส่วนหัว ลิงก์การข้าม หรือภูมิภาคของจุดสังเกต"}, "core/audits/accessibility/bypass.js | title": {"message": "หน้าเว็บมีส่วนหัว ลิงก์การข้าม หรือภูมิภาคของจุดสังเกต"}, "core/audits/accessibility/color-contrast.js | description": {"message": "ข้อความคอนทราสต์ต่ำมักทำให้ผู้ใช้จำนวนมากอ่านได้ยากหรืออ่านไม่ได้เลย [ดูวิธีทำให้สีมีคอนทราสต์เพียงพอ](https://dequeuniversity.com/rules/axe/4.6/color-contrast)"}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "สีพื้นหลังและสีพื้นหน้ามีอัตราส่วนคอนทราสต์ไม่เพียงพอ"}, "core/audits/accessibility/color-contrast.js | title": {"message": "สีพื้นหลังและสีพื้นหน้ามีอัตราส่วนคอนทราสต์ที่เพียงพอ"}, "core/audits/accessibility/definition-list.js | description": {"message": "เมื่อมีการทำเครื่องหมายรายการคำจำกัดความอย่างไม่ถูกต้อง โปรแกรมอ่านหน้าจออาจสร้างเอาต์พุตที่ทำให้สับสนหรือไม่แม่นยำ [ดูวิธีจัดโครงสร้างรายการคําจำกัดความอย่างถูกต้อง](https://dequeuniversity.com/rules/axe/4.6/definition-list)"}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "`<dl>` ไม่ได้มีเพียงกลุ่ม `<dt>` และ `<dd>` หรือองค์ประกอบ `<script>` `<template>` หรือ `<div>` ที่เรียงลำดับอย่างถูกต้อง"}, "core/audits/accessibility/definition-list.js | title": {"message": "`<dl>` มีเพียงกลุ่ม `<dt>` และ `<dd>` หรือองค์ประกอบ `<script>` `<template>` หรือ `<div>` ที่เรียงลำดับอย่างถูกต้อง"}, "core/audits/accessibility/dlitem.js | description": {"message": "รายการย่อยของคำจำกัดความ (`<dt>` และ `<dd>`) ต้องรวมอยู่ในองค์ประกอบ `<dl>` ระดับบนสุดเพื่อดูแลให้โปรแกรมอ่านหน้าจออ่านได้อย่างถูกต้อง [ดูวิธีจัดโครงสร้างรายการคําจำกัดความอย่างถูกต้อง](https://dequeuniversity.com/rules/axe/4.6/dlitem)"}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "รายการย่อยของคำจำกัดความไม่ได้รวมอยู่ในองค์ประกอบ `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "รายการย่อยของคำจำกัดความรวมอยู่ในองค์ประกอบ `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "ชื่อช่วยให้ผู้ใช้โปรแกรมอ่านหน้าจอทราบถึงภาพรวมของหน้า และผู้ใช้เครื่องมือค้นหาจะดูความเกี่ยวข้องของหน้ากับการค้นหาของตนจากชื่อเป็นหลัก [ดูข้อมูลเพิ่มเติมเกี่ยวกับชื่อเอกสาร](https://dequeuniversity.com/rules/axe/4.6/document-title)"}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "เอกสารไม่มีองค์ประกอบ `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "เอกสารมีองค์ประกอบ `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "เอลิเมนต์ที่โฟกัสได้ทั้งหมดต้องมี `id` ที่ไม่ซ้ำกันเพื่อให้เทคโนโลยีความช่วยเหลือพิเศษมองเห็นได้ [ดูวิธีแก้ไข `id` ที่ซ้ำกัน](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "แอตทริบิวต์ `[id]` ของเอลิเมนต์ที่โฟกัสได้และทำงานอยู่มีรหัสที่ซ้ำกัน"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "แอตทริบิวต์ `[id]` ของเอลิเมนต์ที่โฟกัสได้และทำงานอยู่ไม่มีรหัสที่ซ้ำกัน"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "ค่าของรหัส ARIA ต้องไม่ซ้ำกันเพื่อป้องกันไม่ให้เทคโนโลยีความช่วยเหลือพิเศษมองข้ามอินสแตนซ์อื่นๆ [ดูวิธีแก้ไขรหัส ARIA ที่ซ้ำกัน](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)"}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "มีรหัส ARIA ซ้ำกัน"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ไม่มีรหัส ARIA ที่ซ้ำกัน"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "ช่องในฟอร์มที่มีป้ายกำกับหลายรายการอาจทำให้เทคโนโลยีความช่วยเหลือพิเศษ (เช่น โปรแกรมอ่านหน้าจอ) สร้างความสับสนให้กับผู้ใช้ได้ โดยอาจอ่านป้ายกำกับแรก ป้ายกำกับสุดท้าย หรืออ่านทุกป้ายกำกับ [ดูวิธีใช้ป้ายกำกับในแบบฟอร์ม](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)"}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "ช่องในฟอร์มมีป้ายกำกับหลายรายการ"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "ไม่มีช่องในฟอร์มช่องใดมีป้ายกำกับหลายรายการ"}, "core/audits/accessibility/frame-title.js | description": {"message": "ผู้ใช้โปรแกรมอ่านหน้าจอต้องใช้ชื่อเฟรมเพื่ออธิบายเนื้อหาของเฟรม [ดูข้อมูลเพิ่มเติมเกี่ยวกับชื่อเฟรม](https://dequeuniversity.com/rules/axe/4.6/frame-title)"}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "องค์ประกอบ `<frame>` หรือ `<iframe>` ไม่มีชื่อ"}, "core/audits/accessibility/frame-title.js | title": {"message": "องค์ประกอบ `<frame>` หรือ `<iframe>` มีชื่อ"}, "core/audits/accessibility/heading-order.js | description": {"message": "ส่วนหัวที่เรียงลำดับอย่างถูกต้องโดยไม่มีการข้ามระดับจะถ่ายทอดโครงสร้างทางอรรถศาสตร์ของหน้าที่ทำให้การไปยังส่วนต่างๆ และการทำความเข้าใจง่ายมากขึ้นเมื่อใช้เทคโนโลยีความช่วยเหลือพิเศษ [ดูข้อมูลเพิ่มเติมเกี่ยวกับลำดับส่วนหัว](https://dequeuniversity.com/rules/axe/4.6/heading-order)"}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "เอลิเมนต์ส่วนหัวไม่ปรากฏตามลำดับในเอลิเมนต์ลำดับต่อๆ ลงมา"}, "core/audits/accessibility/heading-order.js | title": {"message": "เอลิเมนต์ส่วนหัวปรากฏตามลำดับในเอลิเมนต์ลำดับต่อๆ ลงมา"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "หากหน้าเว็บไม่ได้ระบุแอตทริบิวต์ `lang` โปรแกรมอ่านหน้าจอจะถือว่าหน้าดังกล่าวใช้ภาษาเริ่มต้นที่ผู้ใช้เลือกเมื่อตั้งค่าโปรแกรมอ่านหน้าจอ หากที่จริงแล้วหน้าดังกล่าวไม่ได้ใช้ภาษาเริ่มต้น โปรแกรมอ่านหน้าจออาจอ่านข้อความในหน้าได้ไม่ถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับแอตทริบิวต์ `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)"}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "องค์ประกอบ `<html>` ไม่มีแอตทริบิวต์ `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "องค์ประกอบ `<html>` มีแอตทริบิวต์ `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "การระบุ[ภาษา BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ที่ถูกต้องช่วยให้โปรแกรมอ่านหน้าจออ่านข้อความได้อย่างถูกต้อง [ดูวิธีใช้แอตทริบิวต์ `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)"}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "องค์ประกอบ `<html>` ไม่มีค่าที่ถูกต้องสำหรับแอตทริบิวต์ `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "องค์ประกอบ `<html>` มีค่าที่ถูกต้องสำหรับแอตทริบิวต์ `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "องค์ประกอบเพื่อการให้ข้อมูลควรมีข้อความสำรองที่สั้นกระชับและสื่อความหมาย การใช้แอตทริบิวต์ Alt ที่ว่างเปล่าจะเป็นการเพิกเฉยต่อองค์ประกอบเพื่อการตกแต่ง [ดูข้อมูลเพิ่มเติมเกี่ยวกับแอตทริบิวต์ `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)"}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "องค์ประกอบรูปภาพไม่มีแอตทริบิวต์ `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "องค์ประกอบรูปภาพมีแอตทริบิวต์ `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "เมื่อมีการใช้รูปภาพเป็นปุ่ม `<input>` การระบุข้อความสำรองจะช่วยให้ผู้ใช้โปรแกรมอ่านหน้าจอเข้าใจวัตถุประสงค์ของปุ่มได้ [ดูข้อมูลเกี่ยวกับข้อความแสดงแทนของรูปภาพที่ป้อน](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)"}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "องค์ประกอบ `<input type=\"image\">` ไม่มีข้อความ `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "องค์ประกอบ `<input type=\"image\">` มีข้อความ `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "ป้ายกำกับช่วยดูแลให้เทคโนโลยีความช่วยเหลือพิเศษอย่างเช่น โปรแกรมอ่านหน้าจอ อ่านส่วนควบคุมฟอร์มได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับป้ายกํากับองค์ประกอบแบบฟอร์ม](https://dequeuniversity.com/rules/axe/4.6/label)"}, "core/audits/accessibility/label.js | failureTitle": {"message": "องค์ประกอบฟอร์มไม่มีป้ายกำกับที่เชื่อมโยง"}, "core/audits/accessibility/label.js | title": {"message": "องค์ประกอบฟอร์มมีป้ายกำกับที่เชื่อมโยงอยู่"}, "core/audits/accessibility/link-name.js | description": {"message": "ข้อความลิงก์ (และข้อความสำรองสำหรับรูปภาพเมื่อใช้เป็นลิงก์) ที่แยกแยะได้ ไม่ซ้ำกัน และโฟกัสได้ ช่วยปรับปรุงประสบการณ์การไปยังส่วนต่างๆ สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูวิธีทำให้ลิงก์เข้าถึงได้](https://dequeuniversity.com/rules/axe/4.6/link-name)"}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "ลิงก์ไม่มีชื่อที่แยกแยะได้"}, "core/audits/accessibility/link-name.js | title": {"message": "ลิงก์มีชื่อที่แยกแยะได้"}, "core/audits/accessibility/list.js | description": {"message": "โปรแกรมอ่านหน้าจอมีวิธีเฉพาะในการอ่านรายการ การดูแลให้รายการมีโครงสร้างที่ถูกต้องช่วยโปรแกรมอ่านหน้าจอในการอ่านเนื้อหา [ดูข้อมูลเพิ่มเติมเกี่ยวกับโครงสร้างรายการที่เหมาะสม](https://dequeuniversity.com/rules/axe/4.6/list)"}, "core/audits/accessibility/list.js | failureTitle": {"message": "รายการไม่ได้มีแต่องค์ประกอบ `<li>` และองค์ประกอบที่รองรับสคริปต์ (`<script>` และ`<template>`)"}, "core/audits/accessibility/list.js | title": {"message": "รายการมีเพียงองค์ประกอบ `<li>` และองค์ประกอบที่รองรับสคริปต์ (`<script>` และ `<template>`)"}, "core/audits/accessibility/listitem.js | description": {"message": "โปรแกรมอ่านหน้าจอกำหนดให้รายการย่อย (`<li>`) อยู่ใน `<ul>` `<ol>` หรือ `<menu>` ระดับบนสุดเพื่อให้อ่านได้อย่างถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับโครงสร้างรายการที่เหมาะสม](https://dequeuniversity.com/rules/axe/4.6/listitem)"}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "รายการข้อมูล (`<li>`) ไม่ได้อยู่ภายในองค์ประกอบระดับบนสุด `<ul>`, `<ol>` หรือ `<menu>`"}, "core/audits/accessibility/listitem.js | title": {"message": "รายการข้อมูล (`<li>`) อยู่ในองค์ประกอบระดับบนสุด `<ul>` `<ol>` หรือ `<menu>`"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "ผู้ใช้ไม่ได้คาดหวังให้หน้าเว็บรีเฟรชโดยอัตโนมัติ และการรีเฟรชหน้าเว็บจะย้ายโฟกัสกลับไปที่ด้านบนของหน้า ซึ่งอาจทำให้ผู้ใช้ได้รับประสบการณ์การใช้งานที่สับสนหรือน่าหงุดหงิด [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตาแท็กการรีเฟรช](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)"}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "เอกสารใช้ `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "เอกสารนี้ไม่ได้ใช้ `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "การปิดใช้การซูมจะเป็นปัญหาสำหรับผู้ใช้ที่มีสายตาเลือนรางซึ่งต้องใช้การขยายหน้าจอเพื่อให้ดูเนื้อหาของหน้าเว็บได้อย่างชัดเจน [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตาแท็ก Viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)"}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "มีการใช้ `[user-scalable=\"no\"]` ในองค์ประกอบ `<meta name=\"viewport\">`หรือแอตทริบิวต์ `[maximum-scale]` น้อยกว่า 5"}, "core/audits/accessibility/meta-viewport.js | title": {"message": "ไม่มีการใช้ `[user-scalable=\"no\"]` ในองค์ประกอบ `<meta name=\"viewport\">` และแอตทริบิวต์ `[maximum-scale]` ไม่น้อยกว่า 5"}, "core/audits/accessibility/object-alt.js | description": {"message": "โปรแกรมอ่านหน้าจอแปลเนื้อหาที่ไม่ใช่ข้อความไม่ได้ การเพิ่มข้อความแสดงแทนลงในองค์ประกอบ `<object>` ช่วยโปรแกรมอ่านหน้าจอถ่ายทอดความหมายให้แก่ผู้ใช้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับข้อความแสดงแทนสำหรับองค์ประกอบ `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)"}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "องค์ประกอบ `<object>` ไม่มีข้อความแสดงแทน"}, "core/audits/accessibility/object-alt.js | title": {"message": "องค์ประกอบ `<object>` มีข้อความแสดงแทน"}, "core/audits/accessibility/tabindex.js | description": {"message": "ค่าที่มากกว่า 0 หมายความว่ามีการจัดเรียงการนำทางที่ชัดเจน แม้ว่าการทำงานนี้จะไม่มีปัญหาในทางเทคนิค แต่มักก่อให้เกิดประสบการณ์การใช้งานที่น่าหงุดหงิดสำหรับผู้ใช้เทคโนโลยีความช่วยเหลือพิเศษ [ดูข้อมูลเพิ่มเติมเกี่ยวกับแอตทริบิวต์ `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)"}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "องค์ประกอบบางอย่างมีค่า `[tabindex]` มากกว่า 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "ไม่มีองค์ประกอบที่มีค่า `[tabindex]` มากกว่า 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "โปรแกรมอ่านหน้าจอมีฟีเจอร์ที่ช่วยให้ไปยังส่วนต่างๆ ของตารางได้ง่ายขึ้น การดูแลให้เซลล์ `<td>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึงเซลล์อื่นๆ ในตารางเดียวกันเท่านั้นอาจช่วยปรับปรุงประสบการณ์สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติมเกี่ยวกับแอตทริบิวต์ `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)"}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "เซลล์ในองค์ประกอบ `<table>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึง `id` ขององค์ประกอบที่ไม่พบในตารางเดียวกันนี้"}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "เซลล์ในองค์ประกอบ `<table>` ที่ใช้แอตทริบิวต์ `[headers]` อ้างอิงถึงเซลล์ของตารางภายในตารางเดียวกัน"}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "โปรแกรมอ่านหน้าจอมีฟีเจอร์ที่ช่วยให้ไปยังส่วนต่างๆ ของตารางได้ง่ายขึ้น การดูแลให้ส่วนหัวของตารางอ้างอิงถึงชุดเซลล์บางชุดอยู่เสมออาจช่วยปรับปรุงประสบการณ์สำหรับผู้ใช้โปรแกรมอ่านหน้าจอ [ดูข้อมูลเพิ่มเติมเกี่ยวกับส่วนหัวของตาราง](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)"}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "องค์ประกอบ `<th>` และองค์ประกอบที่มี `[role=\"columnheader\"/\"rowheader\"]` ไม่มีเซลล์ข้อมูลที่องค์ประกอบอธิบาย"}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "องค์ประกอบ `<th>` และองค์ประกอบที่มี `[role=\"columnheader\"/\"rowheader\"]` มีเซลล์ข้อมูลที่องค์ประกอบอธิบาย"}, "core/audits/accessibility/valid-lang.js | description": {"message": "การระบุ[ภาษา BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) ที่ถูกต้องในองค์ประกอบต่างๆ ช่วยดูแลให้โปรแกรมอ่านหน้าจอออกเสียงข้อความได้อย่างถูกต้อง [ดูวิธีใช้แอตทริบิวต์ `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)"}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "แอตทริบิวต์ `[lang]` ไม่มีค่าที่ถูกต้อง"}, "core/audits/accessibility/valid-lang.js | title": {"message": "แอตทริบิวต์ `[lang]` มีค่าที่ถูกต้อง"}, "core/audits/accessibility/video-caption.js | description": {"message": "เมื่อวิดีโอมีคำอธิบายภาพ คนหูหนวกและผู้ใช้ที่มีความบกพร่องทางการได้ยินจะเข้าถึงข้อมูลของวิดีโอได้ง่ายขึ้น [ดูข้อมูลเพิ่มเติมเกี่ยวกับคำบรรยายวิดีโอ](https://dequeuniversity.com/rules/axe/4.6/video-caption)"}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "องค์ประกอบ `<video>` ไม่มีองค์ประกอบ `<track>` ที่มี `[kind=\"captions\"]`"}, "core/audits/accessibility/video-caption.js | title": {"message": "องค์ประกอบ `<video>` มีองค์ประกอบ `<track>` ที่มี `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "ค่าปัจจุบัน"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "โทเค็นแนะนำ"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` ช่วยให้ผู้ใช้ส่งแบบฟอร์มได้เร็วขึ้น พิจารณาเปิดใช้โดยตั้งค่าแอตทริบิวต์ `autocomplete` เป็นค่าที่ถูกต้องเพื่ออำนวยความสะดวกแก่ผู้ใช้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับ `autocomplete` ในแบบฟอร์ม](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "เอลิเมนต์ `<input>` ไม่มีแอตทริบิวต์ `autocomplete` ที่ถูกต้อง"}, "core/audits/autocomplete.js | manualReview": {"message": "ต้องตรวจสอบโดยเจ้าหน้าที่"}, "core/audits/autocomplete.js | reviewOrder": {"message": "ตรวจสอบลำดับของโทเค็น"}, "core/audits/autocomplete.js | title": {"message": "เอลิเมนต์ `<input>` ใช้ `autocomplete` อย่างถูกต้อง"}, "core/audits/autocomplete.js | warningInvalid": {"message": "โทเค็น `autocomplete`: \"{token}\" ใน {snippet} ไม่ถูกต้อง"}, "core/audits/autocomplete.js | warningOrder": {"message": "ตรวจสอบลำดับของโทเค็น: \"{tokens}\" ใน {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "ดำเนินการได้"}, "core/audits/bf-cache.js | description": {"message": "การไปยังส่วนต่างๆ หลายรายการจะดำเนินการโดยกลับไปที่หน้าก่อนหน้าหรือไปยังหน้าถัดไปอีกครั้ง Back-Forward Cache (bfcache) ช่วยเร่งการไปยังส่วนต่างๆ แบบย้อนกลับเหล่านี้ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับ bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{สาเหตุที่ดำเนินการไม่สำเร็จ 1 รายการ}other{สาเหตุที่ดำเนินการไม่สำเร็จ # รายการ}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "เหตุผลที่ไม่สำเร็จ"}, "core/audits/bf-cache.js | failureTitle": {"message": "หน้าเว็บป้องกันไม่ให้กู้คืนฟีเจอร์ Back-Forward <PERSON><PERSON>"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "ประเภทความล้มเหลว"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "ดำเนินการไม่ได้"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "รอการรองรับเบราว์เซอร์"}, "core/audits/bf-cache.js | title": {"message": "หน้าเว็บไม่ได้ป้องกันการกู้คืนฟีเจอร์ Back-Forward <PERSON><PERSON>"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "ส่วนขยาย Chrome ส่งผลเสียต่อประสิทธิภาพในการโหลดของหน้านี้ ลองตรวจสอบหน้าในโหมดไม่ระบุตัวตนหรือจากโปรไฟล์ Chrome ที่ไม่มีส่วนขยาย"}, "core/audits/bootup-time.js | columnScriptEval": {"message": "การประเมินสคริปต์"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "การแยกวิเคราะห์สคริปต์"}, "core/audits/bootup-time.js | columnTotal": {"message": "เวลา CPU รวม"}, "core/audits/bootup-time.js | description": {"message": "พิจารณาลดเวลาที่ใช้ในการแยกวิเคราะห์ คอมไพล์ และประมวลผล JS การส่งเพย์โหลด JS ปริมาณน้อยลงอาจช่วยในเรื่องนี้ได้ [ดูวิธีลดเวลาประมวลผล JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)"}, "core/audits/bootup-time.js | failureTitle": {"message": "ลดเวลาในการดำเนินการกับ JavaScript"}, "core/audits/bootup-time.js | title": {"message": "เวลาในการดำเนินการกับ JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "นำโมดูล JavaScript ขนาดใหญ่ที่ซ้ำกันออกจากแพ็กเกจเพื่อลดจำนวนไบต์ที่ไม่จำเป็นที่กิจกรรมเครือข่ายใช้ "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "นำโมดูลที่ซ้ำกันในแพ็กเกจ JavaScript ออก"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "GIF ขนาดใหญ่ไม่มีประสิทธิภาพในการแสดงเนื้อหาภาพเคลื่อนไหว พิจารณาใช้วิดีโอ MPEG4/WebM สำหรับภาพเคลื่อนไหวและใช้ PNG/WebP สำหรับภาพนิ่งแทน GIF เพื่อประหยัดไบต์ของเครือข่าย [ดูข้อมูลเพิ่มเติมเกี่ยวกับรูปแบบวิดีโอที่มีประสิทธิภาพ](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "ใช้รูปแบบวิดีโอสำหรับเนื้อหาภาพเคลื่อนไหว"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "โพลีฟิลล์และการเปลี่ยนรูปแบบช่วยให้เบราว์เซอร์เดิมใช้ฟีเจอร์ JavaScript ใหม่ได้ แต่ส่วนมากจะไม่จำเป็นสำหรับเบราว์เซอร์ที่ทันสมัย สำหรับ JavaScript แบบแพ็กเกจ ให้ใช้กลยุทธ์การทำให้สคริปต์สมัยใหม่ใช้งานได้โดยใช้การตรวจหาฟีเจอร์โมดูล/ไม่มีโมดูลเพื่อลดจำนวนโค้ดที่ส่งไปยังเบราว์เซอร์ที่ทันสมัย ขณะที่ยังรองรับเบราว์เซอร์เดิมอยู่ [ดูวิธีใช้ JavaScript ที่ทันสมัย](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "หลีกเลี่ยงการแสดง JavaScript ในเบราว์เซอร์สมัยใหม่"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "รูปแบบรูปภาพ เช่น WebP และ AVIF มักบีบอัดได้ดีกว่า PNG หรือ JPEG ซึ่งหมายความว่าจะดาวน์โหลดได้เร็วขึ้นและใช้อินเทอร์เน็ตน้อยลง [ดูข้อมูลเพิ่มเติมเกี่ยวกับรูปแบบรูปภาพที่ทันสมัย](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)"}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "แสดงรูปภาพในรูปแบบสมัยใหม่"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "พิจารณาโหลดรูปภาพนอกหน้าจอและรูปภาพที่ซ่อนไว้แบบ Lazy Loading หลังจากที่ทรัพยากรที่สำคัญทั้งหมดโหลดเสร็จแล้วเพื่อลดเวลาในการตอบสนอง [ดูวิธีเลื่อนรูปภาพนอกหน้าจอ](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)"}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "เลื่อนเวลาโหลดรูปภาพนอกจอภาพ"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "ทรัพยากรบล็อก First Paint ของหน้าเว็บอยู่ พิจารณาแสดง JS/CSS ที่สำคัญในหน้าและเลื่อนเวลาแสดง JS/สไตล์ที่ไม่สำคัญทั้งหมดออกไป [ดูวิธีกำจัดทรัพยากรที่บล็อกการแสดงผล](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)"}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "กำจัดทรัพยากรที่บล็อกการแสดงผล"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "เพย์โหลดปริมาณมากของเครือข่ายทำให้ผู้ใช้เสียค่าใช้จ่ายสูงและสัมพันธ์กับเวลาการโหลดนานเป็นอย่างมาก [ดูวิธีลดขนาดของเพย์โหลด](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)"}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "ขนาดรวมเดิมคือ {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "หลีกเลี่ยงเปย์โหลดเครือข่ายปริมาณมาก"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "หลีกเลี่ยงเปย์โหลดเครือข่ายปริมาณมาก"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "การลดขนาดไฟล์ CSS ช่วยลดขนาดเพย์โหลดของเครือข่ายได้ [ดูวิธีลดขนาด CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)"}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "ลดขนาด CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "การลดขนาดไฟล์ JavaScript ช่วยลดขนาดเพย์โหลดและเวลาในการแยกวิเคราะห์สคริปต์ได้ [ดูวิธีลดขนาด JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)"}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "ลดขนาด JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "ลดกฎที่ไม่ได้ใช้ในสไตล์ชีตและเลื่อนเวลาโหลด CSS ที่ไม่ได้ใช้สำหรับเนื้อหาครึ่งหน้าบนเพื่อลดจำนวนไบต์ที่กิจกรรมเครือข่ายใช้ [ดูวิธีลด CSS ที่ไม่ได้ใช้](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)"}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "ลด CSS ที่ไม่ได้ใช้"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "ลดจำนวน JavaScript ที่ไม่ได้ใช้และเลื่อนเวลาโหลดสคริปต์ไปจนกว่าจะจำเป็นต้องใช้เพื่อลดจำนวนไบต์ที่กิจกรรมเครือข่ายใช้ [ดูวิธีลด JavaScript ที่ไม่ได้ใช้](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)"}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "ลดจำนวน JavaScript ที่ไม่ได้ใช้"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "อายุการใช้งานแคชที่ยาวนานช่วยเพิ่มการเข้าชมหน้าเว็บซ้ำได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับนโยบายแคชที่มีประสิทธิภาพ](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{พบทรัพยากร 1 รายการ}other{พบทรัพยากร # รายการ}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "แสดงเนื้อหาคงที่ที่มีนโยบายแคชที่มีประสิทธิภาพ"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "ใช้นโยบายแคชที่มีประสิทธิภาพกับเนื้อหาคงที่"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "รูปภาพที่ได้รับการเพิ่มประสิทธิภาพจะโหลดได้เร็วขึ้นและใช้อินเทอร์เน็ตมือถือน้อยลง [ดูวิธีเข้ารหัสรูปภาพอย่างมีประสิทธิภาพ](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)"}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "เข้ารหัสรูปภาพอย่างมีประสิทธิภาพ"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "ขนาดจริง"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "ขนาดที่แสดง"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "รูปภาพใหญ่กว่าขนาดที่แสดง"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "รูปภาพเหมาะสำหรับขนาดที่แสดง"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "แสดงรูปภาพที่มีขนาดที่เหมาะสมเพื่อประหยัดอินเทอร์เน็ตมือถือและปรับปรุงเวลาในการโหลด [ดูวิธีปรับขนาดรูปภาพ](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)"}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "ปรับขนาดรูปภาพให้เหมาะสม"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "ทรัพยากรแบบข้อความควรแสดงผลโดยมีการบีบอัด (<PERSON>zi<PERSON>, Deflate หรือ Brotli) เพื่อลดจำนวนไบต์เครือข่ายทั้งหมด [ดูข้อมูลเพิ่มเติมเกี่ยวกับการบีบอัดข้อความ](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)"}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "เปิดใช้การบีบอัดข้อความ"}, "core/audits/content-width.js | description": {"message": "หากความกว้างของเนื้อหาในแอปไม่ตรงกับความกว้างของวิวพอร์ต แอปอาจไม่ได้รับการเพิ่มประสิทธิภาพสำหรับหน้าจออุปกรณ์เคลื่อนที่ [ดูวิธีปรับขนาดเนื้อหาของวิวพอร์ต](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)"}, "core/audits/content-width.js | explanation": {"message": "ขนาดวิวพอร์ต {innerWidth} พิกเซลไม่ตรงกับขนาดหน้าต่าง {outerWidth} พิกเซล"}, "core/audits/content-width.js | failureTitle": {"message": "ไม่ได้ปรับขนาดเนื้อหาอย่างถูกต้องสำหรับวิวพอร์ต"}, "core/audits/content-width.js | title": {"message": "มีการปรับขนาดเนื้อหาอย่างถูกต้องสำหรับวิวพอร์ต"}, "core/audits/critical-request-chains.js | description": {"message": "ห่วงโซ่คำขอที่สำคัญด้านล่างแสดงให้เห็นทรัพยากรที่โหลดโดยมีลำดับความสำคัญสูง พิจารณาลดความยาวของห่วงโซ่ ลดขนาดการดาวน์โหลดของทรัพยากร หรือเลื่อนเวลาการดาวน์โหลดทรัพยากรที่ไม่จำเป็นเพื่อปรับปรุงการโหลดหน้าเว็บ [ดูวิธีหลีกเลี่ยงการเชนคำขอที่สำคัญ](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)"}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{พบห่วงโซ่ 1 รายการ}other{พบห่วงโซ่ # รายการ}}"}, "core/audits/critical-request-chains.js | title": {"message": "หลีกเลี่ยงคำขอสำคัญแบบลูกโซ่"}, "core/audits/csp-xss.js | columnDirective": {"message": "คำสั่ง"}, "core/audits/csp-xss.js | columnSeverity": {"message": "ความรุนแรง"}, "core/audits/csp-xss.js | description": {"message": "นโยบายรักษาความปลอดภัยเนื้อหา (CSP) ที่มีประสิทธิภาพช่วยลดความเสี่ยงต่อการโจมตี Cross-site Scripting (XSS) ได้อย่างมาก [ดูวิธีใช้ CSP เพื่อป้องกัน XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "ไวยากรณ์"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "หน้าเว็บมีการกำหนด CSP ในแท็ก <meta> ลองย้าย CSP ไปยังส่วนหัว HTTP หรือกำหนด CSP ที่เข้มงวดอื่นๆ ในส่วนหัว HTTP"}, "core/audits/csp-xss.js | noCsp": {"message": "ไม่พบ CSP ในโหมดบังคับใช้"}, "core/audits/csp-xss.js | title": {"message": "ตรวจสอบว่า CSP มีผลกับการโจมตี XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "การเลิกใช้งาน / คำเตือน"}, "core/audits/deprecations.js | columnLine": {"message": "บรรทัด"}, "core/audits/deprecations.js | description": {"message": "API ที่เลิกใช้งานแล้วจะถูกนำออกจากเบราว์เซอร์ในท้ายที่สุด [ดูข้อมูลเพิ่มเติมเกี่ยวกับ API ที่เลิกใช้งานแล้ว](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)"}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{พบคำเตือน 1 รายการ}other{พบคำเตือน # รายการ}}"}, "core/audits/deprecations.js | failureTitle": {"message": "ใช้ API ที่เลิกใช้งานแล้ว"}, "core/audits/deprecations.js | title": {"message": "หลีกเลี่ยงการใช้ API ที่เลิกใช้งานแล้ว"}, "core/audits/dobetterweb/charset.js | description": {"message": "จำเป็นต้องประกาศการเข้ารหัสอักขระ ซึ่งทำได้โดยใช้แท็ก `<meta>` ใน 1024 ไบต์แรกของ HTML หรือในส่วนหัวการตอบกลับ HTTP ประเภทเนื้อหา [ดูข้อมูลเพิ่มเติมเกี่ยวกับการประกาศการเข้ารหัสอักขระ](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)"}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "ไม่มีการประกาศชุดอักขระหรือประกาศช้าเกินไปใน HTML"}, "core/audits/dobetterweb/charset.js | title": {"message": "กำหนดชุดอักขระได้อย่างถูกต้อง"}, "core/audits/dobetterweb/doctype.js | description": {"message": "การระบุ DOCTYPE ช่วยป้องกันไม่ให้เบราว์เซอร์เปลี่ยนไปใช้โหมดที่ไม่มาตรฐาน [ดูข้อมูลเพิ่มเติมเกี่ยวกับการประกาศ DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)"}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "ชื่อ DOCTYPE ต้องเป็นสตริง `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "เอกสารมี `doctype` ที่ทริกเกอร์ `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "เอกสารต้องมี DOCTYPE"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "สตริง publicId ควรจะว่าง"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "สตริง systemId ควรจะว่าง"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "เอกสารมี `doctype` ที่ทริกเกอร์ `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "หน้าไม่มี DOCTYPE HTML ดังนั้นจึงทริกเกอร์โหมดที่ไม่มาตรฐาน"}, "core/audits/dobetterweb/doctype.js | title": {"message": "หน้ามี DOCTYPE HTML"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "สถิติ"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "ค่า"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "รายการ DOM ขนาดใหญ่จะใช้หน่วยความจำเพิ่มขึ้น ทำให้[การคำนวณสไตล์](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations)ยาวนานขึ้น และสร้าง[การจัดเรียงการออกแบบใหม่](https://developers.google.com/speed/articles/reflow)ซึ่งมีค่าใช้จ่ายสูง [ดูวิธีหลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)"}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 องค์ประกอบ}other{# องค์ประกอบ}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "หลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "ความลึก DOM สูงสุด"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "องค์ประกอบ DOM ทั้งหมด"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "จำนวนองค์ประกอบย่อยสูงสุด"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "หลีกเลี่ยง DOM ที่มีขนาดใหญ่เกินไป"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "ผู้ใช้ไม่เชื่อถือหรือเกิดความสับสนในเว็บไซต์ที่ขอข้อมูลตำแหน่งโดยไม่มีบริบทให้ พิจารณาผูกคำขอกับการกระทำของผู้ใช้แทน [ดูข้อมูลเพิ่มเติมเกี่ยวกับสิทธิ์เข้าถึงตำแหน่งทางภูมิศาสตร์](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)"}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "ขอสิทธิ์เข้าถึงตำแหน่งทางภูมิศาสตร์ในการโหลดหน้าเว็บ"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "หลีกเลี่ยงการขอสิทธิ์เข้าถึงตำแหน่งทางภูมิศาสตร์ในการโหลดหน้าเว็บ"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "ประเภทปัญหา"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "ปัญหาต่างๆ ที่บันทึกไว้ในแผง `Issues` ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome บ่งบอกว่าเป็นปัญหาที่ยังไม่ได้รับการแก้ไข โดยอาจมาจากคำขอเครือข่ายที่ไม่ประสบความสำเร็จ การควบคุมด้านความปลอดภัยที่ไม่เพียงพอ และข้อกังวลอื่นๆ เกี่ยวกับเบราว์เซอร์ เปิดแผง \"ปัญหา\" ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome เพื่อดูรายละเอียดเพิ่มเติมของแต่ละปัญหา"}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "บันทึกปัญหาไว้แล้วในแผง `Issues` ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "ถูกบล็อกโดยนโยบายข้ามต้นทาง"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "การใช้ทรัพยากรปริมาณมากของโฆษณา"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "ไม่พบปัญหาในแผง `Issues` ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "เวอร์ชัน"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "ตรวจพบไลบรารี JavaScript ส่วนหน้าทั้งหมดในหน้าเว็บ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการตรวจสอบการวินิจฉัยไลบรารี JavaScript นี้](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)"}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "ตรวจพบไลบรารี JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "สำหรับผู้ใช้ที่การเชื่อมต่อช้า สคริปต์ภายนอกที่แทรกเข้ามาแบบไดนามิกผ่านทาง `document.write()` สามารถทำให้การโหลดหน้าเว็บช้าลงได้นับสิบวินาที [ดูวิธีหลีกเลี่ยง document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)"}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "หลีกเลี่ยง `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "หลีกเลี่ยงการใช้ `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "ผู้ใช้ไม่เชื่อถือหรือเกิดความสับสนในเว็บไซต์ที่ขอส่งการแจ้งเตือนโดยไม่มีบริบทให้ พิจารณาผูกคำขอกับท่าทางสัมผัสของผู้ใช้แทน [ดูข้อมูลเพิ่มเติมเกี่ยวกับการขอสิทธิ์ในการแสดงการแจ้งเตือนอย่างมีความรับผิดชอบ](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)"}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "ขอสิทธิ์การแจ้งเตือนในการโหลดหน้าเว็บ"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "หลีกเลี่ยงการขอสิทธิ์การแจ้งเตือนในการโหลดหน้าเว็บ"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "โปรโตคอล"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 มีข้อดีมากกว่า HTTP/1.1 หลายประการ เช่น การมีส่วนหัวแบบไบนารีและการมัลติเพล็กซ์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับ HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)"}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{คำขอ 1 รายการไม่ได้แสดงผ่าน HTTP/2}other{คำขอ # รายการไม่ได้แสดงผ่าน HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "ใช้ HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "ลองระบุ Listener เหตุการณ์แบบแตะและลูกกลิ้งเป็น `passive` เพื่อปรับปรุงประสิทธิภาพการเลื่อนของหน้าเว็บ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการใช้งาน Listener เหตุการณ์แบบแพสซีฟ](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "ไม่ได้ใช้ Listener แบบแพสซีฟเพื่อปรับปรุงประสิทธิภาพการเลื่อน"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "ใช้ Listener แบบแพสซีฟเพื่อปรับปรุงประสิทธิภาพการเลื่อน"}, "core/audits/errors-in-console.js | description": {"message": "ข้อผิดพลาดที่บันทึกลงในคอนโซลแสดงให้เห็นถึงปัญหาที่ไม่ได้รับการแก้ไข ข้อผิดพลาดอาจมาจากคำขอเครือข่ายที่ไม่สำเร็จ และปัญหาอื่นๆ เกี่ยวกับเบราว์เซอร์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับข้อผิดพลาดนี้ในการตรวจสอบการวินิจฉัยของคอนโซล](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "บันทึกข้อผิดพลาดเบราว์เซอร์ลงในคอนโซลแล้ว"}, "core/audits/errors-in-console.js | title": {"message": "ไม่มีข้อผิดพลาดเบราว์เซอร์บันทึกลงในคอนโซล"}, "core/audits/font-display.js | description": {"message": "ใช้ประโยชน์จากฟีเจอร์ CSS ของ `font-display` เพื่อให้ผู้ใช้เห็นข้อความได้ในขณะที่กําลังโหลดเว็บฟอนต์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับ `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)"}, "core/audits/font-display.js | failureTitle": {"message": "ตรวจสอบว่าข้อความจะยังมองเห็นได้ในระหว่างการโหลดเว็บฟอนต์"}, "core/audits/font-display.js | title": {"message": "ข้อความทั้งหมดจะยังมองเห็นได้ในระหว่างการโหลดเว็บฟอนต์"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Lighthouse ตรวจสอบค่า`font-display`ของต้นทาง {fontOrigin} โดยอัตโนมัติไม่ได้}other{Lighthouse ตรวจสอบค่า`font-display`ของต้นทาง {fontOrigin} โดยอัตโนมัติไม่ได้}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "สัดส่วนภาพ (ขนาดจริง)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "สัดส่วนภาพ (ที่แสดง)"}, "core/audits/image-aspect-ratio.js | description": {"message": "ขนาดแสดงรูปภาพควรจะมีสัดส่วนที่เป็นธรรมชาติ [ดูข้อมูลเพิ่มเติมเกี่ยวกับสัดส่วนภาพ](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)"}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "แสดงรูปภาพที่มีสัดส่วนไม่ถูกต้อง"}, "core/audits/image-aspect-ratio.js | title": {"message": "แสดงรูปภาพที่มีสัดส่วนถูกต้อง"}, "core/audits/image-size-responsive.js | columnActual": {"message": "ขนาดจริง"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "ขนาดที่แสดง"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "ขนาดที่คาดไว้"}, "core/audits/image-size-responsive.js | description": {"message": "ขนาดตามธรรมชาติของรูปภาพควรได้สัดส่วนกับขนาดการแสดงผลและอัตราส่วนพิกเซลเพื่อเพิ่มความชัดเจนของรูปภาพให้ได้มากที่สุด [ดูวิธีใส่รูปภาพที่ปรับเปลี่ยนตามอุปกรณ์](https://web.dev/serve-responsive-images/)"}, "core/audits/image-size-responsive.js | failureTitle": {"message": "แสดงรูปภาพที่มีความละเอียดต่ำ"}, "core/audits/image-size-responsive.js | title": {"message": "แสดงรูปภาพที่มีความละเอียดเหมาะสม"}, "core/audits/installable-manifest.js | already-installed": {"message": "มีการติดตั้งแอปนี้ไว้แล้ว"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "ดาวน์โหลดไอคอนที่จำเป็นจากไฟล์ Manifest ไม่ได้"}, "core/audits/installable-manifest.js | columnValue": {"message": "เหตุผลที่ไม่สำเร็จ"}, "core/audits/installable-manifest.js | description": {"message": "Service Worker เป็นเทคโนโลยีที่ช่วยให้แอปของคุณใช้ฟีเจอร์ของ Progressive Web App ได้หลายฟีเจอร์ เช่น ออฟไลน์ เพิ่มไปยังหน้าจอหลัก และข้อความ Push เมื่อใช้ Service Worker และไฟล์ Manifest อย่างเหมาะสม เบราว์เซอร์จะแจ้งผู้ใช้อย่างชัดแจ้งให้เพิ่มแอปของคุณในหน้าจอหลัก ซึ่งจะทำให้ผู้ใช้มีส่วนร่วมเพิ่มขึ้นได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับข้อกำหนดความสามารถในการติดตั้งไฟล์ Manifest](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)"}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{เหตุผล 1 ข้อ}other{เหตุผล # ข้อ}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "ไฟล์ Manifest ของเว็บแอปหรือ Service Worker ไม่ตรงตามข้อกำหนดด้านความสามารถในการติดตั้ง"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL ของแอปใน Play Store และรหัส Play Store ไม่ตรงกัน"}, "core/audits/installable-manifest.js | in-incognito": {"message": "หน้าโหลดในหน้าต่างที่ไม่ระบุตัวตน"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "พร็อพเพอร์ตี้ \"การแสดงผล\" ของไฟล์ Manifest ต้องเป็นอย่างใดอย่างหนึ่งระหว่าง \"standalone\", \"fullscreen\" หรือ \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "ไฟล์ Manifest มีช่อง \"display_override\" และโหมดการแสดงผลโหมดแรกที่รองรับต้องเป็นอย่างใดอย่างหนึ่งระหว่าง \"standalone\", \"fullscreen\" หรือ \"minimal-ui\""}, "core/audits/installable-manifest.js | manifest-empty": {"message": "ดึงข้อมูลไฟล์ Manifest ไม่ได้ ไฟล์ดังกล่าวว่างเปล่า หรือแยกวิเคราะห์ไม่ได้"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL ของไฟล์ Manifest เปลี่ยนไปขณะดึงข้อมูลไฟล์ Manifest"}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "ไฟล์ Manifest ไม่มีช่อง \"name\" หรือ \"short_name\""}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "ไฟล์ Manifest ไม่มีไอคอนที่เหมาะสม ซึ่งต้องอยู่ในรูปแบบ PNG, SVG หรือ WebP และมีขนาดอย่างน้อย {value0} พิกเซล ต้องตั้งค่าแอตทริบิวต์ขนาด และหากมีการตั้งค่าแอตทริบิวต์จุดประสงค์ แอตทริบิวต์นั้นต้องมี \"any\""}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "ไม่ได้ให้ไอคอนที่เป็นรูปสี่เหลี่ยมจัตุรัสและมีขนาดอย่างน้อย {value0} พิกเซลในรูปแบบ PNG, SVG หรือ WebP ซึ่งไม่ได้ตั้งค่าแอตทริบิวต์วัตถุประสงค์เอาไว้หรือตั้งค่าเป็น \"any\""}, "core/audits/installable-manifest.js | no-icon-available": {"message": "ไอคอนที่ดาวน์โหลดว่างเปล่าหรือเกิดความเสียหาย"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "ไม่ได้ระบุรหัส Play Store"}, "core/audits/installable-manifest.js | no-manifest": {"message": "หน้านี้ไม่มี <link> URL ของไฟล์ Manifest"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "ไม่พบ Service Worker ที่ตรงกัน คุณอาจต้องโหลดหน้านี้ซ้ำหรือตรวจสอบว่าขอบเขตของ Service Worker สำหรับหน้าปัจจุบันครอบคลุมขอบเขตและ URL เริ่มต้นจากไฟล์ Manifest"}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "ตรวจสอบ Service Worker โดยไม่มีช่อง \"start_url\" ในไฟล์ Manifest ไม่ได้"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ไม่รู้จักรหัสข้อผิดพลาดด้านความสามารถในการติดตั้ง \"{errorId}\""}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "หน้าไม่ได้แสดงผลจากต้นทางที่ปลอดภัย"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "หน้าไม่ได้โหลดในเฟรมหลัก"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "หน้าใช้งานแบบออฟไลน์ไม่ได้"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "ถอนการติดตั้ง PWA แล้วและกำลังรีเซ็ตการตรวจสอบความสามารถในการติดตั้ง"}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "แพลตฟอร์มแอปพลิเคชันที่ระบุไว้ใช้ไม่ได้ใน Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "ไฟล์ Manifest ระบุ prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications ใช้ได้เฉพาะใน Chrome เบต้า และเวอร์ชันเสถียรใน Android เท่านั้น"}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse ระบุไม่ได้ว่ามี Service Worker หรือไม่ โปรดลองใช้ Chrome เวอร์ชันใหม่กว่านี้"}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "รูปแบบ URL ของไฟล์ Manifest ({scheme}) ใช้ไม่ได้ใน Android"}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "URL เริ่มต้นของไฟล์ Manifest ไม่ถูกต้อง"}, "core/audits/installable-manifest.js | title": {"message": "ไฟล์ Manifest ของเว็บแอปและ Service Worker ตรงตามข้อกำหนดด้านความสามารถในการติดตั้ง"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL ในไฟล์ Manifest มีชื่อผู้ใช้ รหัสผ่าน หรือพอร์ต"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "หน้าใช้งานแบบออฟไลน์ไม่ได้ หน้านี้จะไม่ถือว่าติดตั้งได้หลังจาก Chrome 93 ซึ่งเป็นรุ่นที่เสถียรและเปิดตัวในเดือนสิงหาคม 2021"}, "core/audits/is-on-https.js | allowed": {"message": "อนุญาต"}, "core/audits/is-on-https.js | blocked": {"message": "ถูกบล็อก"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL ไม่ปลอดภัย"}, "core/audits/is-on-https.js | columnResolution": {"message": "การแก้ไขปัญหาตามคำขอ"}, "core/audits/is-on-https.js | description": {"message": "ควรปกป้องทุกเว็บไซต์ด้วยการใช้ HTTPS แม้ว่าจะเป็นเว็บไซต์ที่ไม่มีข้อมูลที่ละเอียดอ่อนก็ตาม ซึ่งรวมถึงการหลีกเลี่ยง[เนื้อหาผสม](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content)ที่มีการโหลดทรัพยากรบางอย่างผ่าน HTTP แม้ว่าคำขอเริ่มแรกจะดำเนินการผ่าน HTTPS ก็ตาม HTTPS ป้องกันผู้บุกรุกไม่ให้แทรกแซงหรือแอบฟังการสื่อสารระหว่างแอปกับผู้ใช้ของคุณ และเป็นข้อกำหนดที่ต้องทำก่อนสำหรับ HTTP/2 รวมถึง API ของแพลตฟอร์มเว็บใหม่ๆ อีกมาก [ดูข้อมูลเพิ่มเติมเกี่ยว HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)"}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{พบคำขอที่ไม่ปลอดภัย 1 รายการ}other{พบคำขอที่ไม่ปลอดภัย # รายการ}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "ไม่ได้ใช้ HTTPS"}, "core/audits/is-on-https.js | title": {"message": "ใช้ HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "อัปเกรดเป็น HTTPS โดยอัตโนมัติ"}, "core/audits/is-on-https.js | warning": {"message": "อนุญาตแบบมีคำเตือน"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "นี่คือองค์ประกอบเนื้อหาขนาดใหญ่สุดซึ่งแสดงผลภายในวิวพอร์ต [ดูข้อมูลเพิ่มเติมเกี่ยวกับองค์ประกอบ Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "องค์ประกอบ Largest Contentful Paint"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "ปัจจัยที่ทำให้เกิด CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "องค์ประกอบ DOM เหล่านี้มีส่วนอย่างมากที่สุดต่อ CLS ของหน้า [ดูวิธีเพิ่มประสิทธิภาพ CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "หลีกเลี่ยงการเลื่อนเลย์เอาต์ขนาดใหญ่"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "รูปภาพครึ่งหน้าบนที่โหลดแบบ Lazy Loading จะแสดงผลภายหลังในวงจรของหน้า ซึ่งอาจทำให้ Largest Contentful Paint ล่าช้า [ดูข้อมูลเพิ่มเติมเกี่ยวกับการโหลดแบบ Lazy Loading ที่ดีที่สุด](https://web.dev/lcp-lazy-loading/)"}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "โหลดรูปภาพ Largest Contentful Paint แบบ Lazy Loading แล้ว"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "ไม่ได้โหลดรูปภาพ Largest Contentful Paint แบบ Lazy Loading"}, "core/audits/long-tasks.js | description": {"message": "ระบุงานที่ใช้เวลานานที่สุดในเทรดหลัก เหมาะสำหรับการระบุปัจจัยที่แย่ที่สุดที่ทำให้อินพุตล่าช้า [ดูวิธีหลีกเลี่ยงงานในเทรดหลักที่ใช้เวลานาน](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{พบ # งานที่ใช้เวลานาน}other{พบ # งานที่ใช้เวลานาน}}"}, "core/audits/long-tasks.js | title": {"message": "หลีกเลี่ยงงานในเทรดหลักที่ใช้เวลานาน"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "หมวดหมู่"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "พิจารณาลดเวลาที่ใช้ในการแยกวิเคราะห์ แปลโปรแกรม และดำเนินการกับ JS การส่งเพย์โหลด JS ปริมาณน้อยลงอาจช่วยในเรื่องนี้ได้ [ดูวิธีลดงานเทรดหลัก](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "ลดการทำงานของเธรดหลัก"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "ลดการทำงานของเธรดหลัก"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "เว็บไซต์ควรทำงานในเบราว์เซอร์หลักๆ ทั้งหมดได้เพื่อให้เข้าถึงผู้ใช้จำนวนมากที่สุด [ดูข้อมูลเกี่ยวกับความเข้ากันได้ในเบราว์เซอร์ต่างๆ](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)"}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "เว็บไซต์ทำงานในเบราว์เซอร์ต่างๆ ได้"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "ตรวจดูว่าแต่ละหน้าทำ Deep Link ผ่าน URL ได้และ URL ต่างๆ ไม่ซ้ำกันเพื่อให้แชร์ได้ในโซเชียลมีเดีย [ดูข้อมูลเพิ่มเติมเกี่ยวกับการระบุ Deep Link](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)"}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "แต่ละหน้ามี URL ที่ไม่ซ้ำกัน"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "การเปลี่ยนควรจะดำเนินไปอย่างรวดเร็วขณะที่คุณแตะไปรอบๆ แม้ในเครือข่ายที่ช้า ซึ่งเป็นสิ่งสำคัญที่ทำให้ผู้ใช้รับรู้ได้ถึงประสิทธิภาพ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการเปลี่ยนหน้า](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)"}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "เปลี่ยนหน้าได้รวดเร็วแม้ว่าเครือข่ายจะช้า"}, "core/audits/maskable-icon.js | description": {"message": "ไอคอนที่มาสก์ได้ช่วยให้รูปภาพแสดงขึ้นเต็มกรอบโดยไม่มีแถบปรากฏอยู่ตามขอบเมื่อติดตั้งแอปในอุปกรณ์ [ดูข้อมูลเกี่ยวกับไอคอนไฟล์ Manifest ที่มาสก์ได้](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)"}, "core/audits/maskable-icon.js | failureTitle": {"message": "ไฟล์ Manifest ไม่มีไอคอนที่มาสก์ได้"}, "core/audits/maskable-icon.js | title": {"message": "ไฟล์ Manifest มีไอคอนที่มาสก์ได้"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "การเปลี่ยนเลย์เอาต์สะสมจะวัดการเคลื่อนไหวขององค์ประกอบที่มองเห็นได้ภายในวิวพอร์ต [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก Cumulative Layout Shift](https://web.dev/cls/)"}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "การโต้ตอบกับ Next Paint จะวัดการตอบสนองของหน้าเว็บ ซึ่งเป็นระยะเวลาที่หน้าเว็บใช้ในการตอบสนองต่ออินพุตของผู้ใช้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก การโต้ตอบกับ Next Paint](https://web.dev/inp/)"}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "First Contentful Paint ระบุเวลาที่มีการแสดงผลข้อความหรือรูปภาพครั้งแรก [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก First Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)"}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "First Meaningful Paint วัดเมื่อเนื้อหาหลักของหน้าเว็บปรากฏ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก First Meaningful Paint](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)"}, "core/audits/metrics/interactive.js | description": {"message": "เวลาในการตอบสนองคือระยะเวลาที่หน้าเว็บใช้ในการตอบสนองอย่างสมบูรณ์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริกเวลาในการตอบสนอง](https://developer.chrome.com/docs/lighthouse/performance/interactive/)"}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Largest Contentful Paint ระบุเวลาที่แสดงผลข้อความหรือรูปภาพได้มากที่สุด [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก Largest Contentful Paint](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "First Input Delay สูงสุดที่อาจเกิดขึ้นซึ่งผู้ใช้อาจเจอคือระยะเวลาของงานที่ยาวที่สุด [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก First Input Delay สูงสุดที่อาจเกิดขึ้น](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)"}, "core/audits/metrics/speed-index.js | description": {"message": "ดัชนีความเร็วแสดงให้เห็นความเร็วที่เนื้อหาของหน้าปรากฏจนดูสมบูรณ์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริกดัชนีความเร็ว](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)"}, "core/audits/metrics/total-blocking-time.js | description": {"message": "ผลรวมช่วงเวลาทั้งหมดระหว่าง FCP และเวลาในการตอบสนอง เมื่อความยาวของงานเกิน 50ms หน่วยเป็นมิลลิวินาที [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริกเวลาทั้งหมดในการบล็อก](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)"}, "core/audits/network-rtt.js | description": {"message": "ระยะเวลารับส่งข้อมูล (RTT) ของเครือข่ายมีผลกระทบอย่างมากต่อประสิทธิภาพ หากต้นทางมี RTT สูง แสดงว่าเซิร์ฟเวอร์ที่อยู่ใกล้กับผู้ใช้มากกว่าอาจช่วยปรับปรุงประสิทธิภาพได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับระยะเวลารับส่งข้อมูล](https://hpbn.co/primer-on-latency-and-bandwidth/)"}, "core/audits/network-rtt.js | title": {"message": "ระยะเวลารับส่งข้อมูลของเครือข่าย"}, "core/audits/network-server-latency.js | description": {"message": "เวลาในการตอบสนองต่อเซิร์ฟเวอร์อาจส่งผลกระทบต่อประสิทธิภาพของเว็บ หากต้นทางใช้เวลาในการตอบสนองต่อเซิร์ฟเวอร์นาน แสดงว่ามีการใช้งานเซิร์ฟเวอร์มากเกินไปหรือประสิทธิภาพแบ็กเอนด์ของเซิร์ฟเวอร์ไม่ดี [ดูข้อมูลเพิ่มเติมเกี่ยวกับเวลาในการตอบสนองของเซิร์ฟเวอร์](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)"}, "core/audits/network-server-latency.js | title": {"message": "เวลาในการตอบสนองจากแบ็กเอนด์ของเซิร์ฟเวอร์"}, "core/audits/no-unload-listeners.js | description": {"message": "เหตุการณ์ `unload` เริ่มทำงานโดยไม่มีความเสถียร และการ Listen อาจทำให้การเพิ่มประสิทธิภาพเบราว์เซอร์อย่าง Back-Forward Cache ไม่ทำงาน ใช้เหตุการณ์ `pagehide` หรือ `visibilitychange` แทน [ดูข้อมูลเพิ่มเติมเกี่ยวกับการยกเลิกการโหลด Listener เหตุการณ์](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "ลงทะเบียน Listener `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "หลีกเลี่ยง Listener เหตุการณ์ `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "ภาพเคลื่อนไหวที่ไม่ได้ทำการ Composite อาจมีคุณภาพต่ำและทำให้ CLS เพิ่มขึ้น [ดูวิธีหลีกเลี่ยงภาพเคลื่อนไหวที่ไม่ได้ทำการ Composite](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{พบองค์ประกอบของภาพเคลื่อนไหว # รายการ}other{พบองค์ประกอบของภาพเคลื่อนไหว # รายการ}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "พร็อพเพอร์ตี้ที่เกี่ยวข้องกับตัวกรองอาจทำให้พิกเซลเคลื่อนที่"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "เป้าหมายมีภาพเคลื่อนไหวอื่นที่ใช้ร่วมกันไม่ได้"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "เอฟเฟกต์มีโหมด Composite อื่นที่ไม่ใช่ \"replace\""}, "core/audits/non-composited-animations.js | title": {"message": "หลีกเลี่ยงการใช้ภาพเคลื่อนไหวที่ไม่ได้ทำการ Composite"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "พร็อพเพอร์ตี้ที่เกี่ยวข้องกับการเปลี่ยนรูปแบบจะขึ้นอยู่กับขนาดช่อง"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{มีพร็อพเพอร์ตี้ CSS ที่ไม่รองรับ: {properties}}other{มีพร็อพเพอร์ตี้ CSS ที่ไม่รองรับ: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "เอฟเฟกต์มีพารามิเตอร์การจับเวลาที่ไม่รองรับ"}, "core/audits/performance-budget.js | description": {"message": "ควบคุมให้จำนวนและขนาดของคำขอเครือข่ายอยู่ภายในเป้าหมายที่กำหนดตามงบประมาณด้านประสิทธิภาพที่ให้มา [ดูข้อมูลเพิ่มเติมเกี่ยวกับงบประมาณด้านประสิทธิภาพ](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 คำขอ}other{# คำขอ}}"}, "core/audits/performance-budget.js | title": {"message": "งบประมาณประสิทธิภาพ"}, "core/audits/preload-fonts.js | description": {"message": "โหลดแบบอักษร `optional` ไว้ล่วงหน้าเพื่อให้ผู้เข้าชมครั้งแรกใช้ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการโหลดแบบอักษรล่วงหน้า](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "ไม่มีการโหลดแบบอักษรที่มีค่า `font-display: optional` ไว้ล่วงหน้า"}, "core/audits/preload-fonts.js | title": {"message": "มีการโหลดแบบอักษรที่มีค่า `font-display: optional` ไว้ล่วงหน้า"}, "core/audits/prioritize-lcp-image.js | description": {"message": "หากเพิ่มองค์ประกอบ LCP ในหน้าเว็บแบบไดนามิก คุณควรโหลดรูปภาพล่วงหน้าเพื่อปรับปรุง LCP [ดูข้อมูลเพิ่มเติมเกี่ยวกับการโหลดองค์ประกอบ LCP ล่วงหน้า](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)"}, "core/audits/prioritize-lcp-image.js | title": {"message": "โหลดรูปภาพ Largest Contentful Paint ล่วงหน้า"}, "core/audits/redirects.js | description": {"message": "การเปลี่ยนเส้นทางทำให้เกิดความล่าช้ามากขึ้นก่อนที่หน้าเว็บจะโหลดได้ [ดูวิธีหลีกเลี่ยงการเปลี่ยนเส้นทางหน้าเว็บ](https://developer.chrome.com/docs/lighthouse/performance/redirects/)"}, "core/audits/redirects.js | title": {"message": "หลีกเลี่ยงการเปลี่ยนเส้นทางหลายหน้า"}, "core/audits/resource-summary.js | description": {"message": "หากต้องการตั้งงบประมาณสำหรับจำนวนและขนาดของทรัพยากรหน้า ให้เพิ่มไฟล์ budget.json [ดูข้อมูลเพิ่มเติมเกี่ยวกับงบประมาณด้านประสิทธิภาพ](https://web.dev/use-lighthouse-for-performance-budgets/)"}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 คำขอ • {byteCount, number, bytes} KiB}other{# คำขอ • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "ควบคุมให้จำนวนคำขอมีไม่มากและการโอนมีขนาดเล็ก"}, "core/audits/seo/canonical.js | description": {"message": "ลิงก์ Canonical จะบอกถึง URL ที่จะแสดงในผลการค้นหา [ดูข้อมูลเพิ่มเติมเกี่ยวกับลิงก์ Canonical](https://developer.chrome.com/docs/lighthouse/seo/canonical/)"}, "core/audits/seo/canonical.js | explanationConflict": {"message": "URL หลายรายการขัดแย้งกัน ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "URL ไม่ถูกต้อง ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "ชี้ไปที่ `hreflang` ตำแหน่งอื่น ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "ไม่ใช่ URL ที่สมบูรณ์ ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "ชี้ไปที่ URL ระดับรากของโดเมน (หน้าแรก) แทนที่จะเป็นหน้าที่เทียบเท่ากันของเนื้อหา"}, "core/audits/seo/canonical.js | failureTitle": {"message": "เอกสารไม่มี `rel=canonical` ที่ถูกต้อง"}, "core/audits/seo/canonical.js | title": {"message": "เอกสารมี `rel=canonical` ที่ถูกต้อง"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "รวบรวมข้อมูลลิงก์ไม่ได้"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "เครื่องมือค้นหาอาจใช้แอตทริบิวต์ `href` ในลิงก์เพื่อทำการ Crawl เว็บไซต์ โปรดตรวจสอบว่าแอตทริบิวต์ `href` ขององค์ประกอบแท็ก Anchor ลิงก์กับปลายทางที่เหมาะสมเพื่อให้ระบบค้นพบหน้าอื่นๆ ของเว็บไซต์ได้ [ดูวิธีทำให้ลิงก์เป็นลิงก์ที่ระบบทำการ Crawl ได้](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "รวบรวมข้อมูลลิงก์ไม่ได้"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "รวบรวมข้อมูลลิงก์ได้"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "ข้อความอื่นที่อ่านได้ไม่ชัดเจน"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "ขนาดแบบอักษร"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% ของข้อความในหน้า"}, "core/audits/seo/font-size.js | columnSelector": {"message": "ตัวเลือก"}, "core/audits/seo/font-size.js | description": {"message": "ขนาดตัวอักษรที่เล็กกว่า 12 พิกเซลจะเล็กเกินไปจนอ่านไม่ออกและทำให้ผู้เข้าชมในอุปกรณ์เคลื่อนที่ต้องใช้นิ้วซูมเพื่ออ่าน พยายามให้ข้อความในหน้าเว็บมากกว่า 60% มีขนาดอย่างน้อย 12 พิกเซล [ดูข้อมูลเพิ่มเติมเกี่ยวกับขนาดแบบอักษรที่อ่านง่าย](https://developer.chrome.com/docs/lighthouse/seo/font-size/)"}, "core/audits/seo/font-size.js | displayValue": {"message": "ข้อความที่อ่านได้ชัดเจน {decimalProportion, number, extendedPercent}"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "ข้อความอ่านได้ไม่ชัดเจนเพราะไม่มีเมตาแท็กวิวพอร์ตที่เพิ่มประสิทธิภาพให้เหมาะกับหน้าจออุปกรณ์เคลื่อนที่"}, "core/audits/seo/font-size.js | failureTitle": {"message": "เอกสารไม่ได้ใช้ขนาดตัวอักษรที่อ่านได้ชัดเจน"}, "core/audits/seo/font-size.js | legibleText": {"message": "ข้อความที่อ่านได้ชัดเจน"}, "core/audits/seo/font-size.js | title": {"message": "เอกสารใช้ขนาดตัวอักษรที่อ่านได้ชัดเจน"}, "core/audits/seo/hreflang.js | description": {"message": "ลิงก์ hreflang จะบอกให้เครื่องมือค้นหาทราบถึงเวอร์ชันของหน้าเว็บที่ควรแสดงในผลการค้นหาสำหรับแต่ละภาษาหรือภูมิภาค [ดูข้อมูลเพิ่มเติมเกี่ยวกับ `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)"}, "core/audits/seo/hreflang.js | failureTitle": {"message": "เอกสารไม่มี `hreflang` ที่ถูกต้อง"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "ค่า href แบบสัมพัทธ์"}, "core/audits/seo/hreflang.js | title": {"message": "เอกสารมี `hreflang` ที่ถูกต้อง"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "รหัสภาษาที่ไม่คาดคิด"}, "core/audits/seo/http-status-code.js | description": {"message": "หน้าเว็บที่มีรหัสสถานะ HTTP ไม่สำเร็จอาจไม่ได้รับการจัดทำดัชนีอย่างถูกต้อง [ดูข้อมูลเพิ่มเติมเกี่ยวกับรหัสสถานะ HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)"}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "หน้าเว็บมีรหัสสถานะ HTTP ไม่สำเร็จ"}, "core/audits/seo/http-status-code.js | title": {"message": "หน้าเว็บมีรหัสสถานะ HTTP สำเร็จ"}, "core/audits/seo/is-crawlable.js | description": {"message": "เครื่องมือค้นหาจะรวมหน้าเว็บของคุณไว้ในผลการค้นหาไม่ได้หากไม่มีสิทธิทำการ Crawl หน้าดังกล่าว [ดูข้อมูลเพิ่มเติมเกี่ยวกับคำสั่งสำหรับ Crawler](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)"}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "หน้าเว็บถูกบล็อกไม่ให้มีการจัดทำดัชนี"}, "core/audits/seo/is-crawlable.js | title": {"message": "หน้าไม่ได้ถูกบล็อกจากการจัดทำดัชนี"}, "core/audits/seo/link-text.js | description": {"message": "ข้อความอธิบายลิงก์ช่วยให้เครื่องมือค้นหาเข้าใจเนื้อหาของคุณ [ดูวิธีทําให้ลิงก์เข้าถึงได้ง่ายขึ้น](https://developer.chrome.com/docs/lighthouse/seo/link-text/)"}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{พบ 1 ลิงก์}other{พบ # ลิงก์}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "ลิงก์ไม่มีข้อความอธิบาย"}, "core/audits/seo/link-text.js | title": {"message": "ลิงก์มีข้อความอธิบาย"}, "core/audits/seo/manual/structured-data.js | description": {"message": "เรียกใช้[เครื่องมือทดสอบ Structured Data](https://search.google.com/structured-data/testing-tool/) และ [Structured Data Linter](http://linter.structured-data.org/) เพื่อตรวจสอบความถูกต้องของ Structured Data [ดูข้อมูลเพิ่มเติมเกี่ยวกับ Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)"}, "core/audits/seo/manual/structured-data.js | title": {"message": " Structured Data ถูกต้อง"}, "core/audits/seo/meta-description.js | description": {"message": "อาจมีการรวมคำอธิบายเมตาในผลการค้นหาเพื่อสรุปเนื้อหาของหน้าเว็บให้สั้นกระชับ [ดูข้อมูลเพิ่มเติมเกี่ยวกับคําอธิบายเมตา](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)"}, "core/audits/seo/meta-description.js | explanation": {"message": "ข้อความอธิบายว่างเปล่า"}, "core/audits/seo/meta-description.js | failureTitle": {"message": "เอกสารไม่มีคำอธิบายเมตา"}, "core/audits/seo/meta-description.js | title": {"message": "เอกสารมีคำอธิบายเมตา"}, "core/audits/seo/plugins.js | description": {"message": "เครื่องมือค้นหาจัดทำดัชนีเนื้อหาปลั๊กอินไม่ได้ และอุปกรณ์จำนวนมากจำกัดการใช้หรือไม่รองรับปลั๊กอิน [ดูข้อมูลเพิ่มเติมเกี่ยวกับการหลีกเลี่ยงปลั๊กอิน](https://developer.chrome.com/docs/lighthouse/seo/plugins/)"}, "core/audits/seo/plugins.js | failureTitle": {"message": "เอกสารใช้ปลั๊กอิน"}, "core/audits/seo/plugins.js | title": {"message": "เอกสารหลีกเลี่ยงการใช้ปลั๊กอิน"}, "core/audits/seo/robots-txt.js | description": {"message": "หากไฟล์ robots.txt มีรูปแบบไม่ถูกต้อง Crawler อาจไม่เข้าใจวิธีที่คุณต้องการให้ Crawl หรือจัดทำดัชนีเว็บไซต์ [ดูข้อมูลเพิ่มเติมเกี่ยวกับ robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)"}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "คำขอ robots.txt แสดงสถานะ HTTP ต่อไปนี้ {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{พบข้อผิดพลาด 1 รายการ}other{พบข้อผิดพลาด # รายการ}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse ดาวน์โหลดไฟล์ robots.txt ไม่ได้"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt ไม่ถูกต้อง"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt ถูกต้อง"}, "core/audits/seo/tap-targets.js | description": {"message": "องค์ประกอบสำหรับการโต้ตอบ เช่น ปุ่มและลิงก์ ต้องมีขนาดใหญ่พอ (48x48 พิกเซล) และมีพื้นที่ว่างโดยรอบมากพอเพื่อให้แตะได้ง่ายๆ โดยไม่ซ้อนทับกับองค์ประกอบอื่นๆ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเป้าหมายการแตะ](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)"}, "core/audits/seo/tap-targets.js | displayValue": {"message": "เป้าหมายการแตะที่มีขนาดเหมาะสม {decimalProportion, number, percent}"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "เป้าหมายการแตะมีขนาดเล็กเกินไปเพราะไม่มีเมตาแท็กวิวพอร์ตที่เพิ่มประสิทธิภาพให้เหมาะกับหน้าจออุปกรณ์เคลื่อนที่"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "เป้าหมายการแตะมีขนาดที่ไม่เหมาะสม"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "เป้าหมายซ้อนทับกัน"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "เป้าหมายการแตะ"}, "core/audits/seo/tap-targets.js | title": {"message": "เป้าหมายการแตะมีขนาดที่เหมาะสม"}, "core/audits/server-response-time.js | description": {"message": "พยายามทำให้การตอบกลับของเซิร์ฟเวอร์สำหรับเอกสารหลักใช้เวลาน้อยเนื่องจากคำขออื่นทั้งหมดจะขึ้นอยู่กับเวลานี้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก Time To First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)"}, "core/audits/server-response-time.js | displayValue": {"message": "เอกสารรากใช้เวลา {timeInMs, number, milliseconds} มิลลิวินาที"}, "core/audits/server-response-time.js | failureTitle": {"message": "ลดเวลาในการตอบกลับของเซิร์ฟเวอร์ขณะเริ่มแรก"}, "core/audits/server-response-time.js | title": {"message": "การตอบกลับของเซิร์ฟเวอร์ขณะเริ่มแรกใช้เวลาน้อย"}, "core/audits/service-worker.js | description": {"message": "Service Worker เป็นเทคโนโลยีที่ช่วยให้แอปของคุณใช้ฟีเจอร์ของ Progressive Web App ได้หลายฟีเจอร์ เช่น ออฟไลน์ เพิ่มไปยังหน้าจอหลัก และข้อความ Push [ดูข้อมูลเพิ่มเติมเกี่ยวกับ Service Worker](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)"}, "core/audits/service-worker.js | explanationBadManifest": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ไม่พบ `start_url` เนื่องจากไฟล์ Manifest แยกวิเคราะห์เป็น JSON ที่ถูกต้องไม่ได้"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ `start_url` ({startUrl}) ไม่ได้อยู่ในขอบเขตของ Service Worker นั้น ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "หน้านี้ควบคุมโดย Service Worker แต่ไม่พบ `start_url` เพราะไม่มีการดึงไฟล์ Manifest"}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "ต้นทางนี้มี Service Worker อย่างน้อย 1 ไฟล์ แต่หน้าเว็บ ({pageUrl}) ไม่อยู่ในขอบเขต"}, "core/audits/service-worker.js | failureTitle": {"message": "ไม่ได้ลงทะเบียน Service Worker ที่ควบคุมหน้าเว็บและ `start_url`"}, "core/audits/service-worker.js | title": {"message": "ลงทะเบียน Service Worker ที่ควบคุมหน้าเว็บและ `start_url`"}, "core/audits/splash-screen.js | description": {"message": "หน้าจอแนะนำที่มีธีมช่วยให้ผู้ใช้ได้รับประสบการณ์ที่มีคุณภาพสูงเมื่อเปิดแอปของคุณจากหน้าจอหลัก [ดูข้อมูลเพิ่มเติมเกี่ยวกับหน้าจอแนะนำ](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)"}, "core/audits/splash-screen.js | failureTitle": {"message": "ไม่ได้กำหนดค่าให้ใช้หน้าจอแนะนำที่กำหนดเอง"}, "core/audits/splash-screen.js | title": {"message": "มีการกำหนดค่าให้ใช้หน้าจอแนะนำที่กำหนดเอง"}, "core/audits/themed-omnibox.js | description": {"message": "คุณกำหนดธีมของแถบที่อยู่เบราว์เซอร์ให้เข้ากับเว็บไซต์ได้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับการกำหนดธีมแถบที่อยู่](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)"}, "core/audits/themed-omnibox.js | failureTitle": {"message": "ไม่ได้กำหนดสีธีมสำหรับแถบที่อยู่"}, "core/audits/themed-omnibox.js | title": {"message": "กำหนดสีธีมของแถบที่อยู่"}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (การสนับสนุนลูกค้า)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (การตลาด)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (โซเชียล)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (วิดีโอ)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "ผลิตภัณฑ์"}, "core/audits/third-party-facades.js | description": {"message": "โค้ดของบุคคลที่สามที่ฝังไว้บางโค้ดจะโหลดแบบ Lazy Loading ได้ ลองนำ Facade มาแทนโค้ดไว้จนกว่าจะต้องใช้โค้ดที่ฝังไว้ดังกล่าว [ดูวิธีเลื่อนบุคคลที่สามด้วย Facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)"}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{มี Facade ที่ใช้แทนได้ # รายการ}other{มี Facade ที่ใช้แทนได้ # รายการ}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "ทรัพยากรของบุคคลที่สามบางส่วนโหลดแบบ Lazy Loading ได้ด้วย Facade"}, "core/audits/third-party-facades.js | title": {"message": "โหลดทรัพยากรของบุคคลที่สามแบบ Lazy Loading ด้วย Facade"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "บุคคลที่สาม"}, "core/audits/third-party-summary.js | description": {"message": "โค้ดของบุคคลที่สามอาจส่งผลกระทบที่สำคัญต่อประสิทธิภาพการโหลด จำกัดจำนวนผู้ให้บริการบุคคลที่สามที่มากเกินไปและพยายามโหลดโค้ดของบุคคลที่สามหลังจากที่หน้าเว็บโหลดเบื้องต้นเสร็จเรียบร้อยแล้ว [ดูวิธีลดผลกระทบของบุคคลที่สาม](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)"}, "core/audits/third-party-summary.js | displayValue": {"message": "โค้ดของบุคคลที่สามบล็อกเทรดหลักเป็นเวลา {timeInMs, number, milliseconds} วินาที"}, "core/audits/third-party-summary.js | failureTitle": {"message": "ลดผลกระทบจากโค้ดของบุคคลที่สาม"}, "core/audits/third-party-summary.js | title": {"message": "ลดการใช้ของบุคคลที่สาม"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "การวัด"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "เมตริก"}, "core/audits/timing-budget.js | description": {"message": "ตั้งงบประมาณด้านเวลาเพื่อช่วยดูประสิทธิภาพของเว็บไซต์ เว็บไซต์ที่มีประสิทธิภาพจะโหลดได้เร็วและตอบสนองต่อเหตุการณ์ที่เป็นอินพุตจากผู้ใช้ได้อย่างรวดเร็ว [ดูข้อมูลเพิ่มเติมเกี่ยวกับงบประมาณด้านประสิทธิภาพ](https://developers.google.com/web/tools/lighthouse/audits/budgets)"}, "core/audits/timing-budget.js | title": {"message": "งบประมาณด้านเวลา"}, "core/audits/unsized-images.js | description": {"message": "กำหนดความกว้างและความยาวขององค์ประกอบรูปภาพอย่างชัดเจนเพื่อลดการขยับของเลย์เอาต์และปรับปรุง CLS [ดูวิธีตั้งค่าขนาดรูปภาพ](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "องค์ประกอบรูปภาพไม่มี `width` และ `height` ที่ชัดเจน"}, "core/audits/unsized-images.js | title": {"message": "องค์ประกอบรูปภาพมี `width` และ `height` ที่ชัดเจน"}, "core/audits/user-timings.js | columnType": {"message": "ประเภท"}, "core/audits/user-timings.js | description": {"message": "พิจารณาติดตั้ง User Timing API กับแอปของคุณเพื่อวัดประสิทธิภาพจริงของแอปในประสบการณ์ใช้งานที่สำคัญของผู้ใช้ [ดูข้อมูลเพิ่มเติมเกี่ยวกับคะแนน \"ระยะเวลาของผู้ใช้\"](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)"}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{ระยะเวลาของผู้ใช้ 1 รายการ}other{ระยะเวลาของผู้ใช้ # รายการ}}"}, "core/audits/user-timings.js | title": {"message": "ระยะเวลาที่เจาะจงของผู้ใช้และระยะเวลาทั่วไป"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "พบ `<link rel=preconnect>` สำหรับ \"{securityOrigin}\" แต่เบราว์เซอร์ไม่ได้นำไปใช้งาน โปรดตรวจสอบว่าคุณใช้แอตทริบิวต์ `crossorigin` อย่างถูกต้องแล้ว"}, "core/audits/uses-rel-preconnect.js | description": {"message": "พิจารณาเพิ่ม `preconnect` หรือ `dns-prefetch` ซึ่งบอกถึงทรัพยากรเพื่อสร้างการเชื่อมต่อกับต้นทางที่สำคัญของบุคคลที่สามตั้งแต่เนิ่นๆ [ดูวิธีเชื่อมต่อกับต้นทางที่จำเป็นล่วงหน้า](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)"}, "core/audits/uses-rel-preconnect.js | title": {"message": "เชื่อมต่อกับต้นทางที่จำเป็นล่วงหน้า"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "พบการเชื่อมต่อ `<link rel=preconnect>` มากกว่า 2 รายการ ควรใช้การเชื่อมต่อเช่นนี้เท่าที่จำเป็นและใช้กับต้นทางที่สำคัญที่สุดเท่านั้น"}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "พบ `<link rel=preconnect>` สำหรับ \"{securityOrigin}\" แต่เบราว์เซอร์ไม่ได้นำไปใช้งาน ใช้ `preconnect` กับต้นทางที่สำคัญซึ่งหน้าเว็บจะขออย่างแน่นอน"}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "พบ `<link>` การโหลดล่วงหน้าสำหรับ \"{preloadURL}\" แต่เบราว์เซอร์ไม่ได้นำไปใช้งาน โปรดตรวจสอบว่าคุณใช้แอตทริบิวต์ `crossorigin` อย่างถูกต้องแล้ว"}, "core/audits/uses-rel-preload.js | description": {"message": "พิจารณาใช้ `<link rel=preload>` เพื่อจัดลำดับความสำคัญในการเรียกทรัพยากรที่มีการขอให้โหลดหน้าเว็บภายหลัง [ดูวิธีโหลดคำขอคีย์ล่วงหน้า](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)"}, "core/audits/uses-rel-preload.js | title": {"message": "โหลดคำขอสำคัญล่วงหน้า"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL ของแผนที่"}, "core/audits/valid-source-maps.js | description": {"message": "แผนที่แหล่งที่มาจะแปลโค้ดที่มีการลดขนาดเป็นซอร์สโค้ดต้นฉบับ ซึ่งจะช่วยนักพัฒนาซอฟต์แวร์ในการแก้ไขข้อบกพร่องในเวอร์ชันที่ใช้งานจริง นอกจากนี้ Lighthouse ยังให้ข้อมูลเชิงลึกเพิ่มเติมได้ด้วย ลองพิจารณานำแผนที่แหล่งที่มาไปใช้งานเพื่อรับประโยชน์ดังกล่าว [ดูข้อมูลเพิ่มเติมเกี่ยวกับการแมปแหล่งที่มา](https://developer.chrome.com/docs/devtools/javascript/source-maps/)"}, "core/audits/valid-source-maps.js | failureTitle": {"message": "ไม่มีแผนที่แหล่งที่มาของ JavaScript ขนาดใหญ่ของบุคคลที่หนึ่ง"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "ไฟล์ JavaScript ขนาดใหญ่ไม่มีแผนที่แหล่งที่มา"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{คำเตือน: มี 1 รายการที่ขาดไปใน `.sourcesContent`}other{คำเตือน: มี # รายการที่ขาดไปใน `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "หน้าเว็บมีแผนที่แหล่งที่มาถูกต้อง"}, "core/audits/viewport.js | description": {"message": "`<meta name=\"viewport\">` ไม่เพียงเพิ่มประสิทธิภาพแอปสำหรับขนาดหน้าจออุปกรณ์เคลื่อนที่เท่านั้น แต่ยังป้องกัน[ไม่ให้เกิดความล่าช้าต่อข้อมูลจากผู้ใช้เป็นเวลา 300 มิลลิวินาที](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/)ด้วย [ดูข้อมูลเพิ่มเติมเกี่ยวกับการใช้เมตาแท็ก Viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)"}, "core/audits/viewport.js | explanationNoTag": {"message": "ไม่พบแท็ก `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "ไม่มีแท็ก `<meta name=\"viewport\">` ที่มี `width` หรือ `initial-scale`"}, "core/audits/viewport.js | title": {"message": "มีแท็ก `<meta name=\"viewport\">` ที่มี `width` หรือ `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "งานบล็อกเทรดนี้จะเกิดขึ้นในระหว่างการวัดการโต้ตอบกับ Next Paint [ดูข้อมูลเพิ่มเติมเกี่ยวกับเมตริก การโต้ตอบกับ Next Paint](https://web.dev/inp/)"}, "core/audits/work-during-interaction.js | displayValue": {"message": "ใช้ไป {timeInMs, number, milliseconds} มิลลิวินาทีกับเหตุการณ์ \"{interactionType}\""}, "core/audits/work-during-interaction.js | eventTarget": {"message": "เป้าหมายของเหตุการณ์"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "ลดงานในระหว่างการโต้ตอบหลัก"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "ความล่าช้าของอินพุต"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "ความล่าช้าของงานนำเสนอ"}, "core/audits/work-during-interaction.js | processingTime": {"message": "ระยะเวลาดำเนินการ"}, "core/audits/work-during-interaction.js | title": {"message": "ลดงานในระหว่างการโต้ตอบหลัก"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการใช้งาน ARIA ในแอปพลิเคชันของคุณ ซึ่งอาจช่วยให้ผู้ใช้ได้รับประสบการณ์การใช้งานเทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ ที่ดียิ่งขึ้น"}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "นี่เป็นโอกาสระบุเนื้อหาสำรองสำหรับเสียงและวิดีโอ การดำเนินการนี้อาจช่วยปรับปรุงประสบการณ์ของผู้ใช้ที่มีความบกพร่องทางการได้ยินหรือการมองเห็น"}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "เสียงและวิดีโอ"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "รายการเหล่านี้ไฮไลต์แนวทางปฏิบัติที่ดีที่สุดที่พบบ่อยของการช่วยเหลือพิเศษ"}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุด"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "การตรวจสอบเหล่านี้ไฮไลต์โอกาสในการ[ปรับปรุงการช่วยเหลือพิเศษของเว็บแอป](https://developer.chrome.com/docs/lighthouse/accessibility/) โดยจะตรวจพบอัตโนมัติได้เฉพาะปัญหากลุ่มย่อยด้านการช่วยเหลือพิเศษ เราจึงขอแนะนำให้ตรวจสอบด้วยตนเองด้วย"}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "รายการเหล่านี้จัดการพื้นที่ที่เครื่องมือทดสอบอัตโนมัติไม่ครอบคลุม ดูข้อมูลเพิ่มเติมในคำแนะนำเกี่ยวกับ[การดำเนินการตรวจสอบการช่วยเหลือพิเศษ](https://web.dev/how-to-review/)"}, "core/config/default-config.js | a11yCategoryTitle": {"message": "การช่วยเหลือพิเศษ"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงความอ่านง่ายของเนื้อหา"}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "คอนทราสต์"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการตีความเนื้อหาของคุณโดยผู้ใช้ภาษาต่างๆ"}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "การปรับให้เป็นสากลและการแปล"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงความหมายของส่วนควบคุมในแอปพลิเคชันของคุณ การดำเนินการนี้อาจช่วยให้ผู้ใช้ได้รับประสบการณ์การใช้งานเทคโนโลยีอำนวยความสะดวก เช่น โปรแกรมอ่านหน้าจอ ที่ดียิ่งขึ้น"}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "ชื่อและป้ายกำกับ"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงการไปยังส่วนต่างๆ ในแอปพลิเคชันของคุณด้วยแป้นพิมพ์"}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "การนำทาง"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "นี่เป็นโอกาสปรับปรุงประสบการณ์การอ่านตารางหรือข้อมูลรายการโดยใช้เทคโนโลยีความช่วยเหลือพิเศษ เช่น โปรแกรมอ่านหน้าจอ"}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "ตารางและรายการ"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "ความเข้ากันได้กับเบราว์เซอร์"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุด"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "ทั่วไป"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "ความน่าเชื่อถือและความปลอดภัย"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "ประสบการณ์ของผู้ใช้"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "งบประมาณประสิทธิภาพจะใช้เป็นมาตรฐานสำหรับประสิทธิภาพของเว็บไซต์คุณ"}, "core/config/default-config.js | budgetsGroupTitle": {"message": "งบประมาณ"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "ข้อมูลเพิ่มเติมเกี่ยวกับประสิทธิภาพของแอปพลิเคชัน ตัวเลขเหล่านี้ไม่[ส่งผลกระทบโดยตรง](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)ต่อคะแนนประสิทธิภาพ"}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "การวินิจฉัย"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "ประสิทธิภาพที่สำคัญที่สุดคือความเร็วที่พิกเซลแสดงผลในหน้าจอ เมตริกที่สำคัญ ได้แก่ การแสดงผลที่มีเนื้อหาเต็มครั้งแรก การแสดงผลที่มีความหมายครั้งแรก"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "การปรับปรุงการแสดงผลครั้งแรก"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "คำแนะนำเหล่านี้จะช่วยให้หน้าโหลดได้เร็วขึ้น โดยจะไม่[ส่งผลกระทบโดยตรง](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)ต่อคะแนนประสิทธิภาพ"}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "โอกาส"}, "core/config/default-config.js | metricGroupTitle": {"message": "เมตริก"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "ปรับปรุงประสบการณ์ในการโหลดโดยรวมเพื่อให้หน้าเว็บตอบสนองและพร้อมใช้งานโดยเร็วที่สุด เมตริกที่สำคัญ ได้แก่ เวลาในการโต้ตอบ ดัชนีความเร็ว"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "การปรับปรุงโดยรวม"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "ประสิทธิภาพ"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "การตรวจสอบเหล่านี้จะตรวจสอบลักษณะต่างๆ ของ Progressive Web App [ดูสิ่งที่จะช่วยทำให้เป็น Progressive Web App ที่ดี](https://web.dev/pwa-checklist/)"}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "การตรวจสอบเหล่านี้เป็นสิ่งที่ต้องทำใน[รายการตรวจสอบ PWA](https://web.dev/pwa-checklist/) ซึ่งเป็นเกณฑ์พื้นฐาน แต่ Lighthouse ไม่ได้ทำการตรวจสอบดังกล่าวโดยอัตโนมัติ การตรวจสอบจะไม่ส่งผลต่อคะแนนของคุณ แต่คุณควรตรวจสอบด้วยตนเอง"}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "ติดตั้งได้"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "เพิ่มประสิทธิภาพ PWA แล้ว"}, "core/config/default-config.js | seoCategoryDescription": {"message": "การตรวจสอบเหล่านี้ช่วยให้มั่นใจว่าหน้าเว็บของคุณทำตามคำแนะนำพื้นฐานในการปรับแต่งเว็บไซต์ให้ติดอันดับบนเครื่องมือค้นหา มีปัจจัยอื่นๆ มากมายที่ Lighthouse ไม่ให้คะแนนไว้ที่นี่ซึ่งอาจส่งผลต่อการจัดอันดับการค้นหา รวมถึงประสิทธิภาพใน [Core Web Vitals](https://web.dev/learn-core-web-vitals/) [ดูข้อมูลเพิ่มเติมเกี่ยวกับ Google Search Essentials](https://support.google.com/webmasters/answer/35769)"}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "เรียกใช้ตัวตรวจสอบความถูกต้องเพิ่มเติมเหล่านี้ในเว็บไซต์ของคุณเพื่อดูแนวทางปฏิบัติที่ดีที่สุดเพิ่มเติมเกี่ยวกับ SEO"}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "จัด HTML ให้อยู่ในรูปแบบที่ช่วยให้ Crawler เข้าใจเนื้อหาแอปได้ง่ายขึ้น"}, "core/config/default-config.js | seoContentGroupTitle": {"message": "แนวทางปฏิบัติที่ดีที่สุดเกี่ยวกับเนื้อหา"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Crawler จะต้องเข้าถึงแอปของคุณได้เพื่อให้แอปปรากฏในผลการค้นหา"}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "การรวบรวมข้อมูลและจัดทำดัชนี"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "ตรวจสอบว่าหน้าเว็บเหมาะกับอุปกรณ์เคลื่อนที่ ผู้ใช้จะได้ไม่ต้องบีบนิ้วหรือซูมเข้าเพื่ออ่านหน้าเนื้อหา [ดูวิธีทำให้หน้าเว็บเหมาะกับอุปกรณ์เคลื่อนที่](https://developers.google.com/search/mobile-sites/)"}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "เหมาะกับอุปกรณ์เคลื่อนที่"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "ดูเหมือนว่า CPU ของอุปกรณ์ที่ใช้ทดสอบจะช้ากว่าที่ Lighthouse คาดไว้ ซึ่งอาจส่งผลเสียต่อคะแนนประสิทธิภาพของคุณ ดูข้อมูลเพิ่มเติมเกี่ยวกับ[การปรับเทียบตัวคูณการชะลอตัวของ CPU ที่เหมาะสม](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "หน้านี้อาจไม่โหลดขึ้นตามที่คาดไว้เนื่องจาก URL ทดสอบของคุณ ({requested}) มีการเปลี่ยนเส้นทางไปยัง {final} ลองทดสอบ URL ที่ 2 โดยตรง"}, "core/gather/driver/navigation.js | warningTimeout": {"message": "หน้าโหลดช้าเกินกำหนดเวลา หน้าที่โหลดมาได้อาจไม่ครบถ้วน"}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "การล้างแคชของเบราว์เซอร์หมดเวลาแล้ว ลองตรวจสอบหน้านี้อีกครั้งและรายงานข้อบกพร่องหากยังคงพบปัญหาอยู่"}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{อาจมีข้อมูลที่จัดเก็บไว้ที่ส่งผลกระทบต่อประสิทธิภาพการโหลดในตำแหน่ง {locations} ทดสอบหน้านี้ในหน้าต่างที่ไม่ระบุตัวตนเพื่อป้องกันไม่ให้ทรัพยากรเหล่านั้นส่งผลกระทบต่อคะแนนของคุณ}other{อาจมีข้อมูลที่จัดเก็บไว้ที่ส่งผลกระทบต่อประสิทธิภาพการโหลดในตำแหน่ง {locations} ทดสอบหน้านี้ในหน้าต่างที่ไม่ระบุตัวตนเพื่อป้องกันไม่ให้ทรัพยากรเหล่านั้นส่งผลกระทบต่อคะแนนของคุณ}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "การล้างข้อมูลต้นทางหมดเวลาแล้ว ลองตรวจสอบหน้านี้อีกครั้งและรายงานข้อบกพร่องหากยังคงพบปัญหาอยู่"}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "เฉพาะหน้าที่โหลดผ่านคำขอ GET เท่านั้นที่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "เฉพาะหน้าที่มีรหัสสถานะ 2XX เท่านั้นที่แคชได้"}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome ตรวจพบความพยายามที่จะเรียกใช้ JavaScript ขณะอยู่ในแคช"}, "core/lib/bf-cache-strings.js | appBanner": {"message": "หน้าที่ขอ AppBanner ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Back-Forward Cache ถูกปิดใช้เนื่องจากมีการแจ้งว่าไม่เหมาะสม ไปที่ chrome://flags/#back-forward-cache เพื่อเปิดใช้งานในอุปกรณ์นี้โดยตรง"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้โดยบรรทัดคำสั่ง"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากหน่วยความจำไม่พอ"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Delegate ไม่รองรับ Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้สำหรับตัวแสดงผลล่วงหน้า"}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "แคชหน้านี้ไม่ได้เพราะมีอินสแตนซ์ BroadcastChannel ที่ Listener ลงทะเบียนไว้"}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "หน้าที่มีส่วนหัว cache-control:no-store ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "มีการล้างแคชโดยตั้งใจ"}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "หน้านี้ถูกนำออกจากแคชเพื่อให้แคชหน้าอื่นได้"}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "หน้าที่มีปลั๊กอินยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "หน้าที่ใช้ FileChooser API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "หน้าที่ใช้ File System Access API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "หน้าที่ใช้ตัวส่งสำหรับอุปกรณ์สื่อไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "โปรแกรมเล่นสื่อกำลังเล่นอยู่ขณะที่มีการออกจากหน้าไป"}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "หน้าที่ใช้ MediaSession API และมีการตั้งสถานะการเล่นไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "หน้าที่ใช้ MediaSession API และมีการตั้งตัวแฮนเดิลการดำเนินการไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากโปรแกรมอ่านหน้าจอ"}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "หน้าที่ใช้ SecurityH<PERSON>ler ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward C<PERSON>"}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "หน้าที่ใช้ Serial API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "หน้าที่ใช้ WebAuthetication API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "หน้าที่ใช้ WebBluetooth API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "หน้าที่ใช้ WebUSB API ไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "หน้าที่ใช้ Dedicated Worker หรือ Worklet ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "เอกสารโหลดไม่เสร็จก่อนที่จะมีการออกจากหน้าไป"}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "พบแบนเนอร์แอปขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "พบเครื่องมือจัดการรหัสผ่านของ Chrome ขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "กำลังแยกเอลิเมนต์ DOM ขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "พบโปรแกรมดูเครื่องมือแยกเอลิเมนต์ DOM ขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Back-Forward C<PERSON> ถูกปิดใช้เนื่องจากส่วนขยายที่ใช้ API การรับส่งข้อความ"}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "ส่วนขยายที่มีการเชื่อมต่อระยะเวลานานควรปิดการเชื่อมต่อก่อนที่จะจัดเก็บไว้ใน Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "ส่วนขยายที่มีการเชื่อมต่อระยะเวลานานพยายามส่งข้อความถึงเฟรมใน Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากส่วนขยาย"}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "กล่องโต้ตอบโมดัล เช่น การส่งแบบฟอร์มอีกครั้งหรือรหัสผ่าน HTTP แสดงขึ้นขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "หน้าแบบออฟไลน์แสดงขึ้นขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "พบแถบการแทรกแซง \"หน่วยความจำไม่เพียงพอ\" ขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "มีคำขอสิทธิ์ขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "พบตัวบล็อกป๊อปอัปขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "รายละเอียด Google Safe Browsing แสดงขึ้นขณะที่มีการออกจากหน้า"}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Google Safe Browsing ถือว่าหน้านี้มีการละเมิดและได้บล็อกป๊อปอัป"}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "มีการเปิดใช้งาน Service Worker ขณะที่หน้าอยู่ใน Back-Forward Cache"}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากเอกสารมีข้อผิดพลาด"}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "หน้าที่ใช้ FencedFrames จัดเก็บใน bfcache ไม่ได้"}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "หน้านี้ถูกนำออกจากแคชเพื่อให้แคชหน้าอื่นได้"}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "หน้าที่ให้สิทธิ์สตรีมสื่อยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "หน้าที่ใช้พอร์ทัลยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | idleManager": {"message": "หน้าที่ใช้ IdleManager ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward C<PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "หน้าที่มีการเชื่อมต่อ IndexedDB แบบเปิดยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "มีการใช้ API ที่ไม่มีสิทธิ์"}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "หน้าที่มีการแทรก JavaScript ลงในส่วนขยายยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "หน้าที่มีการแทรกสไตล์ชีตลงในส่วนขยายยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | internalError": {"message": "ข้อผิดพลาดภายใน"}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Back-Forward <PERSON><PERSON> ถูกปิดใช้เนื่องจากคำขอ Keepalive"}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "หน้าที่ใช้การล็อกแป้นพิมพ์ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | loading": {"message": "หน้านี้โหลดไม่เสร็จก่อนที่จะมีการออกจากหน้าไป"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "หน้าที่ทรัพยากรหลักของหน้ามี cache-control:no-cache ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "หน้าที่ทรัพยากรหลักของหน้ามี cache-control:no-store ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "มีการยกเลิกการไปยังส่วนต่างๆ ก่อนที่จะสามารถกู้คืนหน้าจาก Back-Forward <PERSON><PERSON> ได้"}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "หน้าถูกนำออกจากแคชเพราะการเชื่อมต่อเครือข่ายที่ทำงานอยู่ได้รับข้อมูลมากเกินไป Chrome จำกัดปริมาณข้อมูลที่แต่ละหน้าสามารถรับได้ขณะที่มีการแคชหน้าไว้"}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "หน้าที่มี fetch() หรือ XHR กำลังทำงานยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "หน้าถูกนำออกจาก Back-Forward <PERSON><PERSON> เนื่องจากคำขอเครือข่ายที่ใช้งานอยู่เกี่ยวข้องกับการเปลี่ยนเส้นทาง"}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "หน้านี้ถูกนำออกจากแคชเพราะเปิดการเชื่อมต่อเครือข่ายไว้นานเกินไป Chrome จำกัดเวลาที่หน้าสามารถรับข้อมูลขณะแคช"}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "หน้าที่ไม่มีส่วนหัวการตอบกลับที่ถูกต้องไม่สามารถเข้าถึงฟีเจอร์ Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "มีการไปยังส่วนต่างๆ เกิดขึ้นในเฟรมอื่นนอกเหนือจากเฟรมหลัก"}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "หน้าที่ธุรกรรมฐานข้อมูลได้รับการจัดทำดัชนีอย่างต่อเนื่องยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "หน้าที่คำขอเครือข่ายกำลังทำงานยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "หน้าที่คำขอดึงข้อมูลจากเครือข่ายกำลังทำงานยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "หน้าที่คำขอเครือข่ายกำลังทำงานยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "หน้าที่คำขอเครือข่าย XHR กำลังทำงานยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "หน้าที่ใช้ PaymentManager ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "หน้าที่ใช้การแสดงภาพซ้อนภาพยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | portal": {"message": "หน้าที่ใช้พอร์ทัลยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | printing": {"message": "หน้าที่แสดง Printing UI ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "มีการเปิดหน้านี้โดยใช้ \"`window.open()`\" และมีการอ้างอิงจากแท็บอื่น หรือหน้านี้เปิดหน้าต่างขึ้นมา"}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "การประมวลการแสดงผลสำหรับหน้านี้ใน Back-Forward <PERSON><PERSON> เกิดการขัดข้อง"}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "การประมวลการแสดงผลสำหรับหน้านี้ใน Back-Forward <PERSON><PERSON> ถูกตัด"}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "หน้าที่มีการขอสิทธิ์บันทึกเสียงยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "หน้าที่ขอสิทธิ์เข้าถึงข้อมูลเซ็นเซอร์ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "หน้าที่มีการขอสิทธิ์เพื่อซิงค์เบื้องหลังหรือดึงข้อมูลยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "หน้าที่มีการขอสิทธิ์ MIDI ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward C<PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "หน้าที่มีการขอสิทธิ์ส่งการแจ้งเตือนยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "หน้าที่มีการขอการเข้าถึงพื้นที่เก็บข้อมูลยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "หน้าที่มีการขอสิทธิ์บันทึกวิดีโอยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "เฉพาะหน้าที่รูปแบบ URL ของหน้าเป็น HTTP / HTTPS เท่านั้นที่สามารถแคชไว้ได้"}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "มี Service Worker อ้างสิทธิ์หน้านี้ขณะอยู่ใน Back-Forward Cache"}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "มี Service Worker พยายามส่ง `MessageEvent` ให้หน้าที่อยู่ใน Back-Forward Cache"}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "มีการยกเลิกการลงทะเบียน ServiceWorker ขณะที่หน้าอยู่ใน Back-Forward Cache"}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "หน้านี้ถูกนำออกจากฟีเจอร์ Back-Forward Cache เนื่องจากมีการเปิดใช้งาน Service Worker"}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome รีสตาร์ทและล้างรายการ Back-Forward <PERSON><PERSON>"}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "หน้าที่ใช้ SharedWorker ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "หน้าที่ใช้ SpeechRecognizer ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "หน้าที่ใช้ SpeechSynthesis ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "iframe ในหน้าเริ่มการไปยังส่วนต่างๆ ที่ไม่เสร็จสมบูรณ์"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "หน้าที่ทรัพยากรย่อยของหน้ามี cache-control:no-cache ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "หน้าที่ทรัพยากรย่อยของหน้ามี cache-control:no-store ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | timeout": {"message": "หน้านี้อยู่ใน Back-Forward <PERSON><PERSON> เกินเวลาสูงสุดและหมดอายุไปแล้ว"}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "หน้านี้หมดเวลาเข้าถึง Back-<PERSON> <PERSON><PERSON> (อาจเป็นเพราะตัวแฮนเดิลสำหรับซ่อนหน้าทำงานเป็นเวลานาน)"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "หน้านี้มีตัวแฮนเดิลการยกเลิกการโหลดติดตั้งอยู่ในเฟรมหลัก"}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "หน้านี้มีตัวแฮนเดิลการยกเลิกการโหลดในเฟรมย่อย"}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "เบราว์เซอร์ได้เปลี่ยนส่วนหัวการลบล้างของ User Agent"}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "หน้าที่ให้สิทธิ์เข้าถึงเพื่อบันทึกวิดีโอหรือเสียงยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward <PERSON><PERSON> ในขณะนี้"}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "หน้าที่ใช้ WebDatabase ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webHID": {"message": "หน้าที่ใช้ WebHID ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webLocks": {"message": "หน้าที่ใช้ WebLocks ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webNfc": {"message": "หน้าที่ใช้ WebNfc ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "หน้าที่ใช้ WebOTPService ยังไม่มีสิทธิ์ใช้ฟีเจอร์ bfcache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webRTC": {"message": "หน้าที่มี WebRTC ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | webShare": {"message": "หน้าที่ใช้ WebShare ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/bf-cache-strings.js | webSocket": {"message": "หน้าที่มี WebSocket ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | webTransport": {"message": "หน้าที่มี WebTransport ไม่สามารถเข้าถึงฟีเจอร์ Back-Forward Cache"}, "core/lib/bf-cache-strings.js | webXR": {"message": "หน้าที่ใช้ WebXR ยังไม่มีสิทธิ์ใช้ฟีเจอร์ Back-Forward Cache ในขณะนี้"}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "ลองเพิ่ม URL ในรูปแบบ https: และ http: (ซึ่งเบราว์เซอร์ที่รองรับ \"strict-dynamic\" จะไม่สนใจ) เพื่อให้เข้ากันได้กับเบราว์เซอร์เวอร์ชันเก่ากว่า"}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener เลิกใช้งานไปตั้งแต่ CSP3 โปรดใช้ส่วนหัว Cross-Origin-Opener-Policy แทน"}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer เลิกใช้งานไปตั้งแต่ CSP2 โปรดใช้ส่วนหัว Referrer-Policy แทน"}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss เลิกใช้งานไปตั้งแต่ CSP2 โปรดใช้ส่วนหัว X-XSS-Protection แทน"}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "เมื่อไม่มี base-uri จะอนุญาตแท็ก <base> ที่แทรก เพื่อตั้งค่า URL ฐานสำหรับ URL (เช่น สคริปต์) ทั้งหมดที่เกี่ยวข้องกับโดเมนที่ผู้โจมตีควบคุม ลองตั้งค่า base-uri เป็น \"none\" หรือ \"self\""}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "เมื่อไม่มี object-src จะทำให้มีการแทรกปลั๊กอินซึ่งรันสคริปต์ที่ไม่ปลอดภัย ลองตั้งค่า object-src เป็น \"none\" หากทำได้"}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "ไม่มีคำสั่ง script-src ซึ่งอาจทำให้รันสคริปต์ที่ไม่ปลอดภัยได้"}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "คุณลืมเครื่องหมายเซมิโคลอนหรือเปล่า ดูเหมือนว่า {keyword} จะเป็นคำสั่ง ไม่ใช่คีย์เวิร์ด"}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Nonces ควรใช้ชุดอักขระ base64"}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Nonces ต้องมีอักขระอย่างน้อย 8 ตัว"}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "หลีกเลี่ยงการใช้ URL รูปแบบธรรมดา ({keyword}) ในคำสั่งนี้ URL รูปแบบธรรมดาอนุญาตให้รันสคริปต์ที่มาจากโดเมนที่ไม่ปลอดภัยได้"}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "หลีกเลี่ยงการใช้ไวลด์การ์ดธรรมดา ({keyword}) ในคำสั่งนี้ ไวลด์การ์ดธรรมดาอนุญาตให้รันสคริปต์ที่มาจากโดเมนที่ไม่ปลอดภัยได้"}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "มีการกำหนดค่าปลายทางการรายงานผ่านคำสั่ง report-to เท่านั้น คำสั่งนี้ใช้ได้เฉพาะในเบราว์เซอร์แบบ Chromium จึงขอแนะนำให้ใช้คำสั่ง report-uri ด้วย"}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "ไม่มี CSP ที่กำหนดค่าปลายทางการรายงาน ซึ่งทำให้ดูแลรักษา CSP เมื่อเวลาผ่านไปและตรวจสอบการหยุดทำงานได้ยาก"}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "รายการโฮสต์ที่อนุญาตอาจถูกข้ามได้บ่อยๆ ลองใช้ nonces หรือ hashes ของ CSP แทน รวมถึง \"strict-dynamic\" หากจำเป็น"}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "คำสั่ง CSP ที่ไม่รู้จัก"}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "ดูเหมือนว่า {keyword} จะเป็นคีย์เวิร์ดที่ไม่ถูกต้อง"}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "\"unsafe-inline\" ทำให้รันสคริปต์ในหน้าเว็บและตัวจัดการเหตุการณ์ที่ไม่ปลอดภัยได้ ลองใช้ nonces หรือ hashes ของ CSP เพื่ออนุญาตสคริปต์แต่ละรายการ"}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "ลองเพิ่ม \"unsafe-inline\" (ซึ่งเบราว์เซอร์ที่รองรับ nonces/hashes จะไม่สนใจ) เพื่อให้เข้ากันได้กับเบราว์เซอร์เวอร์ชันเก่ากว่า"}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "สัญลักษณ์ไวลด์การ์ด (*) จะไม่ครอบคลุมการให้สิทธิ์ในการใช้งาน `Access-Control-Allow-Headers` สำหรับ CORS"}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "คำขอทรัพยากรที่ URL มีทั้งอักขระ `(n|r|t)` แบบช่องว่างซึ่งนำออกไปแล้วและมีอักขระน้อยกว่า (`<`) ถูกบล็อก โปรดนำบรรทัดใหม่ออกและเข้ารหัสอักขระที่น้อยกว่าจากที่ต่างๆ อย่างเช่นค่าแอตทริบิวต์ขององค์ประกอบเพื่อโหลดทรัพยากรเหล่านี้"}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` เลิกใช้งานแล้ว โปรดใช้ Navigation Timing 2 ซึ่งเป็น API แบบมาตรฐานแทน"}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` เลิกใช้งานแล้ว โปรดใช้ Paint Timing ซึ่งเป็น API แบบมาตรฐานแทน"}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` เลิกใช้งานแล้ว โปรดใช้ `nextHopProtocol` ใน Navigation Timing 2 ซึ่งเป็น API แบบมาตรฐานแทน"}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "คุกกี้ที่มีอักขระ `(0|r|n)` จะถูกปฏิเสธแทนการตัดให้สั้นลง"}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "การผ่อนปรนนโยบายต้นทางเดียวกันโดยการตั้งค่า `document.domain` เลิกใช้งานแล้วและจะปิดใช้โดยค่าเริ่มต้น คำเตือนการเลิกใช้งานนี้มีไว้สำหรับการเข้าถึงแบบข้ามต้นทางที่เปิดใช้โดยการตั้งค่า `document.domain`"}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "การเรียกใช้ {PH1} จาก iframe แบบข้ามต้นทางเลิกใช้งานแล้วและจะถูกนำออกในอนาคต"}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "ควรใช้แอตทริบิวต์ `disableRemotePlayback` เพื่อปิดใช้การผสานรวมการแคสต์เริ่มต้นแทนการใช้ตัวเลือก `-internal-media-controls-overlay-cast-button`"}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} เลิกใช้งานแล้ว โปรดใช้ {PH2} แทน"}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "นี่คือตัวอย่างข้อความแจ้งปัญหาการเลิกใช้งานที่แปลแล้ว"}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "การผ่อนปรนนโยบายต้นทางเดียวกันโดยการตั้งค่า `document.domain` เลิกใช้งานแล้วและจะปิดใช้โดยค่าเริ่มต้น หากต้องการใช้ฟีเจอร์นี้ต่อ โปรดเลือกไม่ใช้คลัสเตอร์ Agent ที่ผูกกับต้นทางโดยการส่งส่วนหัว `Origin-Agent-Cluster: ?0` พร้อมด้วยการตอบสนองของ HTTP สำหรับเอกสารและเฟรม ดูรายละเอียดเพิ่มเติมได้ที่ https://developer.chrome.com/blog/immutable-document-domain/"}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` เลิกใช้งานแล้วและจะถูกนำออก โปรดใช้ `Event.composedPath()` แทน"}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "ส่วนหัว `Expect-CT` เลิกใช้งานแล้วและจะถูกนำออก Chrome ต้องการ \"ความโปร่งใสของใบรับรอง\" สำหรับใบรับรองทั้งหมดที่ได้รับความเชื่อถือจากสาธารณะซึ่งออกหลังจากวันที่ 30 เมษายน 2018"}, "core/lib/deprecations-strings.js | feature": {"message": "ดูรายละเอียดเพิ่มเติมที่หน้าสถานะฟีเจอร์"}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` และ `watchPosition()` ไม่ทำงานในต้นทางที่ไม่ปลอดภัยอีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` และ `watchPosition()` เลิกใช้งานแล้วในต้นทางที่ไม่ปลอดภัย คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` ทำงานในต้นทางที่ไม่ปลอดภัยไม่ได้อีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS หากต้องการใช้ฟีเจอร์นี้ ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` เลิกใช้งานแล้ว โปรดใช้ `RTCPeerConnectionIceErrorEvent.address` หรือ `RTCPeerConnectionIceErrorEvent.port` แทน"}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "ต้นทางของผู้ขายและข้อมูลที่กําหนดเองจากเหตุการณ์ Service Worker `canmakepayment` ซึ่งได้แก่ `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers` เลิกใช้งานแล้วและจะถูกนำออก"}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "เว็บไซต์ขอทรัพยากรย่อยจากเครือข่ายที่เข้าถึงได้เท่านั้นเนื่องจากมีตำแหน่งเครือข่ายที่เป็นสิทธิ์เฉพาะของผู้ใช้ คำขอเหล่านี้จะเปิดเผยอุปกรณ์และเซิร์ฟเวอร์ที่ไม่เผยแพร่ต่อสาธารณะไปยังอินเทอร์เน็ต ซึ่งเพิ่มความเสี่ยงในการโจมตีโดยการปลอมแปลงคำขอแบบข้ามเว็บไซต์ (CSRF) และ/หรือข้อมูลรั่วไหล Chrome จะเลิกใช้งานคำขอไปยังทรัพยากรย่อยที่ไม่เผยแพร่ต่อสาธารณะเมื่อเริ่มต้นมาจากบริบทที่ไม่ปลอดภัยและจะเริ่มบล็อกคำขอดังกล่าวเพื่อลดความเสี่ยงเหล่านี้"}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "โหลด CSS จาก URL `file:` ไม่ได้ เว้นแต่จะลงท้ายด้วยนามสกุลไฟล์ `.css`"}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "การใช้ `SourceBuffer.abort()` เพื่อล้มเลิกการนำช่วงที่ไม่พร้อมกันออกของ `remove()` เลิกใช้งานแล้วเนื่องจากการเปลี่ยนแปลงข้อกำหนด ระบบจะนำการรองรับออกในอนาคต คุณควรให้ความสำคัญกับเหตุการณ์ `updateend` แทน `abort()` มีไว้เพื่อล้มเลิกการเพิ่มสื่อที่ไม่พร้อมกันหรือรีเซ็ตสถานะโปรแกรมแยกวิเคราะห์เท่านั้น"}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "การตั้งค่า `MediaSource.duration` ต่ำกว่าการประทับเวลาการนำเสนอสูงสุดของเฟรมที่ใส่โค้ดที่บัฟเฟอร์เลิกใช้งานแล้วเนื่องจากการเปลี่ยนแปลงข้อกำหนด การรองรับการนำสื่อที่บัฟเฟอร์ที่มีการตัดให้สั้นลงออกแบบไม่เจาะจงจะถูกนำออกในอนาคต คุณควรดำเนินการ `remove(newDuration, oldDuration)` แบบเจาะจงแทนใน `sourceBuffers` ทั้งหมดที่ `newDuration < oldDuration`"}, "core/lib/deprecations-strings.js | milestone": {"message": "การเปลี่ยนแปลงนี้จะมีผลกับเป้าหมาย {milestone}"}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Web MIDI จะขอสิทธิ์ในการใช้แม้ว่าจะไม่ได้ระบุ SysEx ใน `MIDIOptions` ก็ตาม"}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "ต้นทางที่ไม่ปลอดภัยอาจไม่ใช้ Notification API อีกต่อไป คุณควรพิจารณาเปลี่ยนแอปพลิเคชันไปใช้ต้นทางที่ปลอดภัยอย่างเช่น HTTPS ดูรายละเอียดเพิ่มเติมได้ที่ https://goo.gle/chrome-insecure-origins"}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "iframe แบบข้ามต้นทางอาจไม่ขอสิทธิ์สำหรับ Notification API อีกต่อไป คุณควรพิจารณาขอสิทธิ์จากเฟรมระดับบนสุดหรือเปิดหน้าต่างใหม่แทน"}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "พาร์ทเนอร์ของคุณต่อรอง (D)TLS เวอร์ชันที่ล้าสมัย โปรดตรวจสอบกับพาร์ทเนอร์เพื่อดำเนินการแก้ไข"}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL ในบริบทที่ไม่ปลอดภัยเลิกใช้งานแล้วและจะมีการนำออกเร็วๆ นี้ โปรดใช้พื้นที่เก็บข้อมูลเว็บหรือฐานข้อมูลที่จัดทำดัชนีแล้ว"}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "การระบุ `overflow: visible` ในแท็ก img, video และ canvas อาจทำให้แท็กเหล่านี้สร้างเนื้อหาภาพนอกขอบเขตขององค์ประกอบ โปรดดู https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md"}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` เลิกใช้งานแล้ว โปรดใช้การติดตั้งแบบทันท่วงทีแทนสำหรับตัวแฮนเดิลการชำระเงิน"}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "การเรียกใช้ `PaymentRequest` ได้ข้ามคำสั่งของนโยบายรักษาความปลอดภัยเนื้อหา (CSP) `connect-src` การข้ามนี้เลิกใช้งานแล้ว โปรดเพิ่มตัวระบุวิธีการชำระเงินจาก `PaymentRequest` API (ในช่อง `supportedMethods`) ลงในคำสั่งของ CSP `connect-src`"}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` เลิกใช้งานแล้ว โปรดใช้ `navigator.storage` มาตรฐานแทน"}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` ที่มีองค์ประกอบหลัก `<picture>` ไม่ถูกต้องและด้วยเหตุนี้ระบบจึงจะไม่สนใจ โปรดใช้ `<source srcset>` แทน"}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` เลิกใช้งานแล้ว โปรดใช้ `navigator.storage` มาตรฐานแทน"}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "คำขอทรัพยากรย่อยที่ URL มีข้อมูลเข้าสู่ระบบที่ฝังไว้ (เช่น `**********************/`) ถูกบล็อก"}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "ข้อจำกัด `DtlsSrtpKeyAgreement` ถูกนำออกแล้ว คุณได้ระบุค่า `false` สำหรับข้อจำกัดนี้ ซึ่งระบบตีความว่าเป็นการพยายามใช้เมธอด `SDES key negotiation` ที่นำออกไปแล้ว ฟังก์ชันการทำงานนี้ถูกนำออกแล้ว โปรดใช้บริการที่รองรับ `DTLS key negotiation` แทน"}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "ข้อจำกัด `DtlsSrtpKeyAgreement` ถูกนำออกแล้ว คุณได้ระบุค่า `true` สำหรับข้อจำกัดนี้ซึ่งไม่มีผลกระทบ แต่สามารถนำข้อจำกัดนี้ออกได้เพื่อความเรียบร้อย"}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "ตรวจพบ `Complex Plan B SDP` ไม่รองรับภาษาย่อยของ `Session Description Protocol` นี้อีกต่อไป โปรดใช้ `Unified Plan SDP` แทน"}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics` ที่ใช้เมื่อสร้าง `RTCPeerConnection` ด้วย `{sdpSemantics:plan-b}` เป็น `Session Description Protocol` เวอร์ชันเดิมที่ไม่ได้มาตรฐานซึ่งลบออกจากแพลตฟอร์มเว็บอย่างถาวรแล้ว โดยจะยังคงใช้ได้เมื่อสร้างด้วย `IS_FUCHSIA` แต่เราตั้งใจจะลบออกโดยเร็วที่สุด โปรดหยุดใช้โปรโตคอลดังกล่าว ดูสถานะได้ที่ https://crbug.com/1302249"}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "ตัวเลือก `rtcpMuxPolicy` เลิกใช้งานแล้วและจะถูกนำออก"}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` จะต้องใช้การแยกแบบข้ามต้นทาง ดูรายละเอียดเพิ่มเติมได้ที่ https://developer.chrome.com/blog/enabling-shared-array-buffer/"}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` แบบไม่ต้องมีการเปิดใช้งานของผู้ใช้เลิกใช้งานแล้วและจะถูกนำออก"}, "core/lib/deprecations-strings.js | title": {"message": "ใช้ฟีเจอร์ที่เลิกใช้งานแล้ว"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "ส่วนขยายควรเลือกใช้การแยกแบบข้ามต้นทางเพื่อใช้ `SharedArrayBuffer` ต่อ ดู https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/"}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} เจาะจงผู้ให้บริการ โปรดใช้ {PH2} มาตรฐานแทน"}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "JSON ของการตอบกลับไม่รองรับ UTF-16 ใน `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "`XMLHttpRequest` แบบพร้อมกันในชุดข้อความหลักเลิกใช้งานแล้วเนื่องจากมีผลกระทบที่เป็นอันตรายต่อประสบการณ์ของผู้ใช้ปลายทาง ดูความช่วยเหลือเพิ่มเติมได้ที่ https://xhr.spec.whatwg.org/"}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` เลิกใช้งานแล้ว โปรดใช้ `isSessionSupported()` และตรวจสอบค่าบูลีนที่แก้ไขแล้วแทน"}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "เวลาในการบล็อกเทรดหลัก"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "แคช TTL"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "รายละเอียด"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "ระยะเวลา"}, "core/lib/i18n/i18n.js | columnElement": {"message": "องค์ประกอบ"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "องค์ประกอบที่ไม่ผ่านการตรวจสอบ"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "ตำแหน่ง"}, "core/lib/i18n/i18n.js | columnName": {"message": "ชื่อ"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "เกินงบประมาณ"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "คำขอ"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "ขนาดทรัพยากร"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "ประเภททรัพยากร"}, "core/lib/i18n/i18n.js | columnSize": {"message": "ขนาด"}, "core/lib/i18n/i18n.js | columnSource": {"message": "แหล่งที่มา"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "เวลาเริ่มต้น"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "เวลาที่ใช้"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "ขนาดการโอน"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "เวลาที่อาจประหยัดได้"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "เวลาที่อาจประหยัดได้"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "อาจประหยัดพื้นที่ได้ {wastedBytes, number, bytes} KiB"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{พบ 1 องค์ประกอบ}other{พบ # องค์ประกอบ}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "อาจประหยัดได้ {wastedMs, number, milliseconds} มิลลิวินาที"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "เอกสาร"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "การแสดงผลที่มีความหมายครั้งแรก"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "แบบอักษร"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "รูปภาพ"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "การโต้ตอบกับ Next Paint"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "สูง"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "ต่ำ"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "ปานกลาง"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "First Input Delay สูงสุดที่อาจเกิดขึ้น"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "สื่อ"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} มิลลิวินาที"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "อื่นๆ"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "ทรัพยากรอื่นๆ"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "สคริปต์"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} วินาที"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "สไตล์ชีต"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "บุคคลที่สาม"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "รวม"}, "core/lib/lh-error.js | badTraceRecording": {"message": "เกิดข้อผิดพลาดในการบันทึกการติดตามระหว่างการโหลดหน้าเว็บ โปรดเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "หมดเวลาระหว่างที่รอการเชื่อมต่อโปรโตคอลโปรแกรมแก้ไขข้อบกพร่องเริ่มต้น"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome ไม่ได้รวบรวมภาพหน้าจอใดๆ ระหว่างการโหลดหน้า โปรดตรวจสอบว่ามีเนื้อหาที่มองเห็นได้ในหน้าเว็บ จากนั้นลองเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "เซิร์ฟเวอร์ DNS แก้ปัญหาโดเมนที่ระบุไม่ได้"}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "ตัวรวบรวม {artifactName} ที่จำเป็นพบข้อผิดพลาด: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "เกิดข้อผิดพลาดภายในของ Chrome โปรดรีสตาร์ท Chrome และลองเรียกใช้ Lighthouse อีกครั้ง"}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "ตัวรวบรวม {artifactName} ที่จำเป็นไม่ทำงาน"}, "core/lib/lh-error.js | noFcp": {"message": "หน้านี้ไม่ได้แสดงเนื้อหาใดเลย โปรดตรวจสอบว่าหน้าต่างเบราว์เซอร์อยู่เบื้องหน้าเสมอระหว่างการโหลดแล้วลองอีกครั้ง ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "หน้าเว็บไม่แสดงเนื้อหาที่มีคุณสมบัติเป็น Largest Contentful Paint (LCP) ตรวจสอบว่าหน้าเว็บมีองค์ประกอบ LCP ที่ถูกต้องแล้วลองอีกครั้ง ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "หน้าที่ระบุไม่ใช่ HTML (แสดงเป็นประเภท MIME {mimeType})"}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Chrome เวอร์ชันนี้เก่าเกินกว่าจะรองรับ \"{featureName}\" โปรดใช้เวอร์ชันใหม่เพื่อดูผลลัพธ์ทั้งหมด"}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง"}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Lighthouse โหลด URL ที่คุณขออย่างน่าเชื่อถือไม่ได้เพราะหน้าเว็บไม่ตอบสนอง"}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "URL ที่ระบุไม่มีใบรับรองความปลอดภัยที่ถูกต้อง {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome ป้องกันการโหลดหน้าเว็บด้วยโฆษณาคั่นระหว่างหน้า ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง"}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง (รายละเอียด: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Lighthouse โหลดหน้าเว็บที่คุณขออย่างน่าเชื่อถือไม่ได้ ตรวจสอบว่าคุณกำลังทดสอบ URL ที่ถูกต้องและเซิร์ฟเวอร์ตอบสนองคำขอทั้งหมดอย่างถูกต้อง (รหัสสถานะ: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "หน้าเว็บของคุณใช้เวลาโหลดนานเกินไป โปรดทำตามโอกาสในรายงานเพื่อลดเวลาในการโหลดหน้าเว็บแล้วลองเรียกใช้ Lighthouse อีกครั้ง ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "การรอการตอบสนองของโปรโตคอล DevTools เกินเวลาที่จัดสรรไว้ (เมธอด: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "การดึงข้อมูลเนื้อหาทรัพยากรเกินเวลาที่จัดสรรไว้"}, "core/lib/lh-error.js | urlInvalid": {"message": "ดูเหมือนว่า URL ที่ระบุจะไม่ถูกต้อง"}, "core/lib/navigation-error.js | warningXhtml": {"message": "ประเภท MIME ของหน้าเว็บคือ XHTML: Lighthouse ไม่รองรับเอกสารประเภทนี้อย่างชัดเจน"}, "core/user-flow.js | defaultFlowName": {"message": "การไหลเวียนของผู้ใช้ ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "รายงานการไปยังส่วนต่างๆ ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "รายงานสแนปชอต ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "รายงานช่วงเวลา ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "รายงานทั้งหมด"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "หมวดหมู่"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "การช่วยเหลือพิเศษ"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "แนวทางปฏิบัติที่ดีที่สุด"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "ประสิทธิภาพ"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "เดสก์ท็อป"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "ทำความเข้าใจรายงานโฟลว์ของ Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "ทำความเข้าใจโฟลว์"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "ใช้รายงานการไปยังส่วนต่างๆ เพื่อ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "ใช้รายงานภาพรวมเพื่อ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "ใช้รายงานระยะเวลาเพื่อ..."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "รับคะแนนประสิทธิภาพของ Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "วัดเมตริกประสิทธิภาพของการโหลดหน้าเว็บ เช่น Largest Contentful Paint และดัชนีความเร็ว"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "ประเมินความสามารถของ Progressive Web App"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "ค้นหาปัญหาด้านการช่วยเหลือพิเศษในแอปพลิเคชันหน้าเว็บเดียวหรือรูปแบบที่ซับซ้อน"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "ประเมินแนวทางปฏิบัติแนะนำของเมนูและองค์ประกอบ UI ที่ซ่อนอยู่หลังการโต้ตอบ"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "วัดการเปลี่ยนแปลงเลย์เอาต์และเวลาในการดำเนินการ JavaScript จากชุดการโต้ตอบต่างๆ"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "สำรวจโอกาสของประสิทธิภาพในการปรับปรุงประสบการณ์ของหน้าเว็บที่มีอายุยาวนานและแอปพลิเคชันหน้าเว็บเดียว"}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "มีประสิทธิภาพสูงสุด"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{การตรวจสอบที่เป็นประโยชน์ {numInformative} ครั้ง}other{การตรวจสอบที่เป็นประโยชน์ {numInformative} ครั้ง}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "อุปกรณ์เคลื่อนที่"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "การโหลดหน้าเว็บ"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "รายงานการไปยังส่วนต่างๆ จะวิเคราะห์การโหลดหน้าเว็บ 1 ครั้ง เช่นเดียวกับรายงานดั้งเดิมของ Lighthouse ทุกประการ"}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "รายงานการนำทาง"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{รายงานการนำทาง {numNavigation} ฉบับ}other{รายงานการนำทาง {numNavigation} ฉบับ}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{การตรวจสอบที่ผ่านได้ {numPassableAudits} ครั้ง}other{การตรวจสอบที่ผ่านได้ {numPassableAudits} ครั้ง}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{การตรวจสอบที่ผ่าน {numPassed} ครั้ง}other{การตรวจสอบที่ผ่าน {numPassed} ครั้ง}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "เฉยๆ"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "ข้อผิดพลาด"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "แย่"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "ดี"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "บันทึก"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "จับภาพสถานะของหน้าเว็บแล้ว"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "รายงานภาพรวมจะวิเคราะห์หน้าเว็บในสถานะหนึ่ง โดยทั่วไปจะเกิดขึ้นหลังจากการโต้ตอบของผู้ใช้"}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "รายงานสแนปชอต"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{รายงานสแนปชอต {numSnapshot} ฉบับ}other{รายงานสแนปชอต {numSnapshot} ฉบับ}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "สรุป"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "การโต้ตอบของผู้ใช้"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "รายงานระยะเวลาจะวิเคราะห์ระยะเวลาที่กำหนดเอง ซึ่งมักจะมีการโต้ตอบของผู้ใช้"}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "รายงานระยะเวลา"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{รายงานระยะเวลา {numTimespan} ฉบับ}other{รายงานระยะเวลา {numTimespan} ฉบับ}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "รายงานโฟลว์ผู้ใช้ Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "สำหรับเนื้อหาที่เป็นภาพเคลื่อนไหว ให้ใช้ [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) เพื่อลดการใช้ CPU ขณะที่เนื้อหาไม่ได้อยู่ในหน้าจอ"}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "ลองแสดงคอมโพเนนต์ [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) ทั้งหมดในรูปแบบ WebP โดยกำหนดการสำรองที่เหมาะสมให้กับเบราว์เซอร์อื่นด้วย [ดูข้อมูลเพิ่มเติม](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "ตรวจสอบว่าคุณกำลังใช้ [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) สำหรับรูปภาพเพื่อโหลดแบบ Lazy Loading โดยอัตโนมัติ [ดูข้อมูลเพิ่มเติม](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "ใช้เครื่องมือ เช่น [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer) เพื่อ[แสดงเลย์เอาต์ AMP ฝั่งเซิร์ฟเวอร์](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/)"}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "ดู[เอกสาร AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) เพื่อให้แน่ใจว่าระบบรองรับรูปแบบทั้งหมด"}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "คอมโพเนนต์ [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) รองรับแอตทริบิวต์ [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) เพื่อกำหนดเนื้อหารูปภาพที่จะใช้ตามขนาดของหน้าจอ [ดูข้อมูลเพิ่มเติม](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "ลองใช้การเลื่อนเสมือนจริงด้วย Component Dev Kit (CDK) หากกำลังแสดงรายการที่ใหญ่มาก [ดูข้อมูลเพิ่มเติม](https://web.dev/virtualize-lists-with-angular-cdk/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "ใช้[การแยกโค้ดระดับเส้นทาง](https://web.dev/route-level-code-splitting-in-angular/)เพื่อลดขนาดกลุ่ม JavaScript และลองแคชเนื้อหาล่วงหน้าด้วย [Angular Service Worker](https://web.dev/precaching-with-the-angular-service-worker/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "หากคุณกำลังใช้ Angular CLI โปรดตรวจสอบให้แน่ใจว่ารุ่นดังกล่าวสร้างขึ้นในโหมดที่ใช้งานจริง [ดูข้อมูลเพิ่มเติม](https://angular.io/guide/deployment#enable-runtime-production-mode)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "หากคุณกำลังใช้ Angular CLI ให้รวมแมปที่มาลงในรุ่นที่ใช้งานจริงเพื่อตรวจสอบกลุ่ม [ดูข้อมูลเพิ่มเติม](https://angular.io/guide/deployment#inspect-the-bundles)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "โหลดเส้นทางล่วงหน้าก่อนเวลาเพื่อเร่งความเร็วในการไปยังส่วนต่างๆ [ดูข้อมูลเพิ่มเติม](https://web.dev/route-preloading-in-angular/)"}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "ลองใช้ยูทิลิตี `BreakpointObserver` ใน Component Dev Kit (CDK) เพื่อจัดการเบรกพอยท์ของภาพ [ดูข้อมูลเพิ่มเติม](https://material.angular.io/cdk/layout/overview)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "ลองอัปโหลด GIF ไปยังบริการซึ่งจะทำให้ใช้ GIF เพื่อฝังเป็นวิดีโอ HTML5 ได้"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "ระบุ `@font-display` เมื่อกำหนดแบบอักษรที่กำหนดเองในธีม"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "ลองกำหนดค่า[รูปแบบรูปภาพ WebP ด้วยการแปลงสไตล์รูปภาพ](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles)ในเว็บไซต์"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "ติดตั้ง[โมดูล Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) ที่โหลดรูปภาพแบบ Lazy Loading ได้ โมดูลนี้จะช่วยเลื่อนเวลาโหลดรูปภาพนอกจอภาพเพื่อปรับปรุงประสิทธิภาพ"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "ลองใช้โมดูลเพื่อแทรก CSS และ JavaScript ที่สำคัญในหน้าหรือโมดูลที่อาจโหลดเนื้อหาแบบไม่พร้อมกันผ่าน JavaScript เช่น โมดูล[การรวม CSS/JS ขั้นสูง](https://www.drupal.org/project/advagg) โปรดระวังว่าการเพิ่มประสิทธิภาพโดยโมดูลนี้อาจทำให้เว็บไซต์เสียหาย ซึ่งน่าจะทำให้คุณต้องแก้ไขโค้ด"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "ข้อกำหนดของธีม โมดูล และเซิร์ฟเวอร์ล้วนส่งผลต่อเวลาในการตอบสนองของเซิร์ฟเวอร์ ลองหาธีมที่เพิ่มประสิทธิภาพมากขึ้น พยายามเลือกโมดูลการเพิ่มประสิทธิภาพด้วยความระมัดระวัง และ/หรืออัปเกรดเซิร์ฟเวอร์ เซิร์ฟเวอร์โฮสติ้งควรใช้ประโยชน์จากการแคช PHP Opcode, การแคชหน่วยความจำเพื่อลดเวลาในการค้นหาฐานข้อมูล เช่น Redis หรือ Memcached รวมถึงตรรกะของแอปพลิเคชันที่เพิ่มประสิทธิภาพเพื่อให้เตรียมความพร้อมของหน้าได้เร็วขึ้น"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "ลองใช้[สไตล์รูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณา](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)เพื่อลดขนาดของรูปภาพที่โหลดในหน้า หากคุณใช้มุมมองเพื่อดูรายการเนื้อหาหลายรายการในหน้า ให้ลองใช้การใส่เลขหน้าเพื่อจำกัดจำนวนของรายการเนื้อหาที่แสดงในหน้าหนึ่งๆ"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "ตรวจสอบว่าคุณได้เปิดใช้ \"รวมไฟล์ CSS\" ในหน้า \"การดูแลระบบ » การกำหนดค่า » การพัฒนา\" คุณกำหนดค่าตัวเลือกการรวมขั้นสูงยิ่งขึ้นผ่าน[โมดูลเพิ่มเติม](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search)ได้ด้วยเพื่อช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบอัดสไตล์ CSS"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "ตรวจสอบว่าคุณได้เปิดใช้ \"รวมไฟล์ JavaScript\" ในหน้า \"การดูแลระบบ » การกำหนดค่า » การพัฒนา\" คุณกำหนดค่าตัวเลือกการรวมขั้นสูงยิ่งขึ้นผ่าน[โมดูลเพิ่มเติม](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search)ได้ด้วยเพื่อช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบเนื้อหา JavaScript"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "ลองนำกฎ CSS ที่ไม่ได้ใช้ออกและแนบเฉพาะไลบรารี Drupal ที่จำเป็นลงในหน้าที่เกี่ยวข้องหรือคอมโพเนนต์ในหน้า ดูรายละเอียดได้ที่[ลิงก์เอกสารประกอบของ Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) หากต้องการระบุไลบรารีที่แนบซึ่งเพิ่ม CSS โดยไม่จำเป็น ลองเรียกใช้ [Code Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ใน DevTools ของ Chrome คุณระบุธีม/โมดูลที่รับผิดชอบได้จาก URL ของสไตล์ชีตเมื่อปิดใช้การรวม CSS ในเว็บไซต์ Drupal ของคุณ หาธีม/โมดูลที่มีสไตล์ชีตจำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากใน Code Coverage ธีม/โมดูลควรเป็นเพียงตัวกำหนดลำดับของสไตล์ชีตเท่านั้นหากใช้ธีม/โมดูลในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "ลองนำเนื้อหา JavaScript ที่ไม่ได้ใช้ออก และแนบเฉพาะไลบรารี Drupal ที่จำเป็นลงในหน้าที่เกี่ยวข้องหรือคอมโพเนนต์ในหน้า ดูรายละเอียดได้ที่[ลิงก์เอกสารประกอบของ Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library) หากต้องการระบุไลบรารีที่แนบซึ่งเพิ่ม JavaScript โดยไม่จำเป็น ลองเรียกใช้ [Code Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ใน DevTools ของ Chrome คุณระบุธีม/โมดูลที่รับผิดชอบได้จาก URL ของสคริปต์เมื่อปิดใช้การรวม JavaScript ในเว็บไซต์ Drupal ของคุณ หาธีม/โมดูลที่มีสคริปต์จำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากใน Code Coverage ธีม/โมดูลควรเป็นเพียงตัวกำหนดลำดับของสคริปต์เท่านั้นหากใช้ธีม/โมดูลในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "ตั้งค่า \"อายุสูงสุดของแคชในเบราว์เซอร์และพร็อกซี\" ในหน้า \"การดูแลระบบ » การกำหนดค่า » การพัฒนา\" อ่านเกี่ยวกับ[แคช Drupal และการเพิ่มประสิทธิภาพ](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "ลองใช้[โมดูล](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search)ที่เพิ่มประสิทธิภาพและลดขนาดของรูปภาพที่อัปโหลดผ่านเว็บไซต์โดยอัตโนมัติขณะที่ยังคงรักษาคุณภาพไว้ และตรวจสอบว่าคุณกำลังใช้[สไตล์รูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณา](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)แบบเนทีฟจาก Drupal (พร้อมใช้งานใน Drupal 8 ขึ้นไป) สำหรับรูปภาพทั้งหมดที่แสดงในเว็บไซต์"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "เพิ่มการเชื่อมต่อล่วงหน้าหรือคำแนะนำด้านทรัพยากรที่ DNS ดึงมาล่วงหน้าได้โดยติดตั้งและกำหนดค่า[โมดูล](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search)ที่มีพื้นที่สำหรับคำแนะนำด้านทรัพยากรของ User Agent"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "ตรวจสอบว่าคุณกำลังใช้[สไตล์รูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณา](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8)แบบเนทีฟจาก Drupal (พร้อมใช้งานใน Drupal 8 ขึ้นไป) ใช้สไตล์รูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณาเมื่อแสดงผลช่องรูปภาพผ่านโหมดมุมมอง มุมมอง หรือรูปภาพที่อัปโหลดผ่านตัวแก้ไข WYSIWYG"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "ใช้ [E<PERSON> Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Optimize Fonts` เพื่อใช้ประโยชน์จากฟีเจอร์ CSS สำหรับ `font-display` โดยอัตโนมัติ ซึ่งจะทำให้ผู้ใช้มองเห็นข้อความในขณะที่กำลังโหลดเว็บฟอนต์"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "ใช้ [E<PERSON> Leap](https://pubdash.ezoic.com/speed) และเปิดใช้ `Next-Gen Formats` เพื่อแปลงรูปภาพเป็นรูปแบบ WebP"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "ใช้ [E<PERSON> Leap](https://pubdash.ezoic.com/speed) แล้วเปิดใช้ `Lazy Load Images` เพื่อเลื่อนเวลาโหลดรูปภาพนอกจอภาพ"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) แล้วเปิดใช้ `Critical CSS` กับ `Script Delay` เพื่อเลื่อน JS/CSS ที่ไม่สำคัญ"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "ใช้ [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) เพื่อแคชเนื้อหาของคุณในเครือข่ายทั่วโลก ซึ่งจะช่วยปรับปรุง Time To First Byte"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Minify CSS` เพื่อลดขนาด CSS โดยอัตโนมัติ ซึ่งจะทำให้ขนาดเพย์โหลดของเครือข่ายเล็กลง"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "ใช้ [E<PERSON> Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Minify Javascript` เพื่อลดขนาด JS โดยอัตโนมัติ ซึ่งจะทำให้ขนาดเพย์โหลดของเครือข่ายเล็กลง"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Remove Unused CSS` เพื่อช่วยในการแก้ไขปัญหานี้ โดยการตั้งค่านี้จะระบุคลาสของ CSS ที่มีการใช้งานจริงในแต่ละหน้าของเว็บไซต์ และจะนำคลาสอื่นๆ ออกเพื่อควบคุมให้ไฟล์มีขนาดเล็ก"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Efficient Static Cache Policy` เพื่อตั้งค่าที่แนะนำในส่วนหัวของการแคชสำหรับเนื้อหาแบบคงที่"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "ใช้ [E<PERSON> Leap](https://pubdash.ezoic.com/speed) และเปิดใช้ `Next-Gen Formats` เพื่อแปลงรูปภาพเป็นรูปแบบ WebP"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Pre-Connect Origins` เพื่อเพิ่มคำแนะนำด้านทรัพยากรสำหรับ `preconnect` โดยอัตโนมัติ ซึ่งจะสร้างการเชื่อมต่อกับต้นทางที่สำคัญของบุคคลที่สามไว้ก่อน"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Preload Fonts` กับ `Preload Background Images` เพื่อเพิ่มลิงก์ `preload` ซึ่งจะช่วยจัดลำดับความสำคัญของการดึงทรัพยากรที่มีการขอภายหลังในการโหลดหน้าเว็บ"}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "ใช้ [Ezoic Leap](https://pubdash.ezoic.com/speed) และเปิดใช้งาน `Resize Images` เพื่อปรับขนาดรูปภาพให้เหมาะสมกับอุปกรณ์ ซึ่งจะทำให้ขนาดเพย์โหลดของเครือข่ายเล็กลง"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "ลองอัปโหลด GIF ไปยังบริการซึ่งจะทำให้ใช้ GIF เพื่อฝังเป็นวิดีโอ HTML5 ได้"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "พิจารณาใช้[ปลั๊กอิน](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp)หรือบริการที่จะแปลงรูปภาพที่อัปโหลดเป็นรูปแบบที่เหมาะสมที่สุดโดยอัตโนมัติ"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "ติดตั้ง[ปลั๊กอินการโหลดแบบ Lazy Loading ของ Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) ที่จะช่วยให้เลื่อนเวลาโหลดรูปภาพนอกจอภาพได้ หรือเปลี่ยนไปใช้เทมเพลตที่มีฟังก์ชันดังกล่าว เริ่มตั้งแต่ Joomla 4.0 เป็นต้นไป รูปภาพใหม่ทั้งหมดจะได้รับแอตทริบิวต์ `loading` จาก Core [โดยอัตโนมัติ](https://github.com/joomla/joomla-cms/pull/30748)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "มีปลั๊กอิน Joomla หลายรายการที่ช่วยคุณ[แทรกเนื้อหาสำคัญในหน้า](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)หรือ[เลื่อนเวลาโหลดทรัพยากรที่สำคัญน้อยกว่า](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ได้ โปรดทราบว่าการเพิ่มประสิทธิภาพที่ได้จากปลั๊กอินเหล่านี้อาจทำให้ฟีเจอร์ของเทมเพลตหรือปลั๊กอินเสียหาย คุณจึงจะต้องทดสอบปลั๊กอินเหล่านี้อย่างละเอียด"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "ข้อกำหนดของเทมเพลต ส่วนขยาย และเซิร์ฟเวอร์ล้วนส่งผลต่อเวลาในการตอบสนองของเซิร์ฟเวอร์ ลองหาเทมเพลตที่เพิ่มประสิทธิภาพมากขึ้น พยายามเลือกส่วนขยายการเพิ่มประสิทธิภาพด้วยความระมัดระวัง และ/หรืออัปเกรดเซิร์ฟเวอร์"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "ลองแสดงข้อความที่ตัดตอนมาในหมวดหมู่บทความ (เช่น ผ่านลิงก์ \"อ่านเพิ่มเติม\") ลดจำนวนบทความที่แสดงในหน้าหนึ่งๆ แบ่งโพสต์ยาวๆ เป็นหลายหน้า หรือใช้ปลั๊กอินเพื่อโหลดความคิดเห็นแบบ Lazy Loading"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "มี[ส่วนขยาย Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) หลายรายการที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบอัดสไตล์ CSS นอกจากนี้ยังมีเทมเพลตที่มีฟังก์ชันการทำงานนี้ด้วย"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "มี[ส่วนขยาย <PERSON>om<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) หลายรายการที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบอัดสคริปต์ นอกจากนี้ยังมีเทมเพลตที่มีฟังก์ชันการทำงานนี้ด้วย"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "ลองลดหรือเปลี่ยนจำนวน[ส่วนขยาย Joomla](https://extensions.joomla.org/) ที่โหลด CSS ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุส่วนขยายที่เพิ่ม CSS โดยไม่จำเป็น ลองเรียกใช้ [Code Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ใน DevTools ของ Chrome คุณระบุธีม/ปลั๊กอินที่รับผิดชอบได้จาก URL ของสไตล์ชีต หาปลั๊กอินที่มีสไตล์ชีตจำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากใน Code Coverage ปลั๊กอินควรเป็นเพียงตัวกำหนดลำดับของสไตล์ชีตเท่านั้นหากใช้ปลั๊กอินในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "ลองลดหรือเปลี่ยนจำนวน[ส่วนขยาย Joomla](https://extensions.joomla.org/) ที่โหลด JavaScript ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุปลั๊กอินที่เพิ่ม JS โดยไม่จำเป็น ลองเรียกใช้ [Code Coverage](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) ใน DevTools ของ Chrome คุณระบุส่วนขยายที่รับผิดชอบได้จาก URL ของสคริปต์ หาธีม/โมดูลที่มีสคริปต์จำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากใน Code Coverage ส่วนขยายควรเป็นเพียงตัวกำหนดลำดับของสคริปต์เท่านั้นหากใช้ส่วนขยายในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "อ่านเกี่ยวกับ[การแคชของเบราว์เซอร์ใน <PERSON><PERSON><PERSON>](https://docs.joomla.org/Cache)"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "ลองใช้[ปลั๊กอินการเพิ่มประสิทธิภาพรูปภาพ](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance)ที่บีบอัดรูปภาพแต่ยังคงคุณภาพไว้ได้"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "ลองใช้[ปลั๊กอินรูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณา](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images)เพื่อใช้รูปภาพที่ปรับเปลี่ยนตามพื้นที่โฆษณาในเนื้อหาของคุณ"}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "คุณเปิดใช้การบีบอัดข้อความได้โดยเปิดใช้การบีบอัดหน้าของ Gzip ใน <PERSON><PERSON><PERSON> (ระบบ > การกำหนดค่าส่วนกลาง > เซิร์ฟเวอร์)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "หากไม่ได้รวมกลุ่มเนื้อหา JavaScript ให้ลองใช้ [Baler](https://github.com/magento/baler)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "ปิดใช้[การรวมกลุ่มและการลดขนาดของ JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ในตัวของ Magento และลองใช้ [Baler](https://github.com/magento/baler/) แทน"}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "ระบุ `@font-display` เมื่อ [ กำหนดฟอนต์ที่กำหนดเอง ](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "ลองค้นหาใน[ตลาดกลางของ Magento](https://marketplace.magento.com/catalogsearch/result/?q=webp) เพื่อหาส่วนขยายต่างๆ ของบุคคลที่สามเพื่อใช้ประโยชน์จากรูปแบบรูปภาพที่ใหม่กว่า"}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "ลองแก้ไขเทมเพลตผลิตภัณฑ์และแคตตาล็อกเพื่อใช้ประโยชน์จากฟีเจอร์[การโหลดแบบ Lazy Loading](https://web.dev/native-lazy-loading) ของแพลตฟอร์มเว็บ"}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "ใช้[การผสานรวม Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html) ของ Magento"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "เปิดใช้ตัวเลือก \"Minify CSS Files\" ในการตั้งค่านักพัฒนาซอฟต์แวร์ของร้านค้า [ดูข้อมูลเพิ่มเติม](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "ใช้ [Terser](https://www.npmjs.com/package/terser) เพื่อลดขนาดเนื้อหาของ JavaScript ทั้งหมดจากการใช้งานเนื้อหาแบบคงที่และปิดใช้ฟีเจอร์การลดขนาดในตัว"}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "ปิดใช้[การรวมกลุ่ม JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ในตัวของ Magento"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "ลองค้นหาใน[ตลาดกลางของ Magento](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) เพื่อหาส่วนขยายต่างๆ ของบุคคลที่สามเพื่อเพิ่มประสิทธิภาพให้รูปภาพ"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "เพิ่มการเชื่อมต่อล่วงหน้าหรือคำแนะนำด้านทรัพยากรที่ DNS ดึงมาล่วงหน้าได้โดย[แก้ไขเลย์เอาต์ของธีม](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)"}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "เพิ่มแท็ก `<link rel=preload>` ได้โดย[แก้ไขเลย์เอาต์ของธีม](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "ใช้คอมโพเนนต์ `next/image` แทน `<img>` เพื่อเพิ่มประสิทธิภาพรูปแบบรูปภาพโดยอัตโนมัติ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "ใช้คอมโพเนนต์ `next/image` แทน `<img>` เพื่อโหลดรูปภาพแบบ Lazy Loading โดยอัตโนมัติ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "ใช้คอมโพเนนต์ `next/image` และตั้ง \"ลำดับความสำคัญ\" เป็น \"จริง\" เพื่อโหลดรูปภาพ LCP ไว้ล่วงหน้า [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/api-reference/next/image#priority)"}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "ใช้คอมโพเนนต์ `next/script` เพื่อเลื่อนการโหลดสคริปต์บุคคลที่สามที่ไม่สำคัญ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/basic-features/script)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "ใช้คอมโพเนนต์ `next/image` เพื่อตรวจสอบว่ารูปภาพมีขนาดที่เหมาะสมเสมอ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/api-reference/next/image#width)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "พิจารณาตั้งค่า `PurgeCSS` ในการกำหนดค่า `Next.js` เพื่อนำกฎที่ไม่ได้ใช้ออกจากสไตล์ชีต [ดูข้อมูลเพิ่มเติม](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "ใช้ `Webpack Bundle Analyzer` เพื่อตรวจหาโค้ด JavaScript ที่ไม่ได้ใช้ [ดูข้อมูลเพิ่มเติม](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "พิจารณาใช้ `Next.js Analytics` เพื่อวัดประสิทธิภาพแอปในการใช้งานจริง [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/advanced-features/measuring-performance)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "กำหนดค่าการแคชสำหรับเนื้อหาและหน้า `Server-side Rendered` (SSR) ที่เปลี่ยนแปลงไม่ได้ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/going-to-production#caching)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "ใช้คอมโพเนนต์ `next/image` แทน `<img>` เพื่อปรับคุณภาพของรูป [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/basic-features/image-optimization)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "ใช้คอมโพเนนต์ `next/image` เพื่อกำหนด `sizes` ที่เหมาะสม [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "เปิดใช้การบีบอัดในเซิร์ฟเวอร์ Next.js ของคุณ [ดูข้อมูลเพิ่มเติม](https://nextjs.org/docs/api-reference/next.config.js/compression)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และกำหนด `format=\"webp\"` [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#format)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และกำหนด `loading=\"lazy\"` สำหรับรูปภาพนอกจอภาพ [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#loading)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และระบุ `preload` สำหรับรูปภาพ LCP [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#preload)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และระบุ `width` และ `height` ที่ชัดแจ้ง [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#width--height)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และกำหนด `quality` ที่เหมาะสม [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#quality)"}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "ใช้คอมโพเนนต์ `nuxt/image` และกำหนด `sizes` ที่เหมาะสม [ดูข้อมูลเพิ่มเติม](https://image.nuxtjs.org/components/nuxt-img#sizes)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[นำวิดีโอมาแทนที่ GIF แบบภาพเคลื่อนไหว](https://web.dev/replace-gifs-with-videos/)เพื่อให้หน้าเว็บโหลดเร็วขึ้น และลองใช้รูปแบบไฟล์สมัยใหม่ เช่น [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) หรือ [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) เพื่อปรับปรุงประสิทธิภาพในการบีบอัดมากกว่า 30% เมื่อเทียบกับ VP9 ซึ่งเป็นตัวแปลงรหัสวิดีโอที่ทันสมัยซึ่งใช้อยู่ในปัจจุบัน"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "ลองใช้[ปลั๊กอิน](https://octobercms.com/plugins?search=image)หรือบริการที่จะแปลงรูปภาพที่อัปโหลดเป็นรูปแบบที่เหมาะสมที่สุดโดยอัตโนมัติ [รูปภาพ WebP ที่ไม่เสียรายละเอียด](https://developers.google.com/speed/webp)มีขนาดเล็กกว่ารูปภาพ PNG 26% และมีขนาดเล็กกว่ารูปภาพ JPEG ที่เทียบกันได้ 25-34% ที่ดัชนีคุณภาพ SSIM ที่เทียบเท่า คุณอาจลองใช้รูปแบบสมัยใหม่สำหรับรูปภาพอีกรูปแบบหนึ่ง นั่นคือ [AVIF](https://jakearchibald.com/2020/avif-has-landed/)"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "ลองติดตั้ง[ปลั๊กอินการโหลดรูปภาพแบบ Lazy Loading](https://octobercms.com/plugins?search=lazy) ที่จะช่วยให้เลื่อนเวลาโหลดรูปภาพนอกจอภาพได้ หรือเปลี่ยนไปใช้ธีมที่มีฟังก์ชันดังกล่าว และอาจลองใช้[ปลั๊กอิน AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages) ด้วย"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "มีปลั๊กอินจำนวนมากที่ช่วยให้ใช้งาน[เนื้อหาสำคัญในหน้า](https://octobercms.com/plugins?search=css)ได้ดีขึ้น ปลั๊กอินเหล่านี้อาจทำให้ปลั๊กอินอื่นๆ ทำงานผิดพลาด คุณจึงควรทดสอบอย่างละเอียดถี่ถ้วน"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "ข้อกำหนดของธีม ปลั๊กอิน และเซิร์ฟเวอร์ล้วนส่งผลต่อเวลาการตอบสนองของเซิร์ฟเวอร์ ลองหาธีมที่เพิ่มประสิทธิภาพมากขึ้น เลือกปลั๊กอินการเพิ่มประสิทธิภาพอย่างระมัดระวัง และ/หรืออัปเกรดเซิร์ฟเวอร์ October CMS ช่วยให้นักพัฒนาซอฟต์แวร์สามารถใช้ [`Queues`](https://octobercms.com/docs/services/queues) เพื่อเลื่อนการประมวลผลงานที่ใช้เวลานานออกไป เช่น การส่งอีเมล ซึ่งทำให้คำขอเว็บทำงานเร็วขึ้นอย่างมาก"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "ลองแสดงข้อความที่ตัดตอนมาในรายการโพสต์ (เช่น การใช้ปุ่ม `show more`) ลดจำนวนโพสต์ที่แสดงในหน้าเว็บหนึ่งๆ แบ่งโพสต์ยาวๆ เป็นหลายหน้า หรือใช้ปลั๊กอินเพื่อโหลดความคิดเห็นแบบ Lazy Loading"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "มี[ปลั๊กอิน](https://octobercms.com/plugins?search=css)จำนวนมากที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบอัดสไตล์ การใช้กระบวนการบิลด์เพื่อลดขนาดล่วงหน้าจะช่วยให้การพัฒนาเร็วขึ้นได้"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "มี[ปลั๊กอิน](https://octobercms.com/plugins?search=javascript)จำนวนมากที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการเชื่อมโยง การลดขนาด และการบีบอัดสคริปต์ การใช้กระบวนการบิลด์เพื่อลดขนาดล่วงหน้าจะช่วยให้การพัฒนาเร็วขึ้นได้"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "ลองตรวจสอบ[ปลั๊กอิน](https://octobercms.com/plugins)ที่โหลด CSS ที่ไม่ได้ใช้งานในเว็บไซต์ หากต้องการระบุปลั๊กอินที่เพิ่ม CSS โดยที่ไม่จำเป็น ให้เรียกใช้[การครอบคลุมของโค้ด](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome ระบุธีม/ปลั๊กอินที่ดำเนินการดังกล่าวได้จาก URL ของสไตล์ชีต หาปลั๊กอินที่มีสไตล์ชีตจำนวนมากซึ่งมีสีแดงอยู่หลายแห่งในการครอบคลุมของโค้ด ปลั๊กอินควรจะเพิ่มสไตล์ชีตเฉพาะเมื่อมีการใช้งานจริงๆ ในหน้าเว็บเท่านั้น"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "ลองตรวจสอบ[ปลั๊กอิน](https://octobercms.com/plugins?search=javascript)ที่โหลด JavaScript ที่ไม่ได้ใช้งานในหน้าเว็บ หากต้องการระบุปลั๊กอินที่เพิ่ม JavaScript โดยที่ไม่จำเป็น ให้เรียกใช้[การครอบคลุมของโค้ด](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage)ในเครื่องมือสำหรับนักพัฒนาเว็บใน Chrome ระบุธีม/ปลั๊กอินที่ดำเนินการดังกล่าวได้จาก URL ของสคริปต์ หาปลั๊กอินที่มีสคริปต์จำนวนมากซึ่งมีสีแดงอยู่หลายแห่งในการครอบคลุมของโค้ด ปลั๊กอินควรจะเพิ่มสคริปต์เฉพาะเมื่อมีการใช้งานจริงๆ ในหน้าเว็บเท่านั้น"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "อ่านเกี่ยวกับ[การป้องกันคำขอเครือข่ายที่ไม่จำเป็นด้วยแคช HTTP](https://web.dev/http-cache/#caching-checklist) มี[ปลั๊กอิน](https://octobercms.com/plugins?search=Caching)จำนวนมากที่ช่วยให้การแคชเร็วขึ้นได้"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "ลองใช้[ปลั๊กอินการเพิ่มประสิทธิภาพรูปภาพ](https://octobercms.com/plugins?search=image)เพื่อบีบอัดรูปภาพโดยยังคงคุณภาพไว้ดังเดิม"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "อัปโหลดรูปภาพโดยตรงในโปรแกรมจัดการสื่อเพื่อให้แน่ใจว่าจะมีรูปภาพขนาดตามที่กำหนดให้ใช้งาน ลองใช้[ฟิลเตอร์การปรับขนาด](https://octobercms.com/docs/markup/filter-resize)หรือ[ปลั๊กอินการปรับขนาดรูปภาพ](https://octobercms.com/plugins?search=image)เพื่อให้มีการใช้รูปภาพที่มีขนาดเหมาะสมที่สุด"}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "เปิดใช้การบีบอัดข้อความในการกำหนดค่าเว็บเซิร์ฟเวอร์"}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "ลองใช้ไลบรารี \"การสร้างกรอบเวลา\" เช่น `react-window` เพื่อลดจำนวนโหนดของ DOM ที่สร้างขึ้นให้เหลือน้อยที่สุดหากคุณแสดงผลองค์ประกอบซ้ำกันหลายรายการในหน้านั้น [ดูข้อมูลเพิ่มเติม](https://web.dev/virtualize-long-lists-react-window/) นอกจากนี้ให้ลดการแสดงผลซ้ำที่ไม่จำเป็นให้เหลือน้อยที่สุดโดยใช้ [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) หรือ [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) และ[ข้ามเอฟเฟกต์](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects)ไปจนกว่าทรัพยากร Dependency บางรายการจะมีการเปลี่ยนแปลงในกรณีที่คุณใช้ฮุก `Effect` เพื่อปรับปรุงประสิทธิภาพของรันไทม์"}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "หากคุณกำลังใช้ React Router ให้ลดการใช้ของคอมโพเนนต์ `<Redirect>` ในการ[ไปยังเส้นทางต่างๆ](https://reacttraining.com/react-router/web/api/Redirect)"}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "หากคุณกำลังแสดงคอมโพเนนต์ของ React ใดๆ บนฝั่งเซิร์ฟเวอร์ ให้ลองใช้ `renderToPipeableStream()` หรือ `renderToStaticNodeStream()` เพื่อให้ลูกค้าได้รับและรวมส่วนต่างๆ ของมาร์กอัปแทนที่จะรวมทั้งหมดในคราวเดียว [ดูข้อมูลเพิ่มเติม](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "หากระบบในรุ่นของคุณลดขนาดไฟล์ CSS โดยอัตโนมัติ โปรดตรวจสอบว่าคุณทำให้รุ่นที่ใช้งานจริงของแอปพลิเคชันใช้งานได้ โดยใช้ส่วนขยาย React Developer Tools [ดูข้อมูลเพิ่มเติม](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "หากระบบในรุ่นของคุณลดขนาดไฟล์ JS โดยอัตโนมัติ โปรดตรวจสอบว่าคุณทำให้รุ่นที่ใช้งานจริงของแอปพลิเคชันใช้งานได้ โดยใช้ส่วนขยาย React Developer Tools [ดูข้อมูลเพิ่มเติม](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)"}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "หากคุณไม่ได้กำลังแสดงผลฝั่งเซิร์ฟเวอร์ ให้[แยกกลุ่ม JavaScript](https://web.dev/code-splitting-suspense/) ด้วย `React.lazy()` หรือแยกโค้ดโดยใช้ไลบรารีของบุคคลที่สาม เช่น [คอมโพเนนต์ที่โหลดได้](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)"}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "ใช้ React DevTools Profiler ซึ่งใช้ประโยชน์จาก Profiler API ในการวัดประสิทธิภาพในการแสดงผลของคอมโพเนนต์ [ดูข้อมูลเพิ่มเติม](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "ลองอัปโหลด GIF ไปยังบริการซึ่งจะทำให้ใช้ GIF เพื่อฝังเป็นวิดีโอ HTML5 ได้"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "พิจารณาใช้ปลั๊กอิน [Performance Lab](https://wordpress.org/plugins/performance-lab/) เพื่อแปลงรูปภาพ JPEG ที่อัปโหลดเป็น WebP โดยอัตโนมัติในทุกที่ที่รองรับ"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "ติดตั้ง[ปลั๊กอินการโหลดแบบ Lazy Loading ของ WordPress](https://wordpress.org/plugins/search/lazy+load/) ที่จะช่วยเลื่อนเวลาโหลดรูปภาพนอกหน้าจอ หรือเปลี่ยนไปใช้ธีมที่มีฟังก์ชันดังกล่าว และอาจลองพิจารณาใช้[ปลั๊กอิน AMP](https://wordpress.org/plugins/amp/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "มีปลั๊กอิน WordPress หลายรายการที่ช่วยคุณ[แทรกเนื้อหาที่สำคัญ](https://wordpress.org/plugins/search/critical+css/) หรือ[เลื่อนเวลาโหลดทรัพยากรที่สำคัญน้อยกว่า](https://wordpress.org/plugins/search/defer+css+javascript/) โปรดระวังว่าการเพิ่มประสิทธิภาพโดยปลั๊กอินเหล่านี้อาจทำให้ฟีเจอร์ของธีมหรือปลั๊กอินของคุณเสียหาย ซึ่งน่าจะทำให้คุณต้องแก้ไขโค้ด"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "การกำหนดธีม ปลั๊กอิน และเซิร์ฟเวอร์ล้วนส่งผลต่อเวลาการตอบสนองของเซิร์ฟเวอร์ ลองหาธีมที่เพิ่มประสิทธิภาพมากขึ้น พยายามเลือกปลั๊กอินการเพิ่มประสิทธิภาพด้วยความระมัดระวัง และ/หรืออัปเกรดเซิร์ฟเวอร์"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "ลองแสดงข้อความที่ตัดตอนมาในรายการโพสต์ (เช่น ผ่านแท็ก \"เพิ่มเติม\") ลดจำนวนโพสต์ที่แสดงในหน้าหนึ่งๆ แบ่งโพสต์ยาวๆ เป็นหลายหน้า หรือใช้ปลั๊กอินเพื่อโหลดความคิดเห็นแบบ Lazy Loading"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "มี[ปลั๊กอิน WordPress](https://wordpress.org/plugins/search/minify+css/) หลายอย่างที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการลิงก์ ลดขนาด และบีบอัดสไตล์ นอกจากนี้คุณอาจใช้กระบวนการของเวอร์ชันเพื่อลดขนาดล่วงหน้าหากเป็นไปได้"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "มี[ปลั๊กอิน WordPress](https://wordpress.org/plugins/search/minify+javascript/) หลายอย่างที่ช่วยให้เว็บไซต์เร็วขึ้นได้ด้วยการลิงก์ ลดขนาด และบีบอัดสคริปต์ นอกจากนี้คุณอาจใช้กระบวนการของเวอร์ชันเพื่อลดขนาดล่วงหน้าหากเป็นไปได้"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "ลองลดหรือเปลี่ยนจำนวน[ปลั๊กอิน WordPress](https://wordpress.org/plugins/) ที่โหลด CSS ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุปลั๊กอินที่เพิ่ม CSS โดยไม่จำเป็น ให้ลองเรียกใช้[การครอบคลุมโค้ด](https://developer.chrome.com/docs/devtools/coverage/)ใน DevTools ของ Chrome คุณระบุธีม/ปลั๊กอินที่รับผิดชอบได้จาก URL ของสไตล์ชีต หาปลั๊กอินที่มีสไตล์ชีตจำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากในการครอบคลุมโค้ด ปลั๊กอินควรเป็นเพียงตัวกำหนดลำดับของสไตล์ชีตเท่านั้นหากใช้ปลั๊กอินในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "ลองลดหรือเปลี่ยนจำนวน[ปลั๊กอิน WordPress](https://wordpress.org/plugins/) ที่โหลด JavaScript ที่ไม่ได้ใช้ในหน้าเว็บของคุณ หากต้องการระบุปลั๊กอินที่เพิ่ม JS โดยไม่จำเป็น ให้ลองเรียกใช้ [การครอบคลุมโค้ด](https://developer.chrome.com/docs/devtools/coverage/)ใน DevTools ของ Chrome คุณระบุธีม/ปลั๊กอินที่รับผิดชอบได้จาก URL ของสคริปต์ หาปลั๊กอินที่มีสคริปต์จำนวนมากอยู่ในรายการซึ่งมีสีแดงอยู่จำนวนมากในการครอบคลุมโค้ด ปลั๊กอินควรเป็นเพียงตัวกำหนดลำดับของสคริปต์เท่านั้นหากใช้ปลั๊กอินในหน้าจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "อ่านเกี่ยวกับ[การแคชของเบราว์เซอร์ใน WordPress](https://wordpress.org/support/article/optimization/#browser-caching)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "พิจารณาใช้[ปลั๊กอิน WordPress การเพิ่มประสิทธิภาพรูปภาพ](https://wordpress.org/plugins/search/optimize+images/)ที่บีบอัดรูปภาพแต่ยังคงคุณภาพไว้ได้"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "อัปโหลดรูปภาพโดยตรงผ่าน[ไลบรารีสื่อ](https://wordpress.org/support/article/media-library-screen/)เพื่อให้แน่ใจว่ามีขนาดรูปภาพที่จำเป็นพร้อมใช้งาน จากนั้นแทรกรูปภาพจากไลบรารีสื่อหรือใช้วิดเจ็ตรูปภาพเพื่อให้มีการใช้ขนาดรูปภาพที่มีประสิทธิภาพสูงสุด (รวมถึงขนาดสำหรับเบรกพอยท์ที่ปรับเปลี่ยนตามพื้นที่โฆษณา) หลีกเลี่ยงการใช้รูปภาพ`Full Size` นอกเสียจากว่าขนาดจะเพียงพอต่อการใช้งาน [ดูข้อมูลเพิ่มเติม](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "คุณเปิดใช้การบีบอัดข้อความในการกำหนดค่าเว็บเซิร์ฟเวอร์ได้"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "เปิดใช้ \"Imagify\" จากแท็บการเพิ่มประสิทธิภาพรูปภาพใน \"WP Rocket\" เพื่อแปลงรูปภาพเป็นรูปแบบ WebP"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "เปิดใช้ [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) ใน WP Rocket เพื่อแก้ไขคำแนะนำนี้ ฟีเจอร์นี้จะทำให้การโหลดรูปภาพล่าช้าจนกว่าผู้เข้าชมจะเลื่อนหน้าเว็บลงและต้องการเห็นรูปภาพจริงๆ"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "เปิดใช้[นำ CSS ที่ไม่ได้ใช้ออก](https://docs.wp-rocket.me/article/1529-remove-unused-css) และ[โหลด JavaScript ที่มีการเลื่อนออกไป](https://docs.wp-rocket.me/article/1265-load-javascript-deferred)ใน \"WP Rocket\" เพื่อจัดการกับคำแนะนำนี้ ฟีเจอร์เหล่านี้จะเพิ่มประสิทธิภาพไฟล์ CSS และ JavaScript ตามลำดับเพื่อให้ไม่บล็อกการแสดงผลหน้าเว็บของคุณ"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "เปิดใช้[ไฟล์ Minify CSS](https://docs.wp-rocket.me/article/1350-css-minify-combine) ใน \"WP Rocket\" เพื่อแก้ไขปัญหานี้ ระบบจะนำพื้นที่ว่างและความคิดเห็นในไฟล์ CSS ของเว็บไซต์ออกเพื่อให้ไฟล์มีขนาดเล็กลงและดาวน์โหลดได้เร็วขึ้น"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "เปิดใช้[ไฟล์ Minify JavaScript](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) ใน \"WP Rocket\" เพื่อแก้ไขปัญหานี้ ระบบจะนำพื้นที่ว่างและความคิดเห็นออกจากไฟล์ JavaScript เพื่อให้ไฟล์มีขนาดเล็กลงและดาวน์โหลดได้เร็วขึ้น"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "เปิดใช้[นำ CSS ที่ไม่ได้ใช้ออก](https://docs.wp-rocket.me/article/1529-remove-unused-css)ใน \"WP Rocket\" เพื่อแก้ไขปัญหานี้ วิธีนี้จะลดขนาดหน้าเว็บโดยการนำ CSS และสไตล์ชีตทั้งหมดที่ไม่ได้ใช้ออก ขณะเดียวกันก็เก็บเฉพาะ CSS ที่ใช้ในแต่ละหน้าไว้"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "เปิดใช้[การดำเนินการ Delay JavaScript](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) ใน \"WP Rocket\" เพื่อแก้ไขปัญหานี้ การดำเนินการนี้จะช่วยปรับปรุงการโหลดหน้าเว็บโดยชะลอการดำเนินการของสคริปต์จนกว่าผู้ใช้จะโต้ตอบ หากเว็บไซต์มี iframe คุณจะใช้ [LazyLoad สำหรับ iframe และวิดีโอ](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos)ของ WP Rocket และ[แทนที่ YouTube iframe ด้วยรูปภาพตัวอย่าง](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)ได้เช่นกัน"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "เปิดใช้ \"Imagify\" จากแท็บการเพิ่มประสิทธิภาพรูปภาพใน \"WP Rocket\" และเรียกใช้การเพิ่มประสิทธิภาพแบบกลุ่มเพื่อบีบอัดรูปภาพ"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "ใช้[คำขอการดึงข้อมูล DNS ล่วงหน้า](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests)ใน \"WP Rocket\" เพื่อเพิ่ม \"dns-prefetch\" และเร่งการเชื่อมต่อกับโดเมนภายนอก นอกจากนี้ \"WP Rocket\" ยังเพิ่ม \"preconnect\" ไปยัง[โดเมน Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) และ CNAME ที่เพิ่มผ่านฟีเจอร์[เปิดใช้ CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) โดยอัตโนมัติด้วย"}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "หากต้องการแก้ไขปัญหานี้สำหรับแบบอักษร ให้เปิดใช้[นำ CSS ที่ไม่ได้ใช้ออก](https://docs.wp-rocket.me/article/1529-remove-unused-css)ใน \"WP Rocket\" ระบบจะโหลดแบบอักษรสำคัญของเว็บไซต์ไว้ล่วงหน้าโดยคำนึงถึงลำดับความสำคัญ"}, "report/renderer/report-utils.js | calculatorLink": {"message": "ดูเครื่องคิดเลข"}, "report/renderer/report-utils.js | collapseView": {"message": "ยุบมุมมอง"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "การนำทางเริ่มต้น"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "เวลาในการตอบสนองของเส้นทางสำคัญที่ยาวที่สุด"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "คัดลอก JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "เปิด/ปิดธีมมืด"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "ขยายข้อมูลการพิมพ์"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "สรุปการพิมพ์"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "บันทึกเป็น Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "บันทึกเป็น HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "บันทึกเป็น JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "เปิดในโปรแกรมดู"}, "report/renderer/report-utils.js | errorLabel": {"message": "ข้อผิดพลาด!"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "ข้อผิดพลาดในรายงาน: ไม่มีข้อมูลการตรวจสอบ"}, "report/renderer/report-utils.js | expandView": {"message": "ขยายมุมมอง"}, "report/renderer/report-utils.js | footerIssue": {"message": "รายงานปัญหา"}, "report/renderer/report-utils.js | hide": {"message": "ซ่อน"}, "report/renderer/report-utils.js | labDataTitle": {"message": "ข้อมูลในห้องทดลอง"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "การวิเคราะห์หน้าปัจจุบันในเครือข่ายมือถือจำลองโดย [Lighthouse](https://developers.google.com/web/tools/lighthouse/) ค่ามาจากการประมาณและอาจแตกต่างกันไป"}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "รายการเพิ่มเติมที่ควรตรวจสอบด้วยตนเอง"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "ไม่เกี่ยวข้อง"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "โอกาส"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "เวลาที่ประหยัดได้โดยประมาณ"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "การตรวจสอบที่ผ่านแล้ว"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "การโหลดหน้าเว็บเริ่มต้น"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "การควบคุมที่กำหนดเอง"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "เดสก์ท็อปจำลอง"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "ไม่มีการจำลอง"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "เวอร์ชัน Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "ความแรงของ CPU/หน่วยความจำที่ไม่มีการควบคุม"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "การควบคุม CPU"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "อุปกรณ์"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "การควบคุมเครือข่าย"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "การจำลองหน้าจอ"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "User Agent (เครือข่าย)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "การโหลดหน้าเว็บ 1 ครั้ง"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "ข้อมูลนี้มาจากการโหลดหน้าเว็บ 1 ครั้ง ซึ่งตรงกันข้ามกับข้อมูลภาคสนามที่สรุปหลายเซสชัน"}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "การควบคุม 4G แบบช้า"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "ไม่ทราบ"}, "report/renderer/report-utils.js | show": {"message": "แสดง"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "แสดงการตรวจสอบที่เกี่ยวข้องกับรายการต่อไปนี้"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "ยุบตัวอย่างข้อมูล"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "ขยายตัวอย่างข้อมูล"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "แสดงทรัพยากรของบุคคลที่สาม"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "ให้บริการโดยสภาพแวดล้อม"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "เกิดปัญหาที่มีผลต่อการทำงานนี้ของ Lighthouse"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "ค่ามาจากการประมาณและอาจแตกต่างกันไป [คะแนนประสิทธิภาพคำนวณ](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)จากเมตริกเหล่านี้โดยตรง"}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "ดูการติดตามดั้งเดิม"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "ดูการติดตาม"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "ดูแผนภูมิทรีแม็ป"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "ผ่านการตรวจสอบแต่มีคำเตือน"}, "report/renderer/report-utils.js | warningHeader": {"message": "คำเตือน "}, "treemap/app/src/util.js | allLabel": {"message": "ทั้งหมด"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "สคริปต์ทั้งหมด"}, "treemap/app/src/util.js | coverageColumnName": {"message": "การครอบคลุม"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "โมดูลที่ซ้ำกัน"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "ขนาดไฟล์ทรัพยากร (ไบต์)"}, "treemap/app/src/util.js | tableColumnName": {"message": "ชื่อ"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "ซ่อน/แสดงตาราง"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "ไบต์ที่ไม่ได้ใช้"}}