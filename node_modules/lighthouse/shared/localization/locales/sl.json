{"core/audits/accessibility/accesskeys.js | description": {"message": "Tipke za dostop uporabnikom omogočajo hiter izbor dela strani. Za ustrezno pomikanje mora biti vsaka tipka za dostop drugačna. [Preberite več o tipkah za dostop](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>[accesskey]` niso en<PERSON>"}, "core/audits/accessibility/accesskeys.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[accesskey]` so enoli<PERSON>ne"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "Vsak element ARIA `role` podpira določen podniz atributov `aria-*`. <PERSON>e se ne ujemajo, so atributi `aria-*` razveljavljeni. [Preberite, kako povežete atribute ARIA z njihovimi vlogami](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Atributi `[aria-*]` se ne ujemajo z vlogami"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Atributi `[aria-*]` se ujemajo s svojimi vlogami"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Če element nima dostopnega imena, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [<PERSON><PERSON><PERSON>, kako izboljšate dostop do elementov ukaza](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "Elementi `button`, `link` in `menuitem` nimajo dostopnih imen."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "Elementi `button`, `link` in `menuitem` imajo dostopna imena."}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "<PERSON>d<PERSON><PERSON> tehnologije, kot so <PERSON>ln<PERSON>, <PERSON><PERSON><PERSON><PERSON>, če je atribut `aria-hidden=\"true\"` nastavljen na dokument `<body>`. [<PERSON><PERSON><PERSON>, kako `aria-hidden` vpliva na telo dokumenta](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Atribut `[aria-hidden=\"true\"]` je nastavljen na dokumentu `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Atribut `[aria-hidden=\"true\"]` ni nastavljen na dokumentu `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Podrejeni elementi elementa `[aria-hidden=\"true\"]`, ki jih je mogoče iz<PERSON>ti, pre<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da bi bili tisti interaktivni elementi na voljo uporabnikom podpornih tehnologij, kot so bralniki zaslona. [<PERSON><PERSON><PERSON>, kako `aria-hidden` vpliva na elemente, ki jih je mogoče izbrati](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Elementi `[aria-hidden=\"true\"]` vs<PERSON><PERSON>jejo podrejene elemente, ki jih je mogoče izbrati"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "<PERSON><PERSON><PERSON> elementov ne vsebuje podrejenih elementov, ki jih je mogoče izbrati: `[aria-hidden=\"true\"]`"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Če vnosno polje nima dostopnega imena, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON><PERSON> je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [Preberite več o oznakah polj za vnos](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Vnosna polja ARIA nimajo dostopnih imen"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Vnosna polja ARIA imajo dostopna imena"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Če element za merjenje nima imena za osebe s poseb<PERSON><PERSON> potrebami, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [<PERSON><PERSON><PERSON>, kako poimenujete elemente `meter`](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Elementi ARIA `meter` ni<PERSON>jo dos<PERSON>h imen."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Elementi ARIA `meter` imajo dostopna imena."}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Če element `progressbar` ni<PERSON> imena, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [<PERSON><PERSON><PERSON>, kako ozna<PERSON>ite elemente `progressbar`](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Elementi ARIA `progressbar` ni<PERSON>jo dos<PERSON>h imen."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Elementi ARIA `progressbar` imajo dostopna imena."}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "Nekatere vloge ARIA imajo zahtevane atribute, ki opišejo stanje elementa bralnikom zaslona. [Preberite več o vlogah in zahtevanih atributih](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` ni<PERSON><PERSON> vs<PERSON> z<PERSON><PERSON> atri<PERSON>ov `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` imajo vse zahtevane atribute `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Nekatere nadrejene vloge ARIA morajo zaradi ustreznega izvajanja funkcij za ljudi s posebnimi potrebami vsebovati določene podrejene vloge. [Preberite več o vlogah in zahtevanih podrejenih elementih](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Elementi z vlogo ARIA `[role]`, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, da podrejeni elementi vsebujejo določeno vlogo `[role]`, ne vsebujejo nekaterih ali vseh teh zahtevanih podrejenih elementov."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Elementi z vlogo ARIA `[role]`, ki <PERSON><PERSON><PERSON><PERSON><PERSON>, da podrejeni elementi vsebujejo določeno vlogo `[role]`, vsebujejo vse zahtevane podrejene elemente."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Nekatere podrejene vloge ARIA morajo biti zaradi ustreznega izvajanja funkcij za ljudi s posebnimi potrebami vsebovane v določenih nadrejenih vlogah. [Preberite več o vlogah ARIA in zahtevanih nadrejenih elementih](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> `[role]` <PERSON><PERSON> vsebovane v zahtevanem nadrejenem elementu"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "<PERSON><PERSON><PERSON> `[role]` so vsebovane v zahtevanem nadrejenem elementu"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Vloge ARIA morajo imeti veljavne v<PERSON>, <PERSON><PERSON><PERSON>, da bodo izvajale želene funkcije za ljudi s posebnimi potrebami. [Preberite več o veljavnih vlogah ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> <PERSON>[role]` niso ve<PERSON><PERSON>"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> `[role]` so ve<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Če preklopno polje nima dostopnega imena, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [Preberite več o preklopnih poljih](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Preklopna polja ARIA nimajo dostopnih imen"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Preklopna polja ARIA imajo dostopna imena"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Če element z opisom orodja nima imena za osebe s poseb<PERSON><PERSON> potrebami, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [<PERSON><PERSON><PERSON>, kako poimenujete elemente `tooltip`](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Elementi ARIA `tooltip` nimajo dostop<PERSON>h imen."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Elementi ARIA `tooltip` imajo dostopna imena."}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Če element `treeitem` nima <PERSON> imena, ga bralniki zaslona predstavijo z generičnim imenom, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanašajo na bralnike zaslona. [Preberite več o označevanju elementov `treeitem`](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Elementi ARIA `treeitem` nimajo dostop<PERSON>h imen."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Elementi ARIA `treeitem` imajo dostopna imena."}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tehnologije, kot so braln<PERSON>, ne morejo tolmačiti atributov ARIA z neveljavnimi vrednostmi. [Preberite več o veljavnih vrednostih za atribute ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Atributi `[aria-*]` ni<PERSON><PERSON> ve<PERSON> vrednosti"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Atributi `[aria-*]` imajo veljav<PERSON> vrednosti"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> tehnologije, kot so braln<PERSON>, ne morejo tolmačiti atributov ARIA z neveljavnimi imeni. [Preberite več o veljavnih atributih ARIA](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Atributi `[aria-*]` so neveljavni ali napačno črkovani"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Atributi `[aria-*]` so veljavni in niso napačno črkovani"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Neuspešni elementi"}, "core/audits/accessibility/button-name.js | description": {"message": "<PERSON>e gumb nima dostopnega imena, ga bralniki zaslona predstavijo kot »gumb«, s <PERSON><PERSON>r je neuporaben za uporabnike, ki se zanaša<PERSON> na bralnike zaslona. [<PERSON><PERSON><PERSON>, kako pos<PERSON>, da bodo gumbi dostopnejši](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON><PERSON> ni<PERSON>jo dos<PERSON>ga imena"}, "core/audits/accessibility/button-name.js | title": {"message": "<PERSON><PERSON><PERSON> imajo dos<PERSON> imena"}, "core/audits/accessibility/bypass.js | description": {"message": "<PERSON>e dodate nač<PERSON>, s katerimi je mogoče zaobiti ponavljajočo se vsebino, uporabnikom s tipkovnico omogočite učinkovitejše pomikanje po strani. [Preberite več o obvodih blokad](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "Stran ne vsebuje naslova, povezave za preskok ali območja z mejnikom"}, "core/audits/accessibility/bypass.js | title": {"message": "Stran vsebuje <PERSON>lov, povezavo za preskok ali območje z mejnikom"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Besedilo z nizkim kontrastom številni uporabniki težko preberejo ali ga sploh ne morejo prebrati. [<PERSON>ber<PERSON>, kako zagotovite zadostni barvni kontrast](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON> in ospredja nimajo zadostnega kontrastnega razmerja."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON>ve oz<PERSON> in ospredja imajo zadostno kontrastno razmerje"}, "core/audits/accessibility/definition-list.js | description": {"message": "Če seznami opredelitev niso ustrezno oz<PERSON>, lahko bralniki zaslona izgovorijo nejasno ali netočno vsebino. [<PERSON><PERSON><PERSON>, kako pravilno strukturirate sezname definicij](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Elementi `<dl>` ne vsebu<PERSON>jo samo ustrezno razvrš<PERSON><PERSON> skupin `<dt>` in `<dd>` ter elementov `<script>`, `<template>` ali `<div>`."}, "core/audits/accessibility/definition-list.js | title": {"message": "Elementi `<dl>` vs<PERSON><PERSON><PERSON>jo samo ustrezno razvrš<PERSON><PERSON> skupine `<dt>` in `<dd>` ter elemente `<script>`, `<template>` ali `<div>`."}, "core/audits/accessibility/dlitem.js | description": {"message": "Elementi seznamov opredelitev (`<dt>` in `<dd>`) morajo biti zaradi zagotavljan<PERSON>, da jih lahko bralniki zaslona ustrezno predstavijo, oviti z nadrejenim elementom `<dl>`. [<PERSON><PERSON><PERSON>, kako pravilno strukturirate sezname definicij](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Elementi seznamov opredelitev niso oviti z elementi `<dl>`"}, "core/audits/accessibility/dlitem.js | title": {"message": "Elementi seznamov opredelitev so oviti z elementi `<dl>`"}, "core/audits/accessibility/document-title.js | description": {"message": "Naslov uporabnikom bralnikov zaslona zagotavlja pregled strani, uporabniki iskalnikov pa se nanašajo nanj pri določanju, ali je stran pomembna za njihovo iskanje. [Preberite več o naslovih dokumentov](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokument nima elementa `<title>`"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokument ima element `<title>`"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "<PERSON><PERSON> elementi, ki jih je mogo<PERSON>, morajo imeti enoličen atribut `id`, s <PERSON><PERSON><PERSON> se <PERSON>ago<PERSON>, da so vidni podpornim tehnologijam. [<PERSON><PERSON><PERSON>, kako odpravite težave s podvojenimi elementi`id`](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)"}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Toliko atributov aktiv<PERSON>h elementov, ki jih je mogoče iz<PERSON>ti, ni enoličnih: `[id]`"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Toliko atributov akt<PERSON><PERSON><PERSON>, ki jih je mogoče i<PERSON>, je enoli<PERSON>h: `[id]`"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Vrednost ID-ja ARIA mora biti <PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, da bi podporne tehnologije spregledale druge primerke. [<PERSON><PERSON><PERSON>, kako odpravite podvojene ID-je ARIA](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "ID-ji <PERSON>"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "ID-ji <PERSON>"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Polja v obrazcih z več oznakami lahko podporne tehnologije, kot so bralniki zaslona, ki uporabl<PERSON>jo prvo, z<PERSON><PERSON><PERSON> ali vse oznake, naja<PERSON><PERSON> tako, da zbegajo uporabnike. [<PERSON><PERSON><PERSON>, kako uporabljate oznake obrazca](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Polja v obrazcih imajo več oznak"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Ni polj v obrazcih z več oznakami"}, "core/audits/accessibility/frame-title.js | description": {"message": "Uporabniki bralnikov zas<PERSON>a se <PERSON>, da jim vs<PERSON><PERSON> okvirjev opišejo naslovi okvirjev. [Preberite več o naslovih okvirjev](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Elementi `<frame>` ali `<iframe>` ni<PERSON><PERSON>a"}, "core/audits/accessibility/frame-title.js | title": {"message": "Elementi `<frame>` ali `<iframe>` imajo naslov"}, "core/audits/accessibility/heading-order.js | description": {"message": "Ustrezno razvrščeni naslovi, ki ne preskakujejo stopenj, posredu<PERSON><PERSON> semantično strukturo strani, da jo je z uporabo podpornih tehnologij laže razumeti in se pomikati po njej. [Preberite več o vrstnem redu naslovov](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Elementi naslovov niso v zaporedno padajočem vrstnem redu"}, "core/audits/accessibility/heading-order.js | title": {"message": "Elementi naslovov se pojavljajo v zaporedno padajočem vrstnem redu"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Če stran ne določi atributa `lang`, bralnik zaslona predvideva, da je stran v privzetem jeziku, ki ga je uporabnik izbral pri nastavljanju bralnika zaslona. Če stran ni v privzetem jeziku, bralnik zaslona morda ne bo pravilno predstavil besedila na strani. [Preberite več o atributu `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "Element `<html>` nima atributa `[lang]`"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "Element `<html>` ima atribut `[lang]`"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Če določite veljaven [jezik BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), pomagate bralnikom zaslona ustrezno predstaviti besedilo [Preberite več o uporabi atributa `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "Element `<html>` nima veljavne vrednosti za atribut `[lang]`."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "Element `<html>` ima veljavno vrednost za svoj atribut `[lang]`"}, "core/audits/accessibility/image-alt.js | description": {"message": "Informativni elementi naj imajo krat<PERSON>, opisno nadomestno besedilo. Okrasne elemente je mogoče prezreti s praznim nadomestnim atributom. [Preberite več o atributu `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Elementi slik nimajo atributov `[alt]`"}, "core/audits/accessibility/image-alt.js | title": {"message": "Elementi slik imajo atribute `[alt]`"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Če se kot gumb `<input>` upora<PERSON><PERSON><PERSON> slika, lah<PERSON> z navajanjem nadomestnega besedila uporabnikom bralnikov zaslona pomagate razumeti namen gumba. [Preberite več o vnosu nadomestnega besedila slike](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Elementi `<input type=\"image\">` nimajo besedila `[alt]`"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Elementi `<input type=\"image\">` imajo besedilo `[alt]`"}, "core/audits/accessibility/label.js | description": {"message": "<PERSON><PERSON><PERSON> zago<PERSON>vl<PERSON>, da pomo<PERSON>ne tehnologije, na primer bralniki zaslona, ustrezno predstavijo kontrolnike za obrazce. [Preberite več o oznakah elementov obrazca](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Elementi obrazcev nimajo povezanih oznak"}, "core/audits/accessibility/label.js | title": {"message": "Elementi obrazcev imajo povezane oznake"}, "core/audits/accessibility/link-name.js | description": {"message": "Besedilo za povezavo (in nadomestno besedilo za slike, kadar so uporabljene kot povezave), ki je prepoznavno in edinstveno ter ga je mogoče izbrati, uporabnikom bralnikov zaslona zagotavlja boljšo izkušnjo pomikanja. [<PERSON><PERSON><PERSON>, kako posk<PERSON>ite, da bodo povezave dostopne](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "Povezave nimajo prepoznavnega imena"}, "core/audits/accessibility/link-name.js | title": {"message": "Povezave imajo prepoznavno ime"}, "core/audits/accessibility/list.js | description": {"message": "Bralniki zaslona predstavljajo sezname na poseben način. Če želite doprinesti h kakovostnejši izgovorjavi bralnikov zaslona, poskrbite za ustrezno strukturo seznamov. [Preberite več o ustrezni strukturi seznamov](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "<PERSON>znami ne vsebu<PERSON>jo samo elementov `<li>` in elementov, ki podpirajo skripte (`<script>` in `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Seznami vsebujejo samo elemente `<li>` in elemente, ki podpirajo skripte (`<script>` in `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Bralniki zaslona zahtevajo, da so elementi seznamov (`<li>`) vsebovani v nadrejenih elementih `<ul>`, `<ol>` ali `<menu>`, da jih lahko ustrezno predstavijo. [Preberite več o ustrezni strukturi seznamov](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Elementi seznamov (`<li>`) niso vsebovani v nadrejenih elementih `<ul>`, `<ol>` ali `<menu>`."}, "core/audits/accessibility/listitem.js | title": {"message": "Elementi seznamov (`<li>`) so vsebovani v nadrejenih elementih `<ul>`, `<ol>` ali `<menu>`."}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Uporabniki ne pričakujejo samodejne osvežitve strani. Če se to zgodi, se izbira pomakne nazaj na vrh strani. To lahko privede do zoprne ali zavajajoče izkušnje. [Preberite več o metaoznaki za osvežitev](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "Dokument uporablja `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "Dokument ne uporablja `<meta http-equiv=\"refresh\">`"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Onemogočanje povečave/pomanj<PERSON>ve je težavno za slabovidne uporabnike, ki se zanašajo na povečavo zaslona, da lahko ustrezno vidijo vsebino spletne strani. [Preberite več o metaoznaki viewport](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "Lastnosti prilagajanja velikosti `[user-scalable=\"no\"]` se uporabljajo v elementu `<meta name=\"viewport\">` ali pa je atribut `[maximum-scale]` manjši od 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "Lastnosti prilagajanja velikosti `[user-scalable=\"no\"]` se ne uporabljajo v elementu `<meta name=\"viewport\">` in atribut `[maximum-scale]` ni manjši od 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "Bralniki zaslona ne morejo prevesti vsebine, ki ni besedilna. Če elementom `<object>` dodate nadomestno besedilo, bralnikom zaslona pomagate prenesti pomen uporabnikom. [Preberite več o nadomestnem besedilu za elemente `object`](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "Elementi `<object>` ni<PERSON>jo nadomestnega besedila"}, "core/audits/accessibility/object-alt.js | title": {"message": "Elementi `<object>` imajo nadom<PERSON> besedilo"}, "core/audits/accessibility/tabindex.js | description": {"message": "Vrednost, večja od 0, kaže na izrecno razporeditev pomikanja. Čeprav je načeloma veljavna, pogosto vodi v zoprne izkušnje za uporabnike, ki se zanašajo na pomožne tehnologije. [Preberite več o atributu `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Nekateri elementi imajo vrednost za `[tabindex]` večjo od 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Noben element nima večje vrednosti za `[tabindex]` od 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Bralniki zaslona imajo funkcije za preprostejše pomikanje po razpredelnicah. Če zagotovite, da se celice `<td>`, ki uporabljajo atribut `[headers]`, nanašajo samo na druge celice v isti razpredelnici, lahko izboljšate izkušnjo za uporabnike bralnikov zaslona. [Preberite več o atributu `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Celice v elementu `<table>`, ki uporabljajo atribut `[headers]`, se nanašajo na element `id`, ki ga ni mogoče najti v isti razpredelnici."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Celice v elementu `<table>`, ki uporabljajo atribut `[headers]`, se nanašajo na celice razpredelnice v isti razpredelnici."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Bralniki zaslona imajo funkcije za preprostejše pomikanje po razpredelnicah. <PERSON><PERSON> zagotovite, da se glave razpredelnic vedno nanašajo na določen nabor celic, lahko izboljšate izkušnjo za uporabnike bralnikov zaslona. [Preberite več o glavah tabel](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Elementi `<th>` in elementi z <PERSON>lavami `[role=\"columnheader\"/\"rowheader\"]` ni<PERSON><PERSON> pod<PERSON>h celic, ki jih opisujejo."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Elementi `<th>` in elementi z <PERSON>lavami `[role=\"columnheader\"/\"rowheader\"]` imajo pod<PERSON> celi<PERSON>, ki jih opisujejo."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Če za elemente določite veljaven [jezik BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question), poma<PERSON> zagotov<PERSON>, da bralnik zaslona pravilno izgovori besedilo. [Preberite več o uporabi atributa `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Atributi `[lang]` ni<PERSON><PERSON> veljav<PERSON> vrednosti"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Atributi `[lang]` imajo veljavno vrednost"}, "core/audits/accessibility/video-caption.js | description": {"message": "Če ima videoposnetek dodan pod<PERSON>, <PERSON><PERSON><PERSON> in slušno prizadeti uporabniki preprosteje dostopajo do informacij, ki jih podaja. [Preberite več o podnapisih v videoposnetkih](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Elementi `<video>` ne vsebu<PERSON> elementa `<track>` z opisom `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Elementi `<video>` vsebujejo element `<track>` s podnapisi `[kind=\"captions\"]`."}, "core/audits/autocomplete.js | columnCurrent": {"message": "Trenutna vrednost"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Predlagani žeton"}, "core/audits/autocomplete.js | description": {"message": "`autocomplete` uporabnikom pomaga hitreje poslati obrazce. Če želite zmanjšati prizadevanje uporabnikov, razmislite o omogočanju, tako da atribut `autocomplete` nastavite na veljavno vrednost. [Preberite več o funkciji `autocomplete` v obrazcih](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)."}, "core/audits/autocomplete.js | failureTitle": {"message": "Toliko elementov nima pravilnih atributov `autocomplete`: `<input>`."}, "core/audits/autocomplete.js | manualReview": {"message": "<PERSON>ah<PERSON>va r<PERSON> pregled"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Preglejte vrstni red žetonov"}, "core/audits/autocomplete.js | title": {"message": "Elementi `<input>` pravilno uporabljajo atribut `autocomplete`."}, "core/audits/autocomplete.js | warningInvalid": {"message": "<PERSON><PERSON><PERSON> za `autocomplete`: »{token}« ni veljavno v {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Preglejte vrst<PERSON> red <PERSON>ov: »{tokens}« v {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Omogoča ukrepanje"}, "core/audits/bf-cache.js | description": {"message": "Številna premikanja se izvedejo z vrnitvijo na prejšnjo stran ali vnovičnim odpiranjem naslednje. Predpomnilnik za hitro obnovitev strani (bfcache) lahko pospeši ta povratna premikanja. [Preberite več o predpomnilniku za hitro obnovitev strani](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)."}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 vzrok napake}one{# vzrok napake}two{# vzroka napake}few{# vzroki napake}other{# vzrokov napake}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/bf-cache.js | failureTitle": {"message": "Stran je preprečila obnovitev predpomnilnika za hitro obnovitev strani"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "<PERSON>rsta napake"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Ne omogoča ukrepanja"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Podpora za brskalnik v čakalni vrsti"}, "core/audits/bf-cache.js | title": {"message": "Stran ni preprečila obnovitve predpomnilnika za hitro obnovitev strani"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Razširitve za Chrome so negativno vplivale na nalaganje te strani. Poskusite pregledati to stran v anonimnem načinu ali v profilu za Chrome brez razširitev."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Ocenjevanje sk<PERSON>"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "Razčlenitev skripta"}, "core/audits/bootup-time.js | columnTotal": {"message": "Skupni čas CPE"}, "core/audits/bootup-time.js | description": {"message": "Razmislite o skrajšanju časa, ki ga porabite za razčlenjevanje, prevajan<PERSON> in izvajanje JavaScripta. <PERSON><PERSON><PERSON><PERSON> boste, da vam lahko pri tem morda pomaga dostavljanje manjših paketov koristne vsebine JavaScript. [<PERSON>ber<PERSON>, kako skrajšate čas izvajanja JavaScripta](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Skrajšajte čas izvajanja JavaScripta"}, "core/audits/bootup-time.js | title": {"message": "Čas izvajanja JavaScripta"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "Odstranite velike, podvojene module JavaScript iz svežnjev in tako zmanjšajte nepotrebne bajte, uporabljene v omrežni dejavnosti. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Odstranitev podvojenih v svežnjih JavaScript"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "Veliki GIF-i so neučinkoviti za dostavljanje animirane vsebine. Razmislite o uporabi videoposnetkov MPEG4/WebM za animacije in slik PNG/WebP za statične slike namesto GIF-ov, s čimer prihranite omrežne bajte. [Preberite več o učinkovitih oblikah videoposnetkov.](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Uporabite oblike zapisa videoposnetkov za animirano vsebino"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Elementi »polyfill« in »transform« starejšim brskalnikom omogočajo uporabo novih funkcij JavaScripta. Pri sodobnih brskalnikih pa številni niso potrebni. Za JavaScript v svežnju uporabite sodobno strategijo uvajanja skriptov z zaznavanjem funkcij modulov/ne<PERSON><PERSON><PERSON>, da zmanjšate količino kode, poslane sodobnim brskalnikom, hkrati pa ohranite podporo za starejše brskalnike. [Preberite več o uporabi sodobnega JavaScripta](https://web.dev/publish-modern-javascript/)."}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Izogibajte se prikazovanju starejšega JavaScripta v sodobnih brskalnikih"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Oblike zapisa slike, kot sta WebP in AVIF, pogosto omogočajo učinkovitejše stiskanje kot PNG ali JPEG, kar pomeni hitrejše prenose in manjšo porabo podatkov. [Preberite več o sodobnih oblikah zapisa slik](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Dostavljajte slike v sodobnih oblikah zapisa"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "Razmislite o odloženem nalaganju slik zunaj zaslona in skritih slik po dokončanem nalaganju kritičnih sredstev zaradi skrajšanja časa do interaktivnosti strani. [<PERSON>ber<PERSON>, kako odložite prikaz slik, ki niso na zaslonu](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Odložite nalaganje slik, ki so zunaj zaslona"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Sredstva blokirajo prvo barvanje strani. Razmislite o sprotnem dostavljanju kritičnega JavaScripta/CSS-ja in odlaganju JavaScripta/slogov, ki ni oziroma niso kritični. [<PERSON>ber<PERSON>, kako odpravite vire, ki blokirajo upodabljanje](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Izločite sredstva, ki blokirajo upodabljanje"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Veliki omrežni paketi koristne vsebine uporabnikom povzročajo dejanske stroške in so tesno povezani z dolgimi časi nalaganja. [Preberite, kako zmanj<PERSON>te velikosti paketov koristne vsebine](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Skupna velikost je bila {totalBytes, number, bytes} KiB"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Izogibajte se velikanskim omrežnim paketom koristne vsebine"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Izogiba se velikanskim omrežnim paketom koristne vsebine"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Z zmanjšanjem datotek CSS-ja lahko zmanjšate velikosti paketov koristne vsebine. [<PERSON>ber<PERSON>, kako pomanjšate CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Zmanjšajte CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Z zmanjšanjem datotek JavaScript lahko zmanjšate velikosti paketov koristne vsebine in skrajšate čas razčlenjevanja skriptov. [Preberite, kako pomanjšate JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Pomanjšajte JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "V slogovnih datotekah zmanjšajte število nedejavnih pravil in odložite nalaganje CSS-ja, ki se ne uporablja za vsebino na vrhu strani, ter tako zmanjšajte nepotrebno porabo bajtov v omrežni dejavnosti. [Preberite, kako zmanjšate količino neuporabljenega CSS-ja](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Zmanjšajte količino neuporabljenega CSS-ja"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Zmanjšajte količino neuporabljenega JavaScripta in odložite nalaganje skriptov, dokler niso potre<PERSON>, ter tako zmanjšajte nepotrebno porabo bajtov v omrežni dejavnosti. [Preberite, kako zmanjšate količino neuporabljenega JavaScripta](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Zmanjšajte količino neuporabljenega JavaScripta"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Dolgotrajno predpomnjenje lahko pospeši vnovične obiske strani. [Preberite več o pravilnikih o učinkovitem predpomnilniku](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{Najdeno je bilo 1 sredstvo}one{Najdeno je bilo # sredstvo}two{Najdeni sta bili # sredstvi}few{Najdena so bila # sredstva}other{Najdenih je bilo # sredstev}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Dostavljajte statična sredstva z učinkovitim pravilnikom o predpomnjenju"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Uporaba pravilnika o učinkovitem predpomnjenju za statična sredstva"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimizirane slike se nalagajo hitreje in terjajo manj prenesenih podatkov v mobilnih omrežjih. [Oglejte si navodila za učinkovito kodiranje slik](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Učinkovito kodirajte slike"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Dejanske mere"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "Prikazane mere"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Slike so bile večje kot njihova prikazana velikost."}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Slike so bile ustrezne za svojo prikazano velikost."}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Prikazujte slike primerne velikosti, s čimer poskrbite za prihranek prenesenih podatkov v mobilnih omrežjih in izboljšate čas nalaganja. [Preberite, kako prilagodite velikost slik](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "Uporabite slike ustrezne velikosti"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "<PERSON><PERSON> dostavi besedilnih sredstev uporabite stiskanje (gzip, deflate ali brotli) zaradi zmanjšanja skupnega števila omrežnih bajtov. [Preberite več o stiskanju besedila](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Omogočite stiskanje besedila"}, "core/audits/content-width.js | description": {"message": "Če se širina vsebine aplikacije ne ujema s širino vidnega polja, aplikacija morda ni optimizirana za zaslone mobilnih naprav. [<PERSON>berite, kako nastavite vsebino za vidno območje](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Velikost vidnega ob<PERSON>č<PERSON>, ki zna<PERSON> {innerWidth} slikovnih pik, se ne ujema z velikostjo okna, ki znaša {outerWidth} slikovnih pik."}, "core/audits/content-width.js | failureTitle": {"message": "Vsebina ni ustrezne velikosti za vidno območje"}, "core/audits/content-width.js | title": {"message": "Vsebina je ustrezne velikosti za vidno območje"}, "core/audits/critical-request-chains.js | description": {"message": "Verige kritičnih zahtev spodaj vam prikazujejo, katera sredstva so naložena z visoko prednostjo. Razmislite o skrajšanju verig, zmanjšanju velikosti sredstev ali odlaganju prenosa nepotrebnih sredstev zaradi izboljšanja nalaganja strani. [Preberite, kako se izognete veriženju nujnih zahtev](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je bila 1 veriga}one{Najdena je bila # veriga}two{Najdeni sta bili # verigi}few{Najdene so bile # verige}other{Najdenih je bilo # verig}}"}, "core/audits/critical-request-chains.js | title": {"message": "Ne uporabljajte veriženja nujnih zahtev"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiva"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Resnost"}, "core/audits/csp-xss.js | description": {"message": "Močan pravilnik o varnosti vsebine (CSP) znatno zmanjša tveganje za napade s skriptnim izvajanjem na več mestih (XSS). [Preberite, kako uporabite pravilnik CSP za preprečevanje XSS-jev](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)."}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Sin<PERSON>ks<PERSON>"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "Stran vsebuje pravilnik CSP, določen z oznako <meta>. Razmislite o premikanju pravilnika CSP v glavo HTTP ali določitvi drugega strogega pravilnika CSP v glavi HTTP."}, "core/audits/csp-xss.js | noCsp": {"message": "V načinu uveljavljanja ni pravilnikov CSP."}, "core/audits/csp-xss.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da je pravilnik CSP učinkovit proti napadom XSS"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Zastaranje/opozorilo"}, "core/audits/deprecations.js | columnLine": {"message": "Vrstica"}, "core/audits/deprecations.js | description": {"message": "Zastareli API-ji bodo sčasoma odstranjeni iz brskalnika. [Preberite več o zastarelih API-jih](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{Najdeno je bilo 1 opozorilo}one{Najdeno je bilo # opozorilo}two{Najdeni sta bili # opozorili}few{Najdena so bila # opozorila}other{Najdenih je bilo # opozoril}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Uporablja zastarele API-je"}, "core/audits/deprecations.js | title": {"message": "Izogiba se zastarelim API-jem"}, "core/audits/dobetterweb/charset.js | description": {"message": "Izjava glede kodiranja znakov je obvezna. Izvedete jo lahko z oznako `<meta>` v prvih 1024 B HTML-ja ali v glavi odziva HTTP-ja glede vrste vsebine. [Preberite več o deklaraciji glede kodiranja znakov](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Izjava glede nabor<PERSON> »charset« manjka ali se v HTML-ju pojavi prepozno"}, "core/audits/dobetterweb/charset.js | title": {"message": "Ustrezno določa nabor z<PERSON>kov »charset«"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Če določite doctype, brskalnik ne more preklopiti v način »quirks«. [Preberite več o izjavi glede določitve DOCTYPE](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Ime doctype mora biti niz `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Doku<PERSON> vseb<PERSON>je `doctype`, ki spro<PERSON>i `limited-quirks-mode`."}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Do<PERSON>ment mora vsebovati doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je bilo, da je publicid prazen niz"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je bilo, da je systemid prazen niz"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Doku<PERSON> vseb<PERSON>je `doctype`, ki spro<PERSON>i `quirks-mode`."}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "Stran nima HTML-doctype, zato se sproži na<PERSON> »quirks«"}, "core/audits/dobetterweb/doctype.js | title": {"message": "Stran ima HTML-doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "Statistični podatek"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "Vrednost"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "Velik DOM povzroči povečano uporabo pomnilnika, da<PERSON><PERSON><PERSON><PERSON> [slogovne izračune](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) in drage [prilagoditve postavitve](https://developers.google.com/speed/articles/reflow). [Preberite, kako se izognete prekomerni velikosti DOM-a](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}one{# element}two{# elementa}few{# elementi}other{# elementov}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Izogibajte se prekomerni velikosti DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Največja globina DOM-a"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Skupno število elementov DOM"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Največje število podrejenih elementov"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Izogiba se prekomerni velikosti DOM-a"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "Uporabniki so nezaupljivi do spletnih mest oziroma jih begajo spletna mesta, ki zahtevajo njihovo lokacijo brez konteksta. Razmislite o tem, da bi zahtevo povezali z uporabniškim dejanjem. [Preberite več o dovoljenju za geolokacijo](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Zahteva dovoljenje za geolokacijo pri nalaganju strani"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Ne zahteva dovoljenja za geolokacijo pri nalaganju strani"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "<PERSON>rsta <PERSON>"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Težave, zabeležene v podoknu `Issues` v orodjih za razvijalce v Chromu, označujejo nerazrešene težave. Te so lahko posledica neuspešnih omrežnih zahtev, nezadostnih varnostnih nadzorov in drugih težav z brskalnikom. Če si želite ogledati več podrobnosti o posamezni težavi, v orodjih za razvijalce v Chromu odprite podokno s težavami (Issues)."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Težave so bile zapisane v podokno `Issues` v orodju za razvijalce v Chromu"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blokiral pravilnik iz več izvorov"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Oglasi uporabljajo veliko sredstev"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Ni težav v podoknu `Issues` v orodjih za razvijalce v Chromu"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Različica"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Vse knjižnice JavaScript vmesnikov, zaznane na strani. [Preberite več o tej reviziji diagnostike zaznavanja knjižnice JavaScript](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "Zaznane knjižnice JavaScript"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "Pri uporabnikih s počasnimi povezavami lahko zunanji sk<PERSON>, dinamično vstavljeni prek `document.write()`, zakasnijo nalaganje strani za več deset sekund. [Preberite več o tem, kako se izognete »document.write()«](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Izogibanje temu: `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "Se izogiba `document.write()`"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "Uporabniki so nezaupljivi do spletnih mest oziroma jih begajo spletna mesta, ki zahtevajo pošiljanje obvestil brez konteksta. Razmislite o tem, da bi zahtevo povezali z uporabniškimi potezami. [Preberite več o odgovornem pridobivanju dovoljenja za obvestila](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Zahteva dovoljenje za obvestila pri nalaganju strani"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Ne zahteva dovoljenja za obvestila pri nalaganju strani"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokol"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ima več prednosti pred HTTP/1.1, vkl<PERSON>čno z binarnimi glavami in multipleksiranjem. [Preberite več o protokolu HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 zahteva ni bila poslana prek HTTP/2}one{# zahteva ni bila poslana prek HTTP/2}two{# zahtevi nista bili poslani prek HTTP/2}few{# zahteve niso bile poslane prek HTTP/2}other{# zahtev ni bilo poslanih prek HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Uporabi HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Posluševalnike dogodkov dotika in kolesca lahko označite kot `passive` zaradi izboljšanja delovanja pomikanja na strani. [Preberite več o uporabi pasivnih poslušalcev dogodkov](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Ne uporablja pasivnih poslušalcev za izboljšanje delovanja pomikanja"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Uporablja pasivne poslušalce za izboljšanje delovanja pomikanja"}, "core/audits/errors-in-console.js | description": {"message": "Napake, zabeležene v konzoli, označujejo nerazrešene težave. Te so lahko posledica neuspešnih omrežnih zahtev in drugih težav z brskalnikom. [Preberite več o teh napakah v reviziji diagnostike konzole](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)."}, "core/audits/errors-in-console.js | failureTitle": {"message": "Napake brskalnika so bile zabeležene v konzoli"}, "core/audits/errors-in-console.js | title": {"message": "V konzoli ni bila zabeležena nobena napaka brskalnika"}, "core/audits/font-display.js | description": {"message": "Izkoristite funkcijo CSS-ja `font-display`, s <PERSON><PERSON><PERSON>, da je med nalaganjem spletne pisave besedilo vidno uporabnikom. [Preberite več o funkciji `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>, da bo med nalaganjem spletne pisave besedilo ostalo vidno"}, "core/audits/font-display.js | title": {"message": "<PERSON><PERSON> besedilo ostaja vidno med nalaganjem spletne pisave"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountForOrigin,plural, =1{Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti `font-display` za izvorni URL {fontOrigin}.}one{Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti `font-display` za izvorni URL {fontOrigin}.}two{Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti `font-display` za izvorni URL {fontOrigin}.}few{Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti `font-display` za izvorni URL {fontOrigin}.}other{Orodje Lighthouse ni utegnilo samodejno preveriti vrednosti `font-display` za izvorni URL {fontOrigin}.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Razmerje stranic (dejansko)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "<PERSON><PERSON><PERSON><PERSON> stra<PERSON> (prikazano)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Mere prikaza slike se morajo ujemati z naravnim razmerjem stranic. [Preberite več o razmerju stranic slik](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Prikazuje slike z nepravilnim razmerjem stranic"}, "core/audits/image-aspect-ratio.js | title": {"message": "Prikazuje slike s pravilnim razmerjem stranic"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Dejanska velikost"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Prikazana velikost"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Pričakovana velikost"}, "core/audits/image-size-responsive.js | description": {"message": "Naravne mere slike morajo biti sorazmerne velikosti zaslona in razmerju slikovnih pik zaradi zagotavljanja največje mogoče jasnosti slike. [Preberite, kako zagotovite odzivne slike](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Prikaže slike z nizko ločljivostjo"}, "core/audits/image-size-responsive.js | title": {"message": "Prikaže slike z ustrezno ločljivostjo"}, "core/audits/installable-manifest.js | already-installed": {"message": "Aplikacija je že nameš<PERSON>ena"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Zahtevane ikone iz manifesta ni bilo mogoče prenesti."}, "core/audits/installable-manifest.js | columnValue": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/installable-manifest.js | description": {"message": "Proces storitve je tehnologija, ki aplikaciji omogoča uporabo številnih funkcij moderne spletne aplikacije, na primer delovanje brez povezave, dodajanje na začetni zaslon in potisna obvestila. Z uporabo ustreznega procesa storitve in manifesta lahko brskalniki proaktivno pozovejo uporabnike, da dodajo vašo aplikacijo na začetni zaslon, kar lahko privede do več dejavnosti. [Preberite več o zahtevah za izvedljivost namestitve manifesta](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 razlog}one{# razlog}two{# razloga}few{# razlogi}other{# razlogov}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Manifest spletne aplikacije in proces storitve ne izpolnjujeta zahtev za izvedljivost namestitve"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "URL aplikacije Trgovina Play in ID Trgovine Play se ne ujemata"}, "core/audits/installable-manifest.js | in-incognito": {"message": "Stran je naložena v anonimnem oknu"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Lastnost manifesta »display« mora biti »standalone«, »fullscreen« ali »minimal-ui«"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifest vsebuje polje »display_override« in prvi podprti način prikaza mora biti ena od možnosti »standalone«, »fullscreen« ali »minimal-ui«"}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifesta ni bilo mogoče pridobiti ali razčleniti oziroma je morda prazen"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "URL manifesta je bil spremenjen med pridobivanjem manifesta."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifest ne vsebuje polja »name« ali »short_name«"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifest ne vsebuje ustrezne ikone – zahtevana je oblika PNG, SVG ali WebP z najmanj toliko slikovnimi pikami: {value0}. Nastaviti je treba tudi atribut velikosti (»sizes«), če je nastavljen atribut namena (»purpose«), pa mora vkl<PERSON>ti »any«."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Nobena priložena ikona ni v obliki PNG, SVG ali WebP in ni kvadrat najmanj toliko slikovnih pik: {value0}, pri čemer atribut namena ni nastavljen oziroma je nastavljen na »poljubno«."}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Prenesena ikona je bila prazna ali p<PERSON>škodovana."}, "core/audits/installable-manifest.js | no-id-specified": {"message": "ID Trgovine Play ni naveden"}, "core/audits/installable-manifest.js | no-manifest": {"message": "Stran nima URL-ja manifesta <link>"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Noben ujemajoči proces storitve ni zaznan. Morda boste morali znova naložiti stran oziroma preveriti, ali obseg procesa storitve za trenutno stran zajema obseg in začetni URL manifesta."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Procesa storitve ni mogoče preveriti brez polja »start_url« v manifestu"}, "core/audits/installable-manifest.js | noErrorId": {"message": "ID napake o izvedljivosti namestitve »{errorId}« ni prepoznan"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Stran ni bila poslana iz varnega izvora"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "Stran ni naložena v glavnem okviru"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "Stran ne deluje brez povezave"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "Aplikacija MSA je odmeščena in preverjanja namestljivosti se ponastavljajo."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Navedeno okolje aplikacije ni podprto v sistemu Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifest določa »prefer_related_applications: true«"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "Vrednost »prefer_related_applications« je podprta samo v Chromu Beta in stabilnih različicah Chroma v sistemu Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse ni uspel zaznati procesa storitve. Poskusite z novejšo različico Chroma."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Shema URL-ja v manifestu ({scheme}) ni podprta v sistemu Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Začetni URL manifesta ni veljaven"}, "core/audits/installable-manifest.js | title": {"message": "Manifest spletne aplikacije in proces storitve izpolnjujeta zahteve za izvedljivost namestitve"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "URL v manifestu vsebuje uporabniško ime, geslo ali vrata"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Stran ne deluje brez povezave. Stran ne bo obravnavana kot namestljiva po avgustu 2021, ko bo izdana stabilna različica Chroma 93."}, "core/audits/is-on-https.js | allowed": {"message": "Dovoljeno"}, "core/audits/is-on-https.js | blocked": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "URL zahtev, ki niso varne"}, "core/audits/is-on-https.js | columnResolution": {"message": "Razrešitev zahtev"}, "core/audits/is-on-https.js | description": {"message": "Vsa spletna mesta morajo biti zaščitena s HTTPS, tudi tista, ki nimajo opravka z občutljivimi podatki. To vključuje izogibanje [meša<PERSON> vsebini](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), pri <PERSON><PERSON>er so nekatera sredstva naložena prek protokola HTTP, čeprav je začetna zahteva prikazana prek protokola HTTPS. HTTPS vsiljivcem preprečuje manipuliranje s komunikacijami ali pasivno poslušanje komunikacij med aplikacijo in uporabniki ter je pogoj za HTTP/2 in veliko novih API-jev za spletna okolja. [Preberite več o protokolu HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je bila 1 zahteva, ki ni varna}one{Najdena je bila # zahteva, ki ni varna}two{Najdeni sta bili # zahtevi, ki nista varni}few{Najdene so bile # zahteve, ki niso varne}other{Najdenih je bilo # zahtev, ki niso varne}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Ne uporablja HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Uporablja HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Samodejno nadgrajeno na protokol HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Dovoljeno z opozorilom"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "To je največji vsebinski element, i<PERSON><PERSON><PERSON> z<PERSON> vidnega območja. [Preberite več o elementu največjega vsebinskega izrisa](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element največjega vsebinskega izrisa"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Prispevek k CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Ti elementi DOM največ prispevajo k CLS-ju strani. [Preberite, kako izboljšate CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Izogibajte se velikim pomikom postavitve"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Slike na vrhu strani, ki so o<PERSON><PERSON><PERSON><PERSON> naložene, se upodabljajo pozneje v življenjskem ciklu strani, zato lahko pride do zakasnitve največjega vsebinskega izrisa. [Preberite več o optimalnem odloženem nalaganju](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Slika največjega vsebinskega izrisa je bila odloženo naložena"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Slika največjega vsebinskega izrisa ni bila odloženo naložena"}, "core/audits/long-tasks.js | description": {"message": "Navede najdaljša opravila v glavni niti, kar je koristno za prepoznavanje elementov, ki največ prispevajo k zakasnitvi vnosa. [<PERSON>ber<PERSON>, kako se izognete dolgim opravilom v glavni niti](https://web.dev/long-tasks-devtools/)."}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{Najdeno je bilo # dolgo opravilo}one{Najdeno je bilo # dolgo opravilo}two{Najdeni sta bili # dolgi opravili}few{Najdena so bila # dolga opravila}other{Najdenih je bilo # dolgih opravil}}"}, "core/audits/long-tasks.js | title": {"message": "Izogibajte se dolgim opravilom v glavni niti"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "Kategorija"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Razmislite o skrajšanju časa, ki ga porabite za razčlenjevanje, z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in izvajanje JavaScripta. <PERSON><PERSON><PERSON><PERSON> boste, da vam lahko pri tem morda pomaga dostavljanje manjših paketov koristne vsebine JavaScript. [<PERSON><PERSON><PERSON>, kako minimizirate delo glavne niti](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minimizirajte delo glavne niti"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minimizira delo glavne niti"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Če želite doseči največ <PERSON>, morajo spletna mesta delovati v vsakem pomembnejšem brskalniku. [Preberite več o združljivosti v več brskalnikih](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Spletno mesto deluje v različnih brskalnikih"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Zagotovite povezave v globino do posameznih strani prek URL-jev in poskrbite, da so zaradi objavljanja v družbenih omrežjih ti URL-ji enolični. [Preberite več o zagotavljanju povezav v globino](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Vsaka stran ima URL"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Prehodi morajo ob dotikanju delovati hitro in tekoče tudi v počasnem omrežju. To je ključno za uporabnikov vtis zmogljivega delovanja. [Preberite več o prehodih strani](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Prehodi med stranmi ne dajejo občutka, kot da so blokirani v omrežju"}, "core/audits/maskable-icon.js | description": {"message": "<PERSON><PERSON><PERSON>, ki o<PERSON><PERSON>ča maskiranje, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da pri nameščanju aplikacije v napravi slika zapolni celotno obliko brez črne obrobe zgoraj in spodaj. [Preberite več o ikonah manifestov, ki jih je mogoče maskirati](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifest ni<PERSON>, ki omogoča maskiranje"}, "core/audits/maskable-icon.js | title": {"message": "Manifest ima ikono, ki omogoča maskiranje"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulativna sprememba postavitve meri premikanje vidnih elementov znotraj vidnega območja. [Preberite več o meritvi »Kumulativna sprememba postavitve«](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interakcija do naslednjega izrisa meri odzivnost strani, kako dolgo traja, da se stran vidno odzove na vnos uporabnika. [Preberite več o meritvi interakcije do naslednjega izrisa](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "Prvi vsebinski izris oz<PERSON>, ko je izrisano prvo besedilo oziroma je izrisana prva slika. [Preberite več o meritvi prvega vsebinskega izrisa](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON><PERSON> smiselno <PERSON>, kdaj je vidna glavna vs<PERSON>ina strani. [Preberite več o meritvi prvega smiselnega izrisa](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Čas do interaktivnosti je čas, pot<PERSON><PERSON>, da stran postane povsem interaktivna. [Preberite več o meritvi »Čas do interaktivnosti«](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Največji vsebinski izris ozna<PERSON>, ko je izrisano največje besedilo oziroma je izrisana največja slika. [Preberite več o meritvi »Največji vsebinski izris«](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)."}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Največja potencialna zakasnitev od prvega vnosa, na katero lahko uporabniki naletijo, je trajanje najdaljšega opravila. [Preberite več o meritvi »Največja potencialna zakasnitev od prvega vnosa«](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Indeks hit<PERSON> p<PERSON>, kako hitro je vsebina strani vidno izpolnjena. [Preberite več o meritvi indeksa hitrosti](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Skupek vseh časovnih obdobij med FCP-jem in časom do interaktivnosti, ko je dolžina opravila presegla 50 ms, izražen v milisekundah. [Preberite več o meritvi skupnega časa blokiranja](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Omrežni časi povratnega potovanja (RTT) imajo velik vpliv na učinkovitost delovanja. Če je RTT do izvora velik, to kaže na to, da bi lahko strežniki bližje uporabniku izboljšali učinkovitost delovanja. [Preberite več o času povratnega potovanja](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Časi omrežnega povratnega potovanja"}, "core/audits/network-server-latency.js | description": {"message": "Na spletno učinkovitost delovanja lahko vplivajo zakasnitve strežnikov. Če je zakasnitev strežnika za izvor velika, to kaže na to, da je strežnik preobremenjen ali ima slabo zaledno učinkovitost delovanja. [Preberite več o odzivnem času strežnika](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Zakasnitve strežnikovega zaledja"}, "core/audits/no-unload-listeners.js | description": {"message": "Dogodek `unload` se ne aktivira zanesljivo in poslušanje, kdaj se aktivira, lahko prepreči optimizacije brskalnika, kot je predpomnjenje pomikanja naprej in nazaj. Uporabite dogodke `pagehide` ali `visibilitychange`. [Preberite več o odstranjevanju poslušalcev dogodkov](https://web.dev/bfcache/#never-use-the-unload-event)."}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registrira poslušalca dogodka `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Izogiba se poslušalcem dogodka `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON>, ki niso se<PERSON>, so lahko nestabilne in lahko povečajo CLS. [<PERSON><PERSON><PERSON>, kako se lahko izognete nesestavljenim animacijam](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{Najden je bil # animiran element}one{Najden je bil # animiran element}two{Najdena sta bila # animirana elementa}few{Najdeni so bili # animirani elementi}other{Najdenih je bilo # animiranih elementov}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Lastnost, povezana s »filter«, lahko premika slikovne pike"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "<PERSON><PERSON>j ima drugo animacijo, ki ni združljiva"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Učinek ima sestavljeni način, ki ni »replace«"}, "core/audits/non-composited-animations.js | title": {"message": "Izogibanje nesestavljenim animacijam"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "Lastnost, pove<PERSON>a s »transform«, je odvisna od velikosti polja"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{Nepodprta lastnost CSS: {properties}}one{Nepodprte lastnosti CSS: {properties}}two{Nepodprte lastnosti CSS: {properties}}few{Nepodprte lastnosti CSS: {properties}}other{Nepodprte lastnosti CSS: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Učinek ima nepodprte časovne parametre"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> in velikosti omrežnih zahtev imejte pod cilji, ki so nastavljeni z navedenim proračunom za uspešnost. [Preberite več o proračunih za uspešnost](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 zahteva}one{# zahte<PERSON>}two{# zahte<PERSON>}few{# zahte<PERSON>}other{# zahtev}}"}, "core/audits/performance-budget.js | title": {"message": "Proračun za uspešnost"}, "core/audits/preload-fonts.js | description": {"message": "Vnaprej naložite pisave (`optional`), da jih bodo novi obiskoval<PERSON> la<PERSON>ko uporabili. [Preberite več o vnaprejšnjem nalaganju pisav](https://web.dev/preload-optional-fonts/)."}, "core/audits/preload-fonts.js | failureTitle": {"message": "Pisave z vrednostjo `font-display: optional` niso vnaprej naložene"}, "core/audits/preload-fonts.js | title": {"message": "Pisave z vrednostjo `font-display: optional` so vnap<PERSON>j naložene"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Če je element LCP dinamično dodan na stran, morate za izboljšanje časa za LCP sliko vnaprej naložiti. [Preberite več o vnaprejšnjem nalaganju elementov LCP](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "Vnaprejšnje nalaganje slike največjega vsebinskega izrisa"}, "core/audits/redirects.js | description": {"message": "Preusmeritve vnašajo dodatne zakasnitve nalaganja strani. [Preberite več o preprečevanju preusmeritev strani](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Izogibajte se preusmeritvam na več strani"}, "core/audits/resource-summary.js | description": {"message": "Če želite nastaviti proračune za količino in velikost sredstev strani, dodajte datoteko budget.json. [Preberite več o proračunih za uspešnost](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 zahteva • {byteCount, number, bytes} Ki<PERSON>}one{# zahteva • {byteCount, number, bytes} Ki<PERSON>}two{# zahtevi • {byteCount, number, bytes} Ki<PERSON>}few{# zahteve • {byteCount, number, bytes} KiB}other{# zahtev • {byteCount, number, bytes} KiB}}"}, "core/audits/resource-summary.js | title": {"message": "Število zahtev naj bo majhno in prenosi naj ne bodo preveliki"}, "core/audits/seo/canonical.js | description": {"message": "Kanonične povezave predlagajo, kateri URL naj bo prikazan v rezultatih iskanja. [Preberite več o kanoničnih povezavah](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Več URL-jev v sporu ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Neveljaven URL ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "Kaže na drugo lokacijo `hreflang` ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Ni absolutni URL ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Kaže na korenski URL domene (domačo stran), namesto na enakovredno stran z vsebino"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokument nima veljavne povezave `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokument ima veljaven atribut `rel=canonical`"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "Povezava, ki ne omogoča iskanja po vsebini"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Iskalniki morda uporabljajo atribute `href` v povezavah za iskanje po vsebini spletnih mest. Atribut `href` elementov za sidranje mora biti povezan z ustreznim ciljem, da je mogoče odkriti več strani spletnega mesta. [<PERSON><PERSON><PERSON>, kako omogočite iskanje po vsebini povezav](https://support.google.com/webmasters/answer/9112205)."}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Povezave ne omogočajo iskanja po vsebini"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Povezave omogočajo iskanje po vsebini"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Dodatno neberljivo besedilo"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Velikost pisave"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "% besedila na strani"}, "core/audits/seo/font-size.js | columnSelector": {"message": "Izbirnik"}, "core/audits/seo/font-size.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON> pisave, man<PERSON><PERSON><PERSON> od 12 slikovnih pik, so <PERSON><PERSON><PERSON><PERSON><PERSON>, da bi bile berljive, zato morajo obiskovalci z mobilnimi napravami s potezo razširjanja prstov na zaslonu povečati strani, da bi jih lahko prebrali. Poskušajte zagotoviti, da bo več kot 60 % besedila na strani velikosti, ki ni manjša od 12 slikovnih pik. [Preberite več o berljivih velikostih pisav](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} berljivega besedila"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Besedilo je neberljivo, ker ni metaoznake »viewport«, optimizirane za zaslone mobilnih naprav."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokument ne uporablja berljivih velikosti pisav"}, "core/audits/seo/font-size.js | legibleText": {"message": "<PERSON><PERSON><PERSON><PERSON> besedilo"}, "core/audits/seo/font-size.js | title": {"message": "Dokument uporablja berljive velikosti pisav"}, "core/audits/seo/hreflang.js | description": {"message": "Povezave hreflang iskalnikom povedo, katero različico strani naj prikažejo v rezultatih iskanja za določen jezik ali regijo. [Preberite več o povezavah `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokument nima veljavnega atributa `hreflang`"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativna vrednost href"}, "core/audits/seo/hreflang.js | title": {"message": "Dokument ima veljaven atribut `hreflang`"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Nepričakovana koda jezika"}, "core/audits/seo/http-status-code.js | description": {"message": "Strani z neuspešnimi kodami stanja HTTP morda ne bodo pravilno indeksirane. [Preberite več o kodah stanja HTTP](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "Stran ima neuspešno kodo stanja HTTP"}, "core/audits/seo/http-status-code.js | title": {"message": "Stran ima uspešno kodo stanja HTTP"}, "core/audits/seo/is-crawlable.js | description": {"message": "Če iskalniki nimajo dovoljenja za iskanje po vsebini vaših strani, jih ne morejo vključiti v rezultate iskanja. [Preberite več o direktivah za iskalnike po spletni vsebini](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Indeksiranje strani je blokirano"}, "core/audits/seo/is-crawlable.js | title": {"message": "Indeksiranje strani ni blokirano"}, "core/audits/seo/link-text.js | description": {"message": "Opisno besedilo povezave iskalnikom pomaga razumeti vašo vseb<PERSON>. [<PERSON><PERSON><PERSON>, kako posk<PERSON>ite, da bodo povezave dostopnejše](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{Najdena je 1 povezava}one{Najdena je # povezava}two{Najdeni sta # povezavi}few{Najdene so # povezave}other{Najdenih je # povezav}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "Povezave nimajo opisnega besedila"}, "core/audits/seo/link-text.js | title": {"message": "Povezave imajo opisno besedilo"}, "core/audits/seo/manual/structured-data.js | description": {"message": "Za preverjanje strukturiranih podatkov zaženite [orodje za preizkušanje strukturiranih podatkov](https://search.google.com/structured-data/testing-tool/) in [orodje Linter za strukturirane podatke](http://linter.structured-data.org/). [Preberite več o strukturiranih podatkih](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturirani podatki so veljavni"}, "core/audits/seo/meta-description.js | description": {"message": "Za podroben povzetek vsebine strani so lahko v rezultatih iskanja vključeni metaopisi. [Preberite več o metaopisu](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "<PERSON><PERSON><PERSON> besedilo je prazno."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokument nima metaopisa"}, "core/audits/seo/meta-description.js | title": {"message": "Dokument ima metaopis"}, "core/audits/seo/plugins.js | description": {"message": "Iskalniki ne morejo indeksirati vseb<PERSON> v<PERSON>čnikov in številne naprave vtičnike omejujejo ali jih ne podpirajo. [Preberite več o izogibanju vtičnikom](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokument uporablja vtičnike"}, "core/audits/seo/plugins.js | title": {"message": "Dokument ne vsebuje vtičnikov"}, "core/audits/seo/robots-txt.js | description": {"message": "Če datoteka robots.txt ni pra<PERSON> o<PERSON>, iskal<PERSON>i po spletni vsebini morda ne bodo razume<PERSON>, ka<PERSON>, da se išče po spletni vsebini vašega spletnega mesta in se jo indeksira. [Preberite več o datoteki robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Zahteva za datoteko robots.txt je vrnila to stanje HTTP: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Najdena je bila 1 napaka}one{Najdena je bila # napaka}two{Najdeni sta bili # napaki}few{Najdene so bile # napake}other{Najdenih je bilo # napak}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse ni mogel prenesti datoteke robots.txt"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "Datoteka robots.txt ni veljavna"}, "core/audits/seo/robots-txt.js | title": {"message": "Datoteka robots.txt je veljavna"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktivni elementi, kot so gumbi in povezave, morajo biti dovolj veliki (48 x 48 slikovnih pik) in okoli njih mora biti dovolj prostora, da se jih je mogoče dotakniti, ne da bi se pri tem dotaknili drugih elementov. [Preberite več o ciljih dotikov](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} ciljev dotika primerne velikosti"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Cilji za dotik so prema<PERSON><PERSON>, ker ni metaoznake »viewport«, optimizirane za zaslone mobilnih naprav."}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Cilji za dotik niso primerne velikosti"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Prekrivajoči se cilj"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON><PERSON>j za dotik"}, "core/audits/seo/tap-targets.js | title": {"message": "Cilji za dotikanje so ustrezne velikosti"}, "core/audits/server-response-time.js | description": {"message": "Odzivni čas strežnika za glavni dokument naj bo kratek, saj so od tega odvisne vse druge zahteve. [Preberite več o meritvi »Čas do prvega bajta«](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Korenski dokument je terjal {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Skrajševanje začetnega odzivnega časa strežnika"}, "core/audits/server-response-time.js | title": {"message": "Začetni odzivni čas strežnika je bil kratek"}, "core/audits/service-worker.js | description": {"message": "Proces storitve je tehnologija, ki aplikaciji omogoča uporabo številnih funkcij moderne spletne aplikacije, na primer delovanje brez povezave, dodajanje na začetni zaslon in potisna obvestila. [Preberite več o procesih storitve](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "To stran nad<PERSON>ra proces storitve, vendar ni bilo mogoče najti ničesar od tega: `start_url`, ker ni bilo mogoče razčleniti manifesta kot veljavne datoteke JSON"}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "To stran nadzira proces storitve, vendar `start_url` ({startUrl}) ni v obsegu procesa storitve ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "To stran nad<PERSON>ra proces storitve, vendar ni bilo mogoče najti nič<PERSON>ar od tega: `start_url`, ker niso bili preneseni manifesti."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Ta izvor ima enega ali več procesov storitve, vendar stran ({pageUrl}) ni v obsegu."}, "core/audits/service-worker.js | failureTitle": {"message": "Ne registrira procesa storitve, ki nadzira stran in to: `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registrira proces storitve, ki nadzira stran in to: `start_url`"}, "core/audits/splash-screen.js | description": {"message": "S tematskim pozdravnim zaslonom zagotovite visokokakovostno izkušnjo, ko uporabniki zaženejo aplikacijo z začetnih zaslonov. [Preberite več o pozdravnih zaslonih](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Ni konfigurirano za pozdravni z<PERSON>lon po meri"}, "core/audits/splash-screen.js | title": {"message": "Konfigurirano za pozdravni z<PERSON>lon po meri"}, "core/audits/themed-omnibox.js | description": {"message": "Naslovno vrstico brskalnika je mogoče prilagoditi s temo, ki se ujema s spletnim mestom. [Preberite več o nastavljanju teme za naslovno vrstico](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Ne nastavi barve teme za naslovno vrstico."}, "core/audits/themed-omnibox.js | title": {"message": "Nastavi barvo teme za naslovno vrstico."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (podpora strankam)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (trženje)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (družabno)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/audits/third-party-facades.js | description": {"message": "Nekatere vdelave drugih ponudnikov je mogoče odloženo naložiti. Razmislite o tem, da bi jih nadomestili s preprosto komponento, dokler jih ne boste potrebovali. [<PERSON><PERSON><PERSON>, kako odložite druge ponudnike s preprosto komponento](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{Na voljo je # nadomestna preprosta komponenta}one{Na voljo je # nadomestna preprosta komponenta}two{Na voljo sta # nadomestni preprosti komponenti}few{Na voljo so # nadomestne preproste komponente}other{Na voljo je # nadomestnih preprostih komponent}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "Nekatera sredstva drugih ponudnikov je mogoče odloženo naložiti s preprosto komponento"}, "core/audits/third-party-facades.js | title": {"message": "Odloženo nalaganje sredstev drugih ponudnikov s preprostimi komponentami"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Drugi ponudniki"}, "core/audits/third-party-summary.js | description": {"message": "Koda drugega ponudnika lahko znatno vpliva na učinkovitost nalaganja. Omejite število odvečnih drugih ponudnikov in poskusite naložiti kodo drugega ponudnika, ko je stran prvenstveno končala nalaganje. [Preberite, kako zmanj<PERSON>te vpliv drugih ponudnikov](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Koda drugega ponudnika je blokirala glavno nit {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Zmanjšanje vpliva kode drugega ponudnika"}, "core/audits/third-party-summary.js | title": {"message": "Zmanjšajte uporabo komponent drug<PERSON> pon<PERSON>nikov"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "Izmerjena vrednost"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "<PERSON><PERSON>"}, "core/audits/timing-budget.js | description": {"message": "Nastavite časovni pro<PERSON>čun, da boste laže spremljali uspešnost spletnega mesta. Uspešna spletna mesta se hitro naložijo in naglo odzovejo na dogodke uporabniških vnosov. [Preberite več o proračunih za uspešnost](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Časovni proračun"}, "core/audits/unsized-images.js | description": {"message": "Če želite zmanjšati pomike postavitve in izboljšati CLS, za elemente slik nastavite eksplicitno širino in višino. [<PERSON><PERSON><PERSON>, kako nastavite mere slik](https://web.dev/optimize-cls/#images-without-dimensions)."}, "core/audits/unsized-images.js | failureTitle": {"message": "Elementi slik nimajo izrecnih atributov `width` in `height`"}, "core/audits/unsized-images.js | title": {"message": "Elementi slik imajo eksplicitna atributa `width` in `height`"}, "core/audits/user-timings.js | columnType": {"message": "Vrsta"}, "core/audits/user-timings.js | description": {"message": "Razmislite o uporabi API-ja za merjenje dejanskih izvedb uporabniš<PERSON> dogodkov (User Timing API), če želite izmeriti dejansko delovanje aplikacije med ključnimi uporabniškimi izkušnjami. [Preberite več o oznakah trajanja izvedbe uporabniških dogodkov](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 trajanje izvedbe uporabniških dogodkov}one{# trajanje izvedbe uporabniških dogodkov}two{# trajanji izvedbe uporabniških dogodkov}few{# trajanja izvedbe uporabniških dogodkov}other{# trajanj izvedbe uporabniških dogodkov}}"}, "core/audits/user-timings.js | title": {"message": "Oznake in merjenja trajanj izvedbe uporabniških dogodkov"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "Povezava `<link rel=preconnect>` je bila najdena za »{securityOrigin}«, vendar je brskalnik ni uporabil. Preverite, ali pravilno uporabljate atribut `crossorigin`."}, "core/audits/uses-rel-preconnect.js | description": {"message": "Razmislite o dodajanju namigov za sredstva (`preconnect` ali `dns-prefetch`) zaradi vzpostavljanja zgodnjih povezav s pomembnimi izvori drugih ponudnikov. [Preberite, kako se vnaprej povežete z zahtevanimi izvori](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Vnaprej se povežite z zahtevanimi izvori"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "<PERSON><PERSON><PERSON> smo več kot dve povezavi `<link rel=preconnect>`. Te je treba uporabljati gospodarno in samo za najpomembnejše izvore."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "Povezava `<link rel=preconnect>` je bila najdena za »{securityOrigin}«, vendar je brskalnik ni uporabil. Atribute `preconnect` uporabljajte samo za pomembne izvore, ki jih bo stran gotovo zahtevala."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "Povezava za vnaprejšnje nalaganje `<link>` je bila najdena za »{preloadURL}«, vendar je brskalnik ni uporabil. Preverite, ali pravilno uporabljate atribut `crossorigin`."}, "core/audits/uses-rel-preload.js | description": {"message": "Razmislite o uporabi oznake `<link rel=preload>` za dodeljevanje višje stopnje prednosti pri pridobivanju sredstev, ki so trenutno zahtevana pri nadaljnjem nalaganju strani. [Preberite, kako vnaprej naložite ključne zahteve](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "Vnaprej nalagajte ključne zahteve"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "URL zemljevida"}, "core/audits/valid-source-maps.js | description": {"message": "Zemljevidi izvorne kode prevedejo pomanjšano kodo v izvirno izvorno kodo. To je razvijalcem v pomoč pri odpravljanju napak v različici za splošno razpoložljivost. Lighthouse lahko zagotovi tudi dodatne vpoglede. Če želite izkoristiti te prednosti, razmislite o uvedbi zemljevidov izvorne kode. [Preberite več o zemljevidih izvorne kode](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Manjkajoči zemljevidi izvorne kode za veliko lastno knjižnico JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "Manjkajoč zemljevid izvorne kode v veliki datoteki JavaScript"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Opozorilo: V atributu `.sourcesContent` manjka 1 element}one{Opozorilo: V atributu `.sourcesContent` manjka # element}two{Opozorilo: V atributu `.sourcesContent` manjkata # elementa}few{Opozorilo: V atributu `.sourcesContent` manjkajo # elementi}other{Opozorilo: V atributu `.sourcesContent` manjka # elementov}}"}, "core/audits/valid-source-maps.js | title": {"message": "Stran ima veljavne zemljevide izvorne kode"}, "core/audits/viewport.js | description": {"message": "<PERSON><PERSON> tega, da <PERSON><meta name=\"viewport\">` optimizira vašo aplikacijo za velikosti zaslona mobilnih naprav, prepre<PERSON>u<PERSON> tudi [300-milisekundno zakasnitev uporabniških vnosov](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Preberite več o uporabi metaoznake viewport](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Ni <PERSON>nak `<meta name=\"viewport\">`"}, "core/audits/viewport.js | failureTitle": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s tem: `width` ali `initial-scale`"}, "core/audits/viewport.js | title": {"message": "<PERSON><PERSON> `<meta name=\"viewport\">` s tem: `width` ali `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "To je delo za blokiranje niti, do katerega pride med meritvijo interakcije do naslednjega izrisa. [Preberite več o meritvi interakcije do naslednjega izrisa](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "{timeInMs, number, milliseconds} ms je bilo porabljenih za dogodek »{interactionType}«"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Zmanjšanje dela med ključno interakcijo"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Zakasnitev vnosa"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Zakasnitev predstavitve"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Čas obdelave"}, "core/audits/work-during-interaction.js | title": {"message": "Minimizira delo med ključno interakcijo"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate uporabo atributov ARIA v aplikaciji, s čimer lahko izboljšate izkušnjo za uporabnike pomožnih tehnologij, kot je bralnik zaslona."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "To so priložnosti za izboljšanje nadomestne vsebine za zvok in videoposnetke. S tem lahko izboljšate izkušnjo za uporabnike z motnjami sluha ali vida."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> in video"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Ti elementi izpostavijo pogoste najboljše postopke za zagotavljanje dostopnosti."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "Najboljši postopki"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Ta preverjanja izpostavijo priložnosti za [izboljšanje dostopnosti vaše spletne aplikacije](https://developer.chrome.com/docs/lighthouse/accessibility/). Samodejno je mogoče ugotoviti samo podnabor morebitnih težav z dostopnostjo, zato spodbujamo ročno preverjanje."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Ti elementi se nanašajo na področja, ki jih ne more obdelati samodejno orodje za preizkušanje. Več o tem lahko preberete v našem vodniku o [izvedbi pregleda dostopnosti](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Dostopnost"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate berljivost vsebine."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate, kako si uporabniki v različnih jezikih tolmačijo vašo vsebino."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internacionalizacija in lokalizacija"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Tu imate na voljo priložnosti, da izboljšate pomen kontrolnikov v aplikaciji. S tem lahko izboljšate izkušnjo uporabnikov pomožne tehnologije, kot je bralnik zaslona."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "Imena in oznake"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "To so <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da izboljšate premikanje po aplikaciji s tipkovnico."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Pomikanje"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "To so <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da z uporabo pomožnih tehnologij, kot je bralnik zaslona, izboljšate izkušnjo pri branju tabelarnih podatkov ali podatkov na seznamih."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "Tabele in seznami"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Združljivost brskalnika"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "Najboljši postopki"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "Splošno"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Zaupanje in varnost"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Uporabniška izkušnja"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Proračuni za uspešnost postavljajo standarde za uspešnost spletnega mesta."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Več informacij o delovanju aplikacije. Te številke [neposredno ne vplivajo](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na rezultat uspešnosti."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostika"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Bistveni vidik delovanja je, kako hitro se upodabljajo slikovne pike na zaslonu. Ključni meritvi: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> s<PERSON> bar<PERSON>"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Izboljšave prvega barvanja"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Ti predlogi lahko pomagajo pri hitrejšem nalaganju strani. [Neposredno ne vplivajo](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) na rezultat uspešnosti."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "Pril<PERSON>ž<PERSON>ti"}, "core/config/default-config.js | metricGroupTitle": {"message": "Meritve"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Izboljšajte splošno izkušnjo nalaganja, da bo stran odzivna in čim prej pripravljena na uporabo. Ključni meritvi: Čas do interaktivnosti, <PERSON><PERSON><PERSON>"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Splošne izboljšave"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Delovanje"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Ta preverjanja potrjujejo vidike moderne spletne aplikacije. [<PERSON><PERSON><PERSON>, kaj odlikuje dobro moderno spletno aplikacijo](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "Ta preverjanja zahteva osnovni [kontrolni seznam za MSA](https://web.dev/pwa-checklist/), vendar jih Lighthouse ne opravi samodejno. Preverjanja vplivajo na vaš rezultat, vendar je pome<PERSON>, da jih opravite ročno."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "MSA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "Namestljivo"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimizirano za PWA"}, "core/config/default-config.js | seoCategoryDescription": {"message": "S temi preverjanji se <PERSON>, da stran upošteva osnovna priporočila glede optimizacije iskalnikov. Lighthouse pri tem ne upošteva številnih dodatnih dejavnikov, ki lahko vplivajo na uvrstitev v rezultatih iskanja, vključno z učinkovitostjo delovanja v [osnovnih podatkih za splet](https://web.dev/learn-core-web-vitals/). [Preberite več o osnovah Iskanja Google](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Na spletnem mestu izvedite ta dodatna preverjanja, da preverite dodatne najboljše postopke za SEO."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Oblikujte HTML na način, ki iskalnikom po vsebini omogoča boljše razumevanje vsebine vaše aplikacije."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "Najboljši postopki glede vsebine"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Če želite, da bo prikazana v rezultatih iskanja, morajo imeti iskalniki po vsebini dostop do vaše aplikacije."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Iskanje po vsebini in indeksiranje"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "<PERSON><PERSON><PERSON><PERSON>, da so strani prilagojene za mobilne naprave, da uporabnikom ne bo treba vleči s prsti skupaj ali narazen ali povečevati slike, če bodo želeli brati strani z vsebino. [<PERSON><PERSON><PERSON>, kako posk<PERSON>, da bodo strani prijazne do mobilnih naprav](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Prilagojeno za mobilne naprave"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "<PERSON><PERSON><PERSON> je, da ima preizkušena naprava počasnejši CPE kot ga pričakuje Lighthouse. To lahko negativno vpliva na rezultat uspešnosti. Preberite več o [umerjanju ustreznega množitelja za upočasnitev CPE-ja](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "Stran se morda ne naloži po pričakovanjih, ker je bil preizkusni URL ({requested}) preusmerjen na {final}. Poskusite neposredno preizkusiti drugi URL."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "Stran se je nalo<PERSON> prepoč<PERSON>, da bi se nalaganje dokončalo znotraj časovne omejitve. Rezultati so morda nepopolni."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Časovna omejitev za izbris predpomnilnika brskalnika je potekla. Poskusite znova pregledati to stran in prijavite napako, če težave ne odpravite."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{Na tej lokaciji so morda shranjeni podatki, ki vplivajo na uspešnost nalaganja: {locations}. Preglejte to stran v anonimnem oknu, da preprečite, da bi ti viri vplivali na vaše rezultate.}one{Na teh lokacijah so morda shranjeni podatki, ki vplivajo na uspešnost nalaganja: {locations}. Preglejte to stran v anonimnem oknu, da preprečite, da bi ti viri vplivali na vaše rezultate.}two{Na teh lokacijah so morda shranjeni podatki, ki vplivajo na uspešnost nalaganja: {locations}. Preglejte to stran v anonimnem oknu, da preprečite, da bi ti viri vplivali na vaše rezultate.}few{Na teh lokacijah so morda shranjeni podatki, ki vplivajo na uspešnost nalaganja: {locations}. Preglejte to stran v anonimnem oknu, da prepre<PERSON>ite, da bi ti viri vplivali na vaše rezultate.}other{Na teh lokacijah so morda shranjeni podatki, ki vplivajo na uspešnost nalaganja: {locations}. Preglejte to stran v anonimnem oknu, da preprečite, da bi ti viri vplivali na vaše rezultate.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Časovna omejitev za brisanje izvornih podatkov je potekla. Poskusite znova pregledati to stran in prijavite napako, če težave ne odpravite."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "<PERSON><PERSON> strani, naložene z zahtevo GET, so primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Predpomniti je mogoče samo strani s kodo statusa 2XX."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Chrome je zaznal poskus izvajanja JavaScripta v predpomnilniku."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON><PERSON>, ki so zahtevale AppBanner, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen z zastavicami. Če ga želite lokalno omogočiti v tej napravi, obiščite chrome://flags/#back-forward-cache."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen v ukazni vrstici."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen, ker ni dovolj pomnilnika."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Pooblaščenec ne podpira predpomnilnika za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen za predupodabljalnik."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "Strani ni mogoče predpomniti, ker ima primerek BroadcastChannel z registriranimi poslušalci."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Strani z glavo cache-control:no-store ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Predpomnilnik je bil namenoma izbrisan."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "Stran je bila izvržena iz predpomnilnika, zato da je bila lahko predpomnjena druga stran."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON><PERSON>, ki vs<PERSON><PERSON><PERSON><PERSON> v<PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "<PERSON><PERSON>, ki uporabljajo FileChooser API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "<PERSON><PERSON>, ki uporabljajo File System Access API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "<PERSON><PERSON>, ki uporabljajo Media Device Dispatcher, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Predvajalnik predstavnosti je predva<PERSON><PERSON><PERSON>, preden ga je obiskovalec zapustil."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "<PERSON><PERSON>, ki uporabljajo MediaSession API in nastavijo stanje predvajanja, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "<PERSON><PERSON>, ki uporabljajo MediaSession API in nastavijo rutine za obravnavo dejanj, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi bralnika zaslona."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo <PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Strani, ki uporabljajo WebAuthentication API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "<PERSON><PERSON>, ki uporabljajo WebBluetooth API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "<PERSON><PERSON>, ki uporabljajo WebUSB API, niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> name<PERSON>ki proces ali delovni proces, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Nalaganje dokumenta se ni končalo, preden ga je obiskovalec zapustil."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON>b zapuščanju strani je bila prisotna pasica za aplikacije."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "<PERSON>b zapuščanju strani je bil prisoten Chromov upravitelj gesel."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "<PERSON>b zapuščanju strani je potekalo povzemanje DOM-a."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "<PERSON>b zapuščanju strani je bil prisoten pregledovalnik povzemanja DOM-a."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi razširitev, ki uporabljajo API za sporočanje."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Razširitve z dolgotrajno povezavo morajo pred preklopom v predpomnilnik za hitro obnovitev strani prekiniti povezavo."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Razširitve z dolgotrajno povezavo so poskusile poslati sporočila okvirom v predpomnilniku za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi razširitev."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "Ob zapuščanju strani je bilo prikazano modalno pogovorno okno, na primer za vnovično pošiljanje obrazca ali pogovorno okno http za geslo."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "<PERSON><PERSON> zapuščanju strani je bila prikazana stran brez povezave."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "<PERSON>b zapuščanju strani je bila prisotna vrstica za posredovanje zaradi pomanjkanja pomnilnika."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "<PERSON>b zapuščanju strani so bile prikazane zahteve za dovoljenja."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "<PERSON>b zapuščanju strani je bil prisoten preprečevalec pojavnih oken."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "<PERSON>b zapuščanju strani so bile prikazane podrobnosti Varnega brskanja."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Varno brskanje je to stran smatralo kot neprimerno in je blokiralo pojavno okno."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "Ko je bila stran v predpomnilniku za hitro obnovitev strani, je bil aktiviran proces storitve."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi napake dokumenta."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ni mogoče shraniti v predpomnilniku za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "Stran je bila izvržena iz predpomnilnika, zato da je bila lahko predpomnjena druga stran."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "<PERSON><PERSON>, ki so podelile dostop do toka predstavnosti, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "<PERSON><PERSON>, ki imajo odprto povezavo IndexedDB, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Uporabljeni so bili neprimerni API-ji."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Strani, v katere razširitve vstavijo JavaScript, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Strani, v katere razširitve vstavijo StyleSheet, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | internalError": {"message": "<PERSON><PERSON><PERSON>."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Predpomnilnik za hitro obnovitev strani je onemogočen zaradi zahteve za ohranjanje povezave."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON>, ki uporab<PERSON><PERSON>jo zaklepanje tipkovnice, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | loading": {"message": "Nalaganje strani se ni končalo, preden jo je obiskovalec zapustil."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, pri katerih ima glavno sredstvo cache-control:no-cache, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, pri katerih ima glavno sredstvo cache-control:no-store, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Premikanje je bilo prek<PERSON>, preden je bilo stran mogoče obnoviti iz predpomnilnika za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Stran je bila izvržena iz predpomnilnika, ker je dejavna omrežna povezava prejela preveč podatkov. Chrome omejuje koli<PERSON> podatkov, ki jih lahko stran prejme, medtem ko je predpomnjena."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "<PERSON><PERSON>, ki imajo fetch() ali <PERSON>, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Stran je bila izvržena iz predpomnilnika za hitro obnovitev strani, ker je bila pri dejavni omrežni povezavi vključena preusmeritev."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Stran je bila izvrž<PERSON> iz predpomnilnika, ker je bila omrežna povezava predolgo odprta. Chrome omejuje časovno obdobje, med katerim lahko stran prejema podatke, medtem ko je predpomnjena."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON><PERSON>, ki nimajo veljavne glave odziva, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Do premikanja je prišlo v okviru, ki ni glavni okvir."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> indeksirane transakcije zbirke podatkov, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Strani z omrežno zahtevo, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Strani z omrežno zahtevo fetch, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Strani z omrežno zahtevo, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Strani z omrežno zahtevo XHR, ki <PERSON>e poteka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON><PERSON>, ki uporabljajo sliko v sliki, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | printing": {"message": "<PERSON><PERSON>, ki prikazujejo uporabniški vmesnik za tiskanje, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "Stran je bila odprta z izrezkom »`window.open()`« in drug zavihek ima sklic nanjo ali pa se je stran odprla v oknu."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Proces upodabljalnika za stran v predpomnilniku za hitro obnovitev strani se je zrušil."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Proces upodabljalnika za strani v predpomnilniku za hitro obnovitev strani je ustavljen."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za zajem zvoka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za senzorje, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Strani, ki so zahtevale sinhronizacijo v ozadju ali dovoljenja za pridobitev, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za MIDI, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za obvestila, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "<PERSON><PERSON>, ki so zahtevale dostop do shrambe, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "<PERSON><PERSON>, ki so zahtevale dovoljenja za zajem videa, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Predpomniti je mogoče samo strani s shemo URL-ja HTTP/HTTPS."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "To stran je zahteval proces storitve, medtem ko je v predpomnilniku za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "Proces storitve je poskušal strani v predpomnilniku za hitro obnovitev strani poslati izrezek `MessageEvent`."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "Registracija za ServiceWorker je bila preklicana, medtem ko je bila stran v predpomnilniku za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "Stran je bila izvržena iz predpomnilnika za hitro obnovitev strani zaradi aktiviranja procesa storitve."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome je znova zagnal in počistil vnose v predpomnilniku za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SpeechSynthesis, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "<PERSON><PERSON> iframe na strani je zač<PERSON> premikanje, ki se ni dokončalo."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "<PERSON><PERSON>, pri katerih ima del sredstva cache-control:no-cache, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "<PERSON><PERSON>, pri katerih ima del sredstva cache-control:no-store, ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Stran je presegla najdaljši čas v predpomnilniku za hitro obnovitev strani in je potekla."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Časovna omejitev strani je potekla med vstopom v predpomnilnik za hitro obnovitev strani (verjetno zaradi dolgotrajno izvajajočih se rutin za obravnavo pagehide)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Stran ima rutino za obravnavo za odstranjevanje v glavnem okviru."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "Stran ima rutino za obravnavo za odstranjevanje v podokviru."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Brskalnik je spremenil glavo za preglasitev uporabnikovega posrednika."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON><PERSON>, ki so podelile dostop za snemanje videa ali zvoka, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "<PERSON>rani, ki uporabljajo WebDatabase, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webHID": {"message": "<PERSON><PERSON>, ki uporabl<PERSON>jo WebHID, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo WebLocks, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "<PERSON><PERSON>, ki <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON><PERSON> WebOTPService, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Strani s tehnologijo WebRTC ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webShare": {"message": "<PERSON><PERSON>, ki uporab<PERSON><PERSON>jo WebShare, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Strani z vtičnico WebSocket ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Strani z razlogom WebTransport ne morejo vstopiti v predpomnilnik za hitro obnovitev strani."}, "core/lib/bf-cache-strings.js | webXR": {"message": "<PERSON><PERSON>, ki up<PERSON><PERSON><PERSON><PERSON>jo WebXR, trenutno niso primerne za predpomnilnik za hitro obnovitev strani."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Razmislite o dodajanju shem URL-jev https: in http: (brskalniki, ki podpirajo element »strict-dynamic«, jih prezrejo) zaradi povratne združljivosti s starejšimi brskalniki."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "Direktiva disown-opener je zastarela od pravilnika CSP3. Uporabite glavo pravilnika Cross-Origin-Opener-Policy."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "Direktiva referrer je zastarela od pravilnika CSP2. Uporabite glavno pravilnika Referrer-Policy."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "Direktiva reflected-xss je zastarela od pravilnika CSP2. Uporabite glavo za X-XSS-Protection."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Manjkajoči element base-uri <PERSON>, da vstavljene oznake <base> nastavijo temeljni URL za vse relativne URL-je (npr. skripte) do domene, ki jo nadzira napadalec. Razmislite o nastavitvi elementa base-uri na »none« ali »self«."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Manjkajoči element object-src omogoča vstavitev vtičnikov, ki izvajajo skripte, ki niso varni. Razmislite o nastavitvi elementa object-src na »none«, če je mogoče."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Ni direktive script-src. <PERSON><PERSON><PERSON> tega je lahko dovoljeno izvajan<PERSON>, ki niso varni."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Ste pozabili podpičje? <PERSON><PERSON><PERSON> je, da je {keyword} direktiva, ne pa kl<PERSON>na beseda."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Žetoni morajo uporabljati nabor znakov base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Žetoni morajo biti dolgi najmanj 8 znakov."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "V tej direktivi se izogibajte uporabi golih shem URL-jev ({keyword}). Gole sheme URL-jev omogočajo pridobivanje skriptov iz domene, ki ni varna."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "V tej direktivi se izogibajte uporabi golih nadomestnih znakov ({keyword}). Goli nadomestni znaki omogočajo pridobivanje skriptov iz domene, ki ni varna."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Cilj za poročanje je konfiguriran samo prek direktive »report-to«. Direktivo podpirajo samo brskalniki na podlagi Chromiuma, zato <PERSON>rip<PERSON>, da uporabite tudi direktivo »report-uri«."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "V nobenem pravilniku CSP ni konfiguriran cilj za poročanje. Zato je težko dolgoročno ohranjati pravilnik CSP in ga nadzirati zaradi morebitnih težav z delovanjem."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Sezname dovoljenih pri gostiteljih je pogosto mogoče zaobiti. Razmislite o uporabi žetonov ali zgoščenih vrednosti za pravilnik CSP skupaj z elementom »strict-dynamic«, če je treba."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Neznana direktiva pravilnika CSP."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "<PERSON><PERSON><PERSON> je, da je {keyword} ne<PERSON><PERSON><PERSON>na kl<PERSON><PERSON> beseda."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "Element »unsafe-inline« omogoča izvajanje na strani umeščenih skriptov, ki niso varni, in rutin za obravnavo dogodkov. Razmislite o uporabi žetonov ali zgoščenih vrednosti za pravilnik CSP, s katerim bi omogočali posamezne skripte."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Razmislite o dodajanju elementa »unsafe-inline« (brskalniki, ki podpirajo žetone/zgoščene vrednosti, ga prezrejo) zaradi povratne združljivosti s starejšimi brskalniki."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Pri avtorizaciji ne bo mogoče uporabiti nadomestnega znaka (*) pri obdelavi CORS `Access-Control-Allow-Headers`."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Zahteve za sredstva, katerih URL-ji so vsebovali odstranjene znake za presledke »`(n|r|t)`« in znake »manj kot« (»`<`«), so blokirane. Odstranite prelome vrstic in kodirajte znake »manj kot« z mest, kot so vrednosti atributov elementov, da bi lahko naložili ta sredstva."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "Možnost »`chrome.loadTimes()`« je zastarela, zato namesto nje uporabite standardizirani API: Čas krmarjenja 2."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "Možnost »`chrome.loadTimes()`« je zastarela, zato namesto nje uporabite standardizirani API: Čas upodabljanja."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "Možnost »`chrome.loadTimes()`« je zastarela, zato namesto nje uporabite standardizirani API: »`nextHopProtocol`« v Času krmarjenja 2."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "<PERSON><PERSON><PERSON><PERSON>, ki vseb<PERSON>je<PERSON> znak »`(0|r|n)`«, bodo zavrn<PERSON> in ne prirezani."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Opustitev pravilnika, ki predpisuje isti izvor, z nastavitvijo možnosti »`document.domain`« je zastarela in bo privzeto onemogočena. To opozorilo o zastarelosti je za dostop iz več izvorov, ki je bil omogočen z nastavitvijo možnosti »`document.domain`«."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Sprožanje »{PH1}« iz elementov iframe iz več izvorov je zastarelo in bo v prihodnosti odstranjeno."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Atribut »`disableRemotePlayback`« uporabite za onemogočanje privzete integracije za predvajanje namesto izbirnika »`-internal-media-controls-overlay-cast-button`«."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "Možnost »{PH1}« je zastarela. Namesto tega uporabite »{PH2}«."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "To je primer prevedenega sporočila o težavi zaradi zastarelosti."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Opustitev pravilnika, ki predpisuje isti izvor, z nastavitvijo možnosti »`document.domain`« je zastarela in bo privzeto onemogočena. Če želite še naprej uporabljati to funkcijo, onemogoč<PERSON> gru<PERSON><PERSON>, ki uporabljajo ključ izvora, tako da pošljete glavo »`Origin-Agent-Cluster: ?0`« skupaj z odgovorom HTTP za dokumente in okvire. Več podrobnosti: https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "Možnost »`Event.path`« je zastarela in bo odstranjena. Namesto tega uporabite »`Event.composedPath()`«."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "<PERSON><PERSON> m<PERSON> »`Expect-CT`« je zastarela in bo odstranjena. Chrome zahteva preglednost potrdila za vsa javna zaupanja vredna potrdila, izdana po 30. aprilu 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Več informacij je na voljo na strani s stanjem funkcij."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "»`getCurrentPosition()`« in »`watchPosition()`« ne delujeta več z izvori, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "Možnosti »`getCurrentPosition()`« in »`watchPosition()`« sta označeni kot zastareli za izvore, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "»`getUserMedia()`« ne deluje več z izvori, ki niso varni. Če želite uporabiti to funkcijo, razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "Možnost »`RTCPeerConnectionIceErrorEvent.hostCandidate`« je zastarela. Namesto nje uporabite »`RTCPeerConnectionIceErrorEvent.address`« ali »`RTCPeerConnectionIceErrorEvent.port`«."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "<PERSON><PERSON><PERSON> in arbitrarni podatki iz dogodka procesa storitve `canmakepayment` so zast<PERSON><PERSON> in bodo odstranjeni: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Spletno mesto je zahtevalo podsredstvo iz omrežja, do katerega je lahko dostopalo samo zaradi uporabnikovega prednostnega omrežnega položaja. Te zahteve razkrivajo naprave in strežnike, ki niso javni, v internetu, s čimer se poveča nevarnost za napad s poneverbo zahteve med spletnimi mesti (CSRF) in/ali puščanje podatkov. Za zmanjšanje teh nevarnosti Chrome označi zahteve podsredstvom, ki niso javna, kot zastarele, pri sprožanju iz konte<PERSON>, ki niso varni, in jih bo začel blokirati."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "Slogov CSS ni mogoče naložiti iz URL-jev za »`file:`«, razen če se končajo s datotečno pripono `.css`."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Uporaba možnosti »`SourceBuffer.abort()`« za prekinitev asinhrone odstranitve obsega za »`remove()`« je zastarela zaradi spremembe specifikacije. Podpora bo v prihodnosti odstranjena. Namesto tega raje poslušajte dogodek `updateend`. Možnost »`abort()`« je namenjena samo za prekinitev asinhronega dodajanja predstavnosti ali ponastavitev stanja razčlenjevalnika."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Nastavitev možnosti »`MediaSource.duration`« pod najvišji časovni žig predstavitve katerih koli medpomnjenih kodiranih okvirov je zastarela zaradi spremembe specifikacije. Podpora za implicitno odstranitev prirezanih medpomnjenih predstavnosti bo v prihodnosti odstranjena. Namesto tega izvedite eksplicitno možnost »`remove(newDuration, oldDuration)`« na vseh »`sourceBuffers`«, kjer je `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Ta sprememba bo za<PERSON><PERSON> veljati z mejnikom {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Spletni MIDI bo zahteval dovoljenje za uporabo, tudi če sysex ni določen v »`MIDIOptions`«."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Obvestilnega API-ja ni več dovoljeno uporabljati iz izvorov, ki niso varni. Razmislite o tem, da bi za aplikacijo uporabili varen izvor, kot je HTTPS. Več podrobnosti: https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Dovoljenja za obvestilni API ni več mogoče zahtevati iz elementa iframe iz več izvorov. Razmislite o tem, da bi namesto tega zahtevali dovoljenje od okvira na visoki ravni ali odprli novo okno."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Vaš partner p<PERSON><PERSON><PERSON> pri pogajanju uporabiti zastarelo različico (D)TLS. Obrnite se na partnerja, da to popravi."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL je v kontekstih, ki niso varni, zastarel in bo kmalu odstranjen. Uporabite spletno shrambo ali indeksirano zbirko podatkov."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Če določite lastnost `overflow: visible` za oznake slik, videooznake in oznake platna, se lahko zaradi tega zgodi, da bodo vizualno vs<PERSON><PERSON> ustvarjale zunaj mej elementa. Oglejte si https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "Možnost »`paymentManager.instruments`« je zastarela. Za rutine za obravnavo plačil raje uporabite namestitve »just-in-time«."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "Klic API-ja `PaymentRequest` je zaobšel direktivo `connect-src` pravilnika o varnosti vsebine. To zaobidenje je zastarelo. Dodajte identifikator plačilnega sredstva iz API-ja `PaymentRequest` (v polju `supportedMethods`) v direktivo `connect-src` pravilnika o varnosti vsebine."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "Možnost »`StorageType.persistent`« je zastarela. Namesto tega uporabite standardizirano `navigator.storage`."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "Element »`<source src>`« z nadrejenim elementom »`<picture>`« je neveljaven in torej prezrt. Namesto tega uporabite »`<source srcset>`«."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "Možnost »`window.webkitStorageInfo`« je zastarela. Namesto tega uporabite standardizirano `navigator.storage`."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "<PERSON><PERSON><PERSON><PERSON> za <PERSON>sredstva, katerih URL-ji vsebujejo vdelane poverilnice (npr. »`**********************/`«), so blo<PERSON><PERSON>."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "<PERSON><PERSON><PERSON><PERSON> »`DtlsSrtpKeyAgreement`« je odstranjena. Na<PERSON><PERSON> ste vrednost »`false`« za to o<PERSON><PERSON><PERSON>, kar se razlaga kot poskus uporabe odstranjene metode »`SDES key negotiation`«. Ta funkcija je odstranjena. Namesto nje uporabite storitev, ki podpira »`DTLS key negotiation`«."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "O<PERSON><PERSON><PERSON> »`DtlsSrtpKeyAgreement`« je odstranjena. Za to omejitev ste navedli vrednost »`true`«, kar ni imelo nobenega vpliva, lahko pa to omejitev odstranite zaradi jasnosti."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "Zaznan je »`Complex Plan B SDP`«. Dialekt elementa »`Session Description Protocol`« ni več podprt. Namesto tega uporabite »`Unified Plan SDP`«."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "»`Plan B SDP semantics`«, ki se uporablja pri sestavljanju »`RTCPeerConnection`« z »`{sdpSemantics:plan-b}`«, je starej<PERSON> nestanda<PERSON> različ<PERSON> »`Session Description Protocol`«, ki je trajno odstranjena s spletne platforme. Še vedno je na voljo pri sestavljanju »`IS_FUCHSIA`«, vendar jo nameravamo čim prej izbrisati. Ne zanašajte se več nanjo. Za stanje glejte: https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Možnost »`rtcpMuxPolicy`« je zastarela in bo odstranjena."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "Za »`SharedArrayBuffer`« bo potrebno izoliranje od drugih izvorov. Več podrobnosti: https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "Možnost »`speechSynthesis.speak()`« brez aktiviranja uporabnika je zastarela in bo odstranjena."}, "core/lib/deprecations-strings.js | title": {"message": "Uporabljena je bila zastarela funkcija"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ki želijo še naprej uporabljati »`SharedArrayBuffer`«, morajo omogočiti izoliranje od drugih izvorov. Glejte: https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "Možnost »{PH1}« je samo za določenega od dobavitelja. Namesto tega uporabite standardni {PH2}."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "JSON odgovora v »`XMLHttpRequest`« ne podpira standarda UTF-16."}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Sinhrona možnost »`XMLHttpRequest`« na glavni niti je zastarela, ker slabo vpliva na izkušnjo končnega uporabnika. Dodatna pomoč: https://xhr.spec.whatwg.org/."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "Možnost »`supportsSession()`« je zastarela. Uporabite »`isSessionSupported()`« in namesto tega preverite razrešeno logično vrednost."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Čas blokiranja glavne niti"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "TTL predpomnjenja"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Opis"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Neuspešni elementi"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Lokacija"}, "core/lib/i18n/i18n.js | columnName": {"message": "Ime"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Prek proračuna"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "Velikost sredstva"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Vrsta s<PERSON>va"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Velikost"}, "core/lib/i18n/i18n.js | columnSource": {"message": "Vir"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Začetni čas"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Porabljeni č<PERSON>"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Velikost prenosa"}, "core/lib/i18n/i18n.js | columnURL": {"message": "URL"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "Morebitni prihranki"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "Morebitni prihranki"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "{wastedBytes, number, bytes} KiB morebitnega prihranka"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{Najden je bil 1 element}one{Najden je bil # element}two{Najdena sta bila # elementa}few{Najdeni so bili # elementi}other{Najdenih je bilo # elementov}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "{wastedMs, number, milliseconds} ms morebitnega prihranka"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "<PERSON><PERSON><PERSON> smiselno <PERSON>"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Slika"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interakcija do naslednjega izrisa"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "Visoka"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "Nizka"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Srednja"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Najv. potencial. zakasn. od prvega vnosa"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Predstavnost"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "Drugo"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "Drugi viri"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Slogovna datoteka"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Drugi ponudniki"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Skupno"}, "core/lib/lh-error.js | badTraceRecording": {"message": "Pri snemanju sledi ob nalaganju strani je prišlo do napake. Znova zaženite Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Potek časovne omejitve pri čakanju na začetno povezavo protokola za odpravljanje napak"}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Chrome med nalaganjem strani ni zbral posnetkov zaslona. Poskrbite, da je vsebina vidna na strani, nato poskusite znova zagnati Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Strežnikom DNS ni uspelo razrešiti navedene domene."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "V zahtevanem zbiralniku {artifactName} je prišlo do napake: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "Prišlo je do notranje napake Chroma. Znova zaženite Chrome in poskusite znova zagnati Lighthouse."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "Zahtevani z<PERSON>al<PERSON> {artifactName} se ni izvedel."}, "core/lib/lh-error.js | noFcp": {"message": "Na strani ni bila prikazana nobena vsebina. Poskrbite, da bo okno brskalnika med nalaganjem v ospredju, in poskusite znova. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Na strani ni bila prikazana vsebina, ki izpolnjuje pogoje za največji vsebinski izris. Poskrbite, da ima stran veljaven element največjega vsebinskega izrisa, in poskusite znova. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "Prikazana stran ni v obliki HTML (prikazana je kot vrsta razširitve MIME {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Ta različica Chroma je preveč <PERSON>a, da bi podpirala »{featureName}«. Če si želite ogledati popolne rezultate, uporabite novejšo različico."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevanega URL-ja, ker se je stran nehala odzi<PERSON>i."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Navedeni URL nima veljavnega varnostnega potrdila. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome je preprečil nalaganje strani z vrinjenim zaslonom. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve. (Podrobnosti: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Orodje Lighthouse ni utegnilo zanesljivo naložiti zahtevane strani. Poskrbite, da preizkušate pravilen URL in da se strežnik ustrezno odziva na vse zahteve. (Koda stanja: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Nalaganje strani je trajalo predolgo. Upoštevajte priložnosti v poročilu, da zmanjšate čas nalaganja strani, nato poskusite znova zagnati Lighthouse. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Čakanje na odziv protokola za DevTools je preseglo dodeljeni čas. (Metoda: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Trajanje pridobivanja vsebine sredstva je preseglo dodeljeni čas"}, "core/lib/lh-error.js | urlInvalid": {"message": "<PERSON><PERSON><PERSON> je, da ste navedli neveljaven URL."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Vrsta razširitve MIME strani je XHTML: Lighthouse izrecno ne podpira te vrste dokumenta."}, "core/user-flow.js | defaultFlowName": {"message": "Uporabniški tok ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Poročilo o pomikanju ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Poročilo o povzetku ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Poročilo o časovnem obdobju ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "Vsa poročila"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "Kategorije"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Dostopnost"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "Najboljši postopki"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Delovanje"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Moderna spletna aplikacija"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "Namizna r<PERSON>č<PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Razumevanje poročila o toku orodja Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Uporaba poročil o pomikanju za …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Uporaba poročil o povzetku za …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Uporaba poročil o časovnem obdobju za …"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Pridobivanje rezultata uspešnosti orodja Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> meritev uspešnosti nalaganja strani, kot sta največji vsebinski izris in indeks hitrosti."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Ocenjevanje zmožnosti modernih spletnih aplikacij."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Iskanje težav z dostopnostjo v enostranskih aplikacijah ali kompleksnih obrazcih."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Ovrednotenje najboljših postopkov menijev in elementov uporabniškega vmesnika, skritimi za interakcijo."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Merjenje pomikov postavitev in časa izvajanja JavaScripta v seriji interakcij."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Odkrivanje priložnosti za uspešnost zaradi izboljšanja izkušnje pri dolgotrajnih straneh in enostranskih aplikacijah."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "Največji vpliv"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativna revizija}one{{numInformative} informativna revizija}two{{numInformative} informativni reviziji}few{{numInformative} informativne revizije}other{{numInformative} informativnih revizij}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "<PERSON>lag<PERSON><PERSON> strani"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Poročila o pomikanju analizirajo nalaganje ene strani, enako kot izvirna poročila orodja Lighthouse."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Poročilo o pomikanju"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} poročilo o pomikanju}one{{numNavigation} poročilo o pomikanju}two{{numNavigation} poročili o pomikanju}few{{numNavigation} poročila o pomikanju}other{{numNavigation} poročil o pomikanju}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} morebitno uspešna revizija}one{{numPassableAudits} morebitno uspešna revizija}two{{numPassableAudits} morebitno uspešni reviziji}few{{numPassableAudits} morebitno uspešne revizije}other{{numPassableAudits} morebitno uspešnih revizij}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{<PERSON><PERSON><PERSON>no je bila opravljena {numPassed} revizija.}one{Uspešno je bila opravljena {numPassed} revizija.}two{Uspešno sta bili opravljeni {numPassed} reviziji.}few{Uspešno so bile opravljene {numPassed} revizije.}other{Uspešno je bilo opravljenih {numPassed} revizij.}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Povprečno"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "Napaka"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "Š<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Dobro"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "<PERSON><PERSON><PERSON> stanje strani"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Poročila o povzetku analizirajo stran v določenem stanju, običajno po uporabniških interakcijah."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Poročilo o povzetku"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} poročilo o povzetku}one{{numSnapshot} poročilo o povzetku}two{{numSnapshot} poročili o povzetku}few{{numSnapshot} poročila o povzetku}other{{numSnapshot} poročil o povzetku}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Povzetek"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Uporabniške interakcije"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Poročila o časovnem obdobju analizirajo poljubno časovno obdobje, ki običajno vsebuje uporabniške interakcije."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Poročilo o časovnem obdobju"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} poročilo o časovnem obdobju}one{{numTimespan} poročilo o časovnem obdobju}two{{numTimespan} poročili o časovnem obdobju}few{{numTimespan} poročila o časovnem obdobju}other{{numTimespan} poročil o časovnem obdobju}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Poročilo o toku uporabnikov orodja Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "Za anim<PERSON><PERSON> vs<PERSON><PERSON> uporabit<PERSON> [`amp-anim`](https://amp.dev/documentation/components/amp-anim/), da zmanjšate uporabo CPE-ja, ko vsebina ni na zaslonu."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Razmislite o tem, da bi vse komponente za [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) prikazali v oblikah zapisa WebP, pri tem pa določili ustrezno rezervno možnost za druge brskalnike. [Več o tem](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "<PERSON><PERSON><PERSON><PERSON>, da uporab<PERSON><PERSON>te oznake [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) za slike, ki se samodejno odloženo nalagajo. [Več o tem](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "<PERSON>a [postavitve upodabljanj AMP na strani strežnika](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) uporabite orodja, kot je [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "Preberite [dokumentacijo za AMP](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) in se prepri<PERSON><PERSON><PERSON>, da so podprti vsi slogi."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponenta [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) podpira atribut [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) za <PERSON>, katera slikovna sredstva naj bodo uporabljena na podlagi velikosti zaslona. [Več o tem](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Če upodabljate zelo velike sezname, razmislite o uporabi navideznega drsenja s kompletom Component Dev Kit (CDK). [Preberite več o tem](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Uporabite [razdelitev kode na ravni poti](https://web.dev/route-level-code-splitting-in-angular/), da karseda zmanjšate velikost JavaScriptnih svežnjev. Razmislite tudi o vnaprejšnejm predpomnjenju sredstev [s procesom storitve Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Če uporabljate Angular CLI, poskrbite, da bodo delovne različice generirane v načinu za splošno razpoložljivost. [Preberite več o tem](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Če uporabljate Angular CLI, v gradnjo za splošno razpoložljivost vključite zemljevide izvorne kode za pregled svežnjev. [Več o tem](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON>ti nalo<PERSON> vnaprej, da pospešite krmarjenje. [Preberite več o tem](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Razmislite o uporabi orodja `BreakpointObserver` v kompletu Component Dev Kit (CDK) za upravljanje prekinitvenih točk posnetka. [Preberite več o tem](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Razmislite o tem, da bi GIF naložili v storitev, prek katere bo na voljo za vdelavo kot videoposnetek v obliki HTML5."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "Pri definiranju pisav po meri v temi določite `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Razmislite o konfiguriranju [oblik zapisa slik WebP s slogom »Pretvorjena slika«](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) na spletnem mestu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Namestite [modul sistema Drupal](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search), ki lahko uporablja odloženo nalaganje slik. Taki moduli omogočajo odlog nalaganja slik, ki niso na zaslonu, za večjo učinkovitost delovanja."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Razmislite o uporabi modula za vstavljanje kritičnega CSS-ja in JavaScripta ali potencialno asinhrono nalaganje elementov prek JavaScripta, kot je modul [Napredno združevanje za CSS/JS](https://www.drupal.org/project/advagg). Up<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da lahko optimizacije, ki jih ponuja ta modul, pokvarijo delovanje vašega spletnega mesta, zato boste verjetno morali spremeniti kodo."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Teme, moduli in strežniške specifikacije prispevajo k odzivnemu času strežnika. Razmislite o tem, da bi poiskali bolj optimizirano temo, skrbno izbrali optimizacijski modul in/ali nadgradili strežnik. Vaši gostiteljski strežniki bi morali izkoriščati predpomnjenje operativne kode, predpomnjenje pomnilnika za skrajšanje časa poizvedbe v zbirki podatkov (denimo programsko opremo Redis ali Memcached) ter tudi optimizirano logiko aplikacij za hitrejšo pripravo strani."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Razmislite o uporabi [slogov odzivnih slik](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) za zmanjšanje velikosti slik, naloženih na strani. Če za prikaz več vsebinskih elementov na strani uporabljate funkcijo Pogledi, razmislite o razdelitvi na več strani, da omejite število vsebinskih elementov, prikazanih na določeni strani."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Preverite, ali ste omogočili možnost »Združevanje datotek CSS« na strani »Skrbništvo » Konfiguracija » Razvoj«. V [dodatnih modulih](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) lahko konfigurirate tudi naprednejše možnosti združevanja ter tako s sestavljanjem, pomanjševanjem in stiskanjem slogov za CSS pospešite svoje spletno mesto."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Preverite, ali ste omogočili možnost »Združevanje datotek JavaScript« na strani »Skrbništvo » Konfiguracija » Razvoj«. V [dodatnih modulih](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) lahko konfigurirate tudi naprednejše možnosti združevanja ter tako s sestavljanjem, pomanjševanjem in stiskanjem elementov JavaScript pospešite svoje spletno mesto."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Razmislite o tem, da bi odstranili neuporabljena pravila za CSS in potrebne knjižnice sistema Drupal priložili samo ustrezni strani ali komponenti na strani. Za podrobnosti si oglejte [povezavo do dokumentacije za Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Če želite ugotoviti, katere priložene knjižnice dodajo zunanji CSS, poskušajte z orodji Chrome DevTools izvesti [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezno temo/modul lahko ugotovite na podlagi URL-ja datoteke s slogi, ko je združevanje za CSS onemogočeno na vašem spletnem mestu sistema Drupal. Bodite pozorni na teme/module, ki imajo na seznamu mnogo datotek s slogi, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Datoteka s slogi naj bo v čakalni vrsti teme/modula samo, če je dejansko uporabljena na strani."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Razmislite o tem, da bi odstranili neuporabljene elemente JavaScipt in potrebne knjižnice sistema Drupal priložili samo ustrezni strani ali komponenti na strani. Za podrobnosti si oglejte [povezavo do dokumentacije za Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Če želite ugotoviti, katere priložene knjižnice dodajo zunanji JavaScript, poskušajte z orodji Chrome DevTools izvesti [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezno temo/modul lahko ugotovite na podlagi URL-ja skripta, ko je združevanje za JavaScript onemogočeno na vašem spletnem mestu sistema Drupal. Bodite pozorni na teme/module, ki imajo na seznamu mnogo skriptov, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Skript naj bo v čakalni vrsti teme/modula samo, če je dejansko uporabljen na strani."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "Nastavite možnost »Najvišja starost predpomnilnika strežnika proxy in brskalnika« na strani »Skrbništvo » Konfiguracija » Razvoj«. Preberite več o [predpomnilniku sistema Drupal in optimiziranju za učinkovito delovanje](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Razmislite o uporabi [modula](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search), ki samodejno optimizira in zmanjša velikost slik, nalož<PERSON>h prek spletnega mesta, hkrati pa ohrani kakovost. Preverite tudi, ali uporabljate izvorne [sloge odzivnih slik](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), ki jih zagotavlja sistem Drupal (na voljo v sistemu Drupal 8 in novejšem) za vse slike, upodobljene na spletnem mestu."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Dodate lahko namige za vnaprejšnje vzpostavljanje povezave in za vire za vnaprejšnje nalaganje DNS, in sicer tako, da namestite in konfigurirate [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search), ki zagotavljajo sredstva za namige za vire uporabnikovega posrednika."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Preverite, ali uporabl<PERSON>te izvorne [sloge odzivnih slik](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8), ki jih zagotavlja sistem Drupal (na voljo v sistemu Drupal 8 in novejšem). Sloge odzivnih slik uporabite pri upodabljanju slikovnih polj v načinih ogleda, ogledih ali slikah, naloženih prek urejevalnika WYSIWYG."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Optimize Fonts` samodejno uporabi funkcijo `font-display` za CSS ter tako z<PERSON>, da je med nalaganjem spletne pisave besedilo vidno uporabnikom."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Next-Gen Formats` pretvori slike v WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `<PERSON><PERSON> Images` odloži nalaganje slik zunaj z<PERSON>a, dokler te niso potrebne."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da elementa `Critical CSS` in `Script Delay` odložita nalaganje manj pomembnega JavaScripta/CSS-ja."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Uporabite [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) za predpomnjenje vsebine v celotnem omrežju, da izboljšate čas do prvega bajta."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Minify CSS` samodejno pomanjša vaš CSS ter tako zmanjša velikosti paketov koristne vsebine."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Minify Javascript` samodejno poman<PERSON> vaš JS ter tako zmanjša velikosti paketov koristne vsebine."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da element `Remove Unused CSS` pomaga pri tej težavi. Prepoznal bo razrede CSS-ja, ki se dejansko uporabljajo na vsaki strani vašega spletnega mesta, in odstranil vse druge, da datoteka ne bo prevelika."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Efficient Static Cache Policy` nastavi priporočene vrednosti v glavi predpomnjenja za statična sredstva."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Next-Gen Formats` pretvori slike v WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Pre-Connect Origins` samodejno doda namige za sredstva `preconnect` zaradi vzpostavljanja zgodnjih povezav s pomembnimi izvori drugih ponudnikov."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da elementa `Preload Fonts` in `Preload Background Images` dodata povezave `preload` za dodeljevanje višje stopnje prednosti pri pridobivanju sredstev, ki so trenutno zahtevana pri nadaljnjem nalaganju strani."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Uporabite [Ezoic Leap](https://pubdash.ezoic.com/speed) in omogočite, da `Resize Images` spremeni velikost slik tako, da us<PERSON><PERSON><PERSON>ra<PERSON>, ter tako zmanj<PERSON> velikosti paketov koristne vsebine."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Razmislite o tem, da bi GIF naložili v storitev, prek katere bo na voljo za vdelavo kot videoposnetek v obliki HTML5."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Razmislite o tem, da bi uporabili [vtič<PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) ali storitev, ki naložene slike samodejno pretvori v optimalne oblike."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Namestite [vtičnik za odloženo nalaganje za sistem Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading), ki omogoča odlog nalaganja slik, ki niso na zaslonu, ali preidite na predlogo, ki ponuja to funkcijo. Od različice sistema Joomla 4.0 naprej bodo vse nove slike [samodejno](https://github.com/joomla/joomla-cms/pull/30748) dobile atribut `loading` iz jedra."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Na voljo je več vtičnikov za program <PERSON><PERSON><PERSON>, ki vam lahko pomagajo [uvrstiti nujna sredstva](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) ali [odložiti manj pomembna sredstva](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da lah<PERSON> optimizacije, ki jih ponujajo ti vtičniki, pokvarijo delovanje funkcij vaših predlog ali vti<PERSON>, zato jih boste verjetno morali temeljito preizkusiti."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Predloge, razširitve in strežniške specifikacije prispevajo k odzivnemu času strežnika. Razmislite o tem, da bi poiskali bolj optimizirano predlogo, skrbno izbrali optimizacijsko razširitev in/ali nadgradili strežnik."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Razmislite o tem, da bi v kategorijah člankov prikazali izvlečke (npr. s povezavo »več o tem«), <PERSON><PERSON><PERSON><PERSON><PERSON>ov, prikazanih na posamezni strani, dal<PERSON>še objave razdelili na več strani ali uporabili vtičnik za odloženo nalaganje komentarjev."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Na voljo je več [raz<PERSON><PERSON>tev za program Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), ki lahko s sestavljanjem, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem slogov za CSS pospešijo vaše spletno mesto. Prav tako so na voljo predloge, ki zagotavljajo to funkcijo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Na voljo je več [raz<PERSON><PERSON>tev za program Joomla](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), ki lahko s sestavljanjem, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem skriptov pospešijo vaše spletno mesto. Prav tako so na voljo predloge, ki zagotavljajo to funkcijo."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [razširitev za program Joomla](https://extensions.joomla.org/), ki na vaši strani nalagajo neuporabljen CSS. Če želite ugotoviti, katere razširitve dodajo zunanji CSS, poskušajte z orodji Chrome DevTools izvesti [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezen vtičnik/temo lahko ugotovite na podlagi URL-ja datoteke s slogi. Bodite pozorni na vtičnike, ki imajo na seznamu mnogo datotek s slogi, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Datoteka s slogi naj bo v čakalni vrsti vtičnika samo, če je dejansko uporabljena na strani."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [razširitev za program Joomla](https://extensions.joomla.org/), ki na vaši strani nalagajo neuporabljen JavaScript. Če želite ugotoviti, kateri vtičniki dodajo zunanji JS, poskušajte z orodji Chrome DevTools izvesti [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezno razširitev lahko ugotovite na podlagi URL-ja skripta. Bodite pozorni na razširitve, ki imajo na seznamu mnogo skriptov, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Skript naj bo v čakalni vrsti razširitve samo, če je dejansko uporabljen na strani."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "Preberite o [predpomnjenju brskalnika v programu Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Razmislite o tem, da bi uporabili [vtičnik za optimizacijo slik](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance), ki slike stisne, a ohrani kakovost."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Razmislite o tem, da bi uporabili [vtičnik za odzivne slike](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images), ki omogoča uporabo odzivnih slik v vsebini."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Stiskanje besedila lahko omogočite tako, da omogočite stiskanje strani Gzip v programu Joomla (Sistem > Globalna konfiguracija > Strežnik)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Če JavaScriptnih sredstev ne boste združili v sveženj, razmislite o uporabi orodja [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Onemogočite Magentovo vgrajeno [združevanje JavaScripta v svežnje in pomanjševanje](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) ter razmislite, da bi namesto tega uporabili [baler](https://github.com/magento/baler/)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "Pri [definiranju pisav po meri](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html) določite `@font-display`."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Razmislite o tem, da na tržnici [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) poiščete različne razširitve drugih ponudnikov, s katerimi lahko izkoristite novejše oblike zapisa slik."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Razmislite o tem, da bi svoje predloge izdelkov in kataloga spremenili tako, da uporabljajo funkcijo spletne platforme za [»leno« nalaganje](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "Uporabite Magentovo [integracijo za Varnish](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "V nastavitvah za razvijalce v svoji trgovini omogočite možnost »Pomanjšaj datoteke CSS«. [Preberite več o tem](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "Uporabite [Terser](https://www.npmjs.com/package/terser) za pomanjšanje vseh JavaScriptnih sredstev iz statičnega uvajanja vsebine ter onemogočite vgrajeno funkcijo za pomanjšanje."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Onemogočite Magentovo vgrajeno [združevanje JavaScripta v svežnje](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Razmislite o tem, da na tržnici [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) poiščete različne razširitve drugih ponudnikov za optimiziranje slik."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Dodate lahko namige za vnaprejšnje vzpostavljanje povezave in za vire za vnaprejšnje nalaganje DNS, in sicer tako, da [spremenite postavitev teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> `<link rel=preload>` lahko dodate tako, da [spremenite postavitev teme](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "<PERSON>a samodejno optimizacijo oblike zapisa slike uporabite komponento `next/image` namesto `<img>`. [Več o tem](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Za samodejno odloženo nalaganje slik uporabite komponento `next/image` namesto `<img>`. [Več o tem](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Uporabite komponento `next/image` in možnost »priority« (prednost) nastavite na »true« za vnaprejšnje nalaganje slike LCP. [Več o tem](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "S komponento `next/script` odložite nalaganje manj pomembnih skriptov drugih ponudnikov. [Več o tem](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Uporabite komponento `next/image`, da bo velikost slik vedno ustrezno nastavljena. [Več o tem](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "Razmislite o nastavitvi elementa `PurgeCSS` v konfiguraciji `Next.js`, če želite iz datotek s slogi odstraniti neuporabljena pravila. [Več o tem](https://purgecss.com/guides/next.html)"}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Uporabite `Webpack Bundle Analyzer` za zaznavanje neuporabljene kode JavaScript. [Več o tem](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Razmislite o uporabi elementa `Next.js Analytics` za merjenje dejanskega delovanja aplikacije. [Več o tem](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurirajte predpomnjenje za nespremenljive elemente in strani `Server-side Rendered` (SSR). [Več o tem](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>ev kakovosti slike uporabite komponento `next/image` namesto `<img>`. [Več o tem](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "Uporabite komponento `next/image` za nastavitev ustreznega nabora `sizes`. [Več o tem](https://nextjs.org/docs/api-reference/next/image#sizes)"}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Omogočite stiskanje v strežniku Next.js. [Več o tem](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Uporabite komponento `nuxt/image` in nastavite `format=\"webp\"`. [Več o tem](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Uporabite komponento `nuxt/image` in nastavite `loading=\"lazy\"` za slike, ki niso na zaslonu. [Več o tem](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Uporabite komponento `nuxt/image` in določite `preload` za sliko LCP. [Več o tem](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Uporabite komponento `nuxt/image` in določite eksplicitni `width` in `height` (širino in dolžino). [Več o tem](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Uporabite komponento `nuxt/image` in nastavite ustrezno `quality` (kakovost). [Več o tem](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Uporabite komponento `nuxt/image` in nastavite ustrezne `sizes` (velikosti). [Več o tem](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "Za hitrejše nalaganje spletnih strani lahko [animirane slike GIF nadomestite z videoposnetki](https://web.dev/replace-gifs-with-videos/), razmislite pa tudi o uporabi sodobnih oblik datotek, kot je [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) ali [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder), da učinkovitost stiskanja izboljšate za več kot 30 % v primerjavi s trenutno najboljšim videokodekom VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Razmislite o uporabi [vtičnika](https://octobercms.com/plugins?search=image) ali stor<PERSON>, ki naložene slike samodejno pretvori v optimalne oblike. [Slike WebP brez izgub](https://developers.google.com/speed/webp) so 26 % manjše od slik PNG in 25–34 % manjše od primerljivih slik JPEG pri enakovrednem indeksu kakovosti SSIM. Še ena sodobna oblika zapisa slike, o kateri je vredno razmisliti, je [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Razmislite o namestitvi [vtičnika za odloženo nalaganje](https://octobercms.com/plugins?search=lazy), ki omogoča odlog nalaganja slik, ki niso na zaslonu, ali preidite na temo, ki ponuja to funkcijo. Razmislite tudi o uporabi [vtičnika AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Na voljo so številni vtičniki, ki pomagajo [uvrstiti nujna sredstva](https://octobercms.com/plugins?search=css). Ti vtičniki lahko pokvarijo delovanje drugih vtičnikov, zato jih morate temeljito preizkusiti."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Te<PERSON>, vti<PERSON><PERSON><PERSON> in strežniške specifikacije prispevajo k odzivnemu času strežnika. Razmislite o tem, da bi poiskali bolj optimizirano temo, skrbno izbrali optimizacijski vtičnik in/ali nadgradili strežnik. Sistem za upravljanje vsebine October razvijalcem omogoča tudi uporabo elementa [`Queues`](https://octobercms.com/docs/services/queues), ki omogoča odlog obdelave zamudnega opravila, kot je pošiljanje e-pošte. To znatno pospeši spletne zahteve."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Razmislite o tem, da bi na seznamih objav prikazali izvlečke (npr. z gumbom `show more`), <PERSON><PERSON><PERSON><PERSON><PERSON> število objav, prikazanih na posamezni spletni strani, da<PERSON><PERSON>š<PERSON> objave razdelili na več spletnih strani ali uporabili vtičnik za odloženo nalaganje komentarjev."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=css), ki lahko s spojitvijo, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem slogov pospešijo spletno mesto. Razvoj je mogoče pospešiti z uporabo postopka gradnje, ki to pomanjševanje izvede vnaprej."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=javascript), ki lahko s spojitvijo, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem skriptov pospešijo spletno mesto. Razvoj je mogoče pospešiti z uporabo postopka gradnje, ki to pomanjševanje izvede vnaprej."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Razmislite o pregledu [v<PERSON><PERSON><PERSON>](https://octobercms.com/plugins), ki na spletnem mestu nalagajo neuporabljen CSS. Če želite ugotoviti, kateri vtičniki dodajo nepotrebni CSS, z orodji za razvijalce v Chromu izvedite [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezno temo/vtičnik lahko ugotovite na podlagi URL-ja datoteke s slogi. Bodite pozorni na vtičnike z več datotekami s slogi, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Vtičnik naj doda datoteko s slogi samo, če je dejansko uporabljena na spletni strani."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Razmislite o pregledu [v<PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=javascript), ki na spletni strani nalagajo neuporabljen JavaScript. Če želite ugotoviti, kateri vtičniki dodajo nepotrebni JavaScript, z orodji za razvijalce v Chromu izvedite [preizkus pokritosti kode](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage). Ustrezno temo/vtičnik lahko ugotovite na podlagi URL-ja skripta. Bodite pozorni na vtičnike z več skripti, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Vtičnik naj doda skript samo, če je dejansko uporabljen na spletni strani."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "Preberite več o [preprečevanju nepotrebnih omrežnih zahtev s predpomnilnikom HTTP](https://web.dev/http-cache/#caching-checklist). Na voljo je več [v<PERSON><PERSON><PERSON>](https://octobercms.com/plugins?search=Caching), s kater<PERSON>i je mogoče pospešiti predpomnjenje."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Razmislite o uporabi [vtičnika za optimizacijo slik](https://octobercms.com/plugins?search=image), ki s<PERSON> s<PERSON>, a ohrani kakovost."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Slike naložite neposredno v program za upravljanje predstavnosti in tako zagotovite, da so na voljo potrebne velikosti slik. Razmislite o uporabi [filtra za spreminjanje velikosti](https://octobercms.com/docs/markup/filter-resize) ali [vtičnika za spreminjanje velikosti slik](https://octobercms.com/plugins?search=image), da zagotovite uporabo optimalnih velikosti slik."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Omogočite stiskanje besedila v konfiguraciji spletnega strežnika."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Razmislite o uporabi knjižnice prikaza v oknu, kot je `react-window`, da zman<PERSON><PERSON><PERSON> število ustvarjenih vozlišč <PERSON>, če na strani upodabljate več ponovljenih elementov. [Več o tem](https://web.dev/virtualize-long-lists-react-window/). Poleg tega zmanjšajte število nepotrebnih vnovičnih upodobitev z možnostmi [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) ali [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) in [preskočite učinke](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects), dokler se ne spremenijo samo določene odvisnosti, če uporabljate rutino `Effect` za izboljšanje učinkovitosti delovanja."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Če uporabljate React Router, karseda omejite uporabo komponente `<Redirect>` za[krma<PERSON><PERSON><PERSON><PERSON> po poteh](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Če na strežniški strani upodabljate morebitne komponente React, razmislite o uporabi elementa `renderToPipeableStream()` ali `renderToStaticNodeStream()`, da bi odjemalcu dovolili prejemanje in hidriranje različnih delov oznak, namesto vseh hkrati. [Preberite več o tem](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Če vaš sistem gradnje samodejno pomanjša datoteke CSS, se prepričajte, da uvajate gradnjo aplikacije za splošno razpoložljivost. To lahko preverite z razširitvijo React Developer Tools. [Več o tem](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Če vaš sistem gradnje samodejno pomanj<PERSON> datoteke JS, se prep<PERSON><PERSON>te, da uvajate gradnjo aplikacije za splošno razpoložljivost. To lahko preverite z razširitvijo React Developer Tools. [Več o tem](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Če ne upodabljate na strani strežnika, [razdelite JavaScriptne svežnje](https://web.dev/code-splitting-suspense/) z elementom `React.lazy()`. Kodo sicer razdelite s knjižnico drugega ponudnika, kot je [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Uporabite React DevTools Profiler, ki uporablja API za Profiler API, za merjenje učinkovitosti delovanja upodabljanja komponent. [Več o tem](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Razmislite o tem, da bi GIF naložili v storitev, prek katere bo na voljo za vdelavo kot videoposnetek v obliki HTML5."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Razmislite o uporabi vtičnika [Performance Lab](https://wordpress.org/plugins/performance-lab/) za samodejno pretvorbo naloženih slik JPEG v WebP, kjer koli je to podprto."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Namestite [vtičnik za postopno nalaganje za WordPress](https://wordpress.org/plugins/search/lazy+load/) ki omogoča odlog nalaganja slik, ki niso na zaslonu, ali preidite na temo, ki ponuja to funkcijo. Razmislite tudi o uporabi [vtičnika AMP](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Na voljo je več vtičnikov za WordPress, ki vam lahko pomagajo [uvrstiti nujna sredstva](https://wordpress.org/plugins/search/critical+css/) ali [odložiti manj pomembna sredstva](https://wordpress.org/plugins/search/defer+css+javascript/). <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, da lah<PERSON> optimizacije, ki jih ponujajo ti vtičniki, pokvarijo delovanje funkcij vaših tem ali vtič<PERSON>, zato boste verjetno morali spremeniti kodo."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Teme, vti<PERSON><PERSON><PERSON> in strežniške specifikacije prispevajo k odzivnemu času strežnika. Razmislite o tem, da bi poiskali bolj optimizirano temo, skrbno izbrali optimizacijski vtičnik in/ali nadgradili strežnik."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Razmislite o tem, da bi na seznamih objav prikazali izvlečke (npr. z oznako »more«), zman<PERSON><PERSON><PERSON> število objav, prikazanih na posamezni strani, dal<PERSON>še objave razdelili na več strani ali uporabili vtičnik za odloženo nalaganje komentarjev."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON> za WordPress](https://wordpress.org/plugins/search/minify+css/) ki lahko s sestavljanjem, pomanjševanjem in stiskanjem slogov pospešijo vaše spletno mesto. Po možnosti uporabite tudi postopek gradnje, ki to pomanjševanje izvede vnaprej."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "Na voljo je več [v<PERSON><PERSON><PERSON> za WordPress](https://wordpress.org/plugins/search/minify+javascript/), ki lahko s sestavl<PERSON>, poman<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> in stiskanjem slogov pospešijo vaše spletno mesto. Po možnosti uporabite tudi postopek gradnje, ki to pomanjševanje izvede vnaprej."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [vtičnikov za WordPress](https://wordpress.org/plugins/), ki na vaši strani nalagajo neuporabljen CSS. Če želite ugotoviti, kateri vtičniki dodajo zunanji CSS, poskušajte z orodji Chrome DevTools izvesti [pokritost kode](https://developer.chrome.com/docs/devtools/coverage/). Ustrezen vtičnik/temo lahko ugotovite na podlagi URL-ja datoteke s slogi. Bodite pozorni na vtičnike, ki imajo na seznamu mnogo datotek s slogi, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Datoteka s slogi naj bo v čakalni vrsti vtičnika samo, če je dejansko uporabljena na strani."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Razmislite o tem, da bi zmanjšali ali spremenili število [vtičnikov za WordPress](https://wordpress.org/plugins/), ki na vaši strani nalagajo neuporabljen JavaScript. Če želite ugotoviti, kateri vtičniki dodajo zunanji JS, poskušajte z orodji Chrome DevTools izvesti [pokritost kode](https://developer.chrome.com/docs/devtools/coverage/). Ustrezen vtičnik/temo lahko ugotovite na podlagi URL-ja skripta. Bodite pozorni na vtičnike, ki imajo na seznamu mnogo skriptov, ki imajo v pokritosti kode veliko rdeče obarvanega območja. Skript naj bo v čakalni vrsti vtičnika samo, če je dejansko uporabljen na strani."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "Preberite o [predpomnjenju brskalnika v WordPressu](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Razmislite o tem, da bi uporabili [vtičnik za optimizacijo slik za WordPress](https://wordpress.org/plugins/search/optimize+images/), ki slike s<PERSON>, a ohrani kakovost."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Slike naložite neposredno prek [predstavnostne knjižnice](https://wordpress.org/support/article/media-library-screen/) in tako zagotovite, da so na voljo potrebne velikosti slik. Nato jih vstavite iz predstavnostne knjižnice ali uporabite slikovni pripomoček, da zagotovite uporabo optimalnih velikosti slik (vključno s tistimi za odzivne prekinitvene točke). Izogibajte se slikam `Full Size`, razen če so mere primerne za njihovo uporabo. [Več o tem](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Stiskanje besedila lahko omogočite v konfiguraciji spletnega strežnika."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Če želite slike pretvoriti v obliko WebP, na zavihku za optimizacijo slik v vtičniku »WP Rocket« omogočite možnost »Imagify« (Optimiziranje slike)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "V vtičniku »WP Rocket« omogočite [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images), če želite ukrepati glede tega priporočila. Ta funkcija zakasni nalaganje slik, dokler se obiskovalec ne pomakne po strani navzdol in jih mora videti."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "V vtičniku »WP Rocket« omogočite možnosti [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranitev neuporabljenega CSS-ja) in [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) (Odloženo nalaganje JavaScripta). Ti funkciji bosta optimizirali datoteke CSS in JavaScript, tako da ne bodo blokirale upodabljanja strani."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "V vtičniku »WP Rocket« omogočite možnost [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) (Pomanjšanje datotek CSS), če želite odpraviti to težavo. Vsi prostori in komentarji v datotekah CSS spletnega mesta bodo odstranjeni zaradi zmanjšanja velikosti datotek in zagotavljanja njihovega hitrejšega prenosa."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "V vtičniku »WP Rocket« omogočite možnost [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) (Pomanjšanje datotek JavaScript), če želite odpraviti to težavo. Prazni prostori in komentarji v datotekah JavaScript bodo odstranjeni zaradi zmanjšanja velikosti datotek in zagotavljanja njihovega hitrejšega prenosa."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Če želite odpraviti to težavo, v vtičniku »WP Rocket« omogočite možnost [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranitev neuporabljenega CSS-ja). Zmanjša velikost strani, saj odstrani vse CSS-je in datoteke s slogi, ki se ne uporabljajo, ter za vsako stran ohrani samo CSS, ki se uporablja."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "V vtičniku »WP Rocket« omogočite možnost [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) (Odlog izvajanja JavaScripta), če želi<PERSON> odpraviti to težavo. Nalaganje strani bo izboljšano zaradi odloga izvajanja skriptov do interakcije uporabnika. Če vaše spletno mesto vsebuje elemente iframe, lahko uporabite možnosti vtičnika WP Rocket [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) (LazyLoad za elemente iframe in videoposnetke) in [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image) (Nadomestitev elementa iframe v YouTubu s sliko za predogled)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Če želite stisniti slike, na zavihku za optimizacijo slik v vtičniku »WP Rocket« omogočite možnost »Imagify« (Optimiziranje slike) in izvedite optimiziranje v velikem obsegu."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Uporabite možnost [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) (Vnaprejšnje pridobivanje zahtev DNS) v vtičniku »WP Rocket«, če želite dodati atribut »dns-prefetch« in povečati hitrost povezave z zunanjimi domenami. Vtičnik »WP Rocket« tudi samodejno doda atribut »preconnect« (vnaprejšnja vzpostavitev povezave) [domeni za Google Fonts](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) in vsem zapisom CNAME, dodanim s funkcijo [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn) (Omogočanje omrežja za prenos vsebine)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Če želite odpraviti to težavo glede pisav, v vtičniku »WP Rocket« omogočite možnost [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) (Odstranitev neuporabljenega CSS-ja). Nujne pisave spletnega mesta bodo vnaprej naložene prednostno."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Oglejte si kalkulator."}, "report/renderer/report-utils.js | collapseView": {"message": "<PERSON><PERSON><PERSON> pogled"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "Začetno krmarjenje"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Največja zakasnitev kritične poti:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Preklop temne teme"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Tiskanje razširjenega poročila"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Tiskanje povzetka"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "S<PERSON>ni kot Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Shrani kot HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Shrani kot JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Odpri v pregledovalniku"}, "report/renderer/report-utils.js | errorLabel": {"message": "Napaka"}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Napaka sporočila: ni podatkov o pregledu"}, "report/renderer/report-utils.js | expandView": {"message": "<PERSON><PERSON><PERSON><PERSON> pogled"}, "report/renderer/report-utils.js | footerIssue": {"message": "Prijavi <PERSON>"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON>k<PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Laboratorijski podatki"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) trenutne strani v emuliranem mobilnem omrežju. Vrednosti so ocenjene in lahko odstopajo."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Dodatni elementi za ročno preverjanje"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "Se ne uporablja"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Priložnost"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Ocenjeni p<PERSON>"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "Začetno nalaganje st rani"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Zaviranje po meri"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "<PERSON><PERSON><PERSON> na<PERSON>z<PERSON>"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Različica knjižnice Axe"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Neomejeno delovanje CPE-ja/moči pomnilnika"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "Zaviranje CPE-ja"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Omejevanje omrežja"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Emulacija zaslona"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Uporabnikov posrednik (omrežje)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Nalaganje ene strani"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Ti podatki za razliko od podatkov iz polja, ki povzemajo več sej, izvirajo iz nalaganja ene strani."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Počasno zaviranje pri povezavi 4G"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Neznano"}, "report/renderer/report-utils.js | show": {"message": "Pokaži"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Prikaz revizij, pomembnih za to:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Prikaži sredstva drugih ponudnikov"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Zagotavlja okolje"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Na to izvedbo storitve Lighthouse so vp<PERSON>le težave:"}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Vrednosti so ocenjene in lahko odstopajo. [Rezultat uspešnosti se izračuna](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) neposredno iz teh meritev."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "<PERSON><PERSON> izvirne sledi"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Ogled sledi"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "<PERSON><PERSON> dreves<PERSON>ga zemljevida"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Pregledi so bili uspešno opravljeni, vendar z opozorili"}, "report/renderer/report-utils.js | warningHeader": {"message": "Opozorila: "}, "treemap/app/src/util.js | allLabel": {"message": "Vse"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON><PERSON> sk<PERSON>ti"}, "treemap/app/src/util.js | coverageColumnName": {"message": "Pokritost"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Podvojeni moduli"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "<PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | tableColumnName": {"message": "Ime"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Preklop tabele"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Neuporab<PERSON><PERSON><PERSON> b<PERSON>"}}