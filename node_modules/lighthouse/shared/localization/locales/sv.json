{"core/audits/accessibility/accesskeys.js | description": {"message": "Med åtkomsttangenter kan användaren snabbt flytta fokus till en viss del av sidan. Ingen åtkomsttangent får användas flera gånger om navigeringen ska fungera ordentligt. [<PERSON><PERSON><PERSON> mer om åtkomsttangenter](https://dequeuniversity.com/rules/axe/4.6/accesskeys)."}, "core/audits/accessibility/accesskeys.js | failureTitle": {"message": "Alla värden på `[accesskey]` är inte unika"}, "core/audits/accessibility/accesskeys.js | title": {"message": "`[accesskey]` värden är unika"}, "core/audits/accessibility/aria-allowed-attr.js | description": {"message": "<PERSON><PERSON><PERSON>-`role` har stöd för ett visst antal attribut av typen `aria-*`. Om dessa inte överensstämmer blir attributen av typen `aria-*` ogiltiga. [<PERSON><PERSON><PERSON> mer om hur du matchar ARIA-attribut med deras roller](https://dequeuniversity.com/rules/axe/4.6/aria-allowed-attr)."}, "core/audits/accessibility/aria-allowed-attr.js | failureTitle": {"message": "Alla attribut av typen `[aria-*]` stämmer inte med elementets roll"}, "core/audits/accessibility/aria-allowed-attr.js | title": {"message": "Attributen av typen `[aria-*]` stämmer med elementets roll"}, "core/audits/accessibility/aria-command-name.js | description": {"message": "Utan en maskinläsbar etikett läses element upp med en generell etikett av skärmläsarna. Det gör dem oanvändbara för personer som behöver använda en skärmläsare. [Läs mer om hur du gör kommandoelement mer tillgängliga](https://dequeuniversity.com/rules/axe/4.6/aria-command-name)."}, "core/audits/accessibility/aria-command-name.js | failureTitle": {"message": "<PERSON><PERSON> för `button`-, `link`- och `menuitem`-elementen är inte igenkännliga."}, "core/audits/accessibility/aria-command-name.js | title": {"message": "<PERSON><PERSON> för `button`-, `link`- och `menuitem`-elementen är igenkännliga"}, "core/audits/accessibility/aria-hidden-body.js | description": {"message": "Hjälpmedelstekniker som skärmläsare fungerar inte ordentligt när `aria-hidden=\"true\"` har angetts för dokumentet `<body>`. [<PERSON><PERSON><PERSON> om hur `aria-hidden` påverkar dokumenttexten](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-body)."}, "core/audits/accessibility/aria-hidden-body.js | failureTitle": {"message": "Alla `[aria-hidden=\"true\"]` finns i dokumentet `<body>`"}, "core/audits/accessibility/aria-hidden-body.js | title": {"message": "Alla `[aria-hidden=\"true\"]` finns inte i dokumentet `<body>`"}, "core/audits/accessibility/aria-hidden-focus.js | description": {"message": "Fokuserbara underordnade element i `[aria-hidden=\"true\"]`-element för<PERSON>drar att de interaktiva elementen blir tillgängliga för användare av hjälpmedelstekniker som skärmläsare. [<PERSON><PERSON><PERSON> mer om hur `aria-hidden` påverkar fokuserbara element](https://dequeuniversity.com/rules/axe/4.6/aria-hidden-focus)."}, "core/audits/accessibility/aria-hidden-focus.js | failureTitle": {"message": "Alla `[aria-hidden=\"true\"]`-element innehåller fokuserbara underordnade element"}, "core/audits/accessibility/aria-hidden-focus.js | title": {"message": "Alla `[aria-hidden=\"true\"]`-element har inte fokuserbara underordnade element"}, "core/audits/accessibility/aria-input-field-name.js | description": {"message": "Utan en maskinläsbar etikett läses inmatningsfält upp med en generell etikett av skärmläsarna. Det gör dem oanvändbara för personer som behöver använda en skärmläsare. [Läs mer om etiketter för inmatningsfält](https://dequeuniversity.com/rules/axe/4.6/aria-input-field-name)."}, "core/audits/accessibility/aria-input-field-name.js | failureTitle": {"message": "Namnen för inmatningsfälten för ARIA är inte tillgängliga"}, "core/audits/accessibility/aria-input-field-name.js | title": {"message": "Namnen för inmatningsfälten för ARIA är tillgängliga"}, "core/audits/accessibility/aria-meter-name.js | description": {"message": "Utan en maskinläsbar etikett läses mätarelement upp med en generell etikett av skärmläsare. Detta gör elementet oanvändbart för personer som behöver använda en skärmläsare. [Läs mer om hur du sätter etikett på `meter` element](https://dequeuniversity.com/rules/axe/4.6/aria-meter-name)."}, "core/audits/accessibility/aria-meter-name.js | failureTitle": {"message": "Namnen för `meter`-elementen för ARIA är inte igenkännliga."}, "core/audits/accessibility/aria-meter-name.js | title": {"message": "Namnen för `meter`-elementen för ARIA är igenkännliga"}, "core/audits/accessibility/aria-progressbar-name.js | description": {"message": "Utan en maskinläsbar etikett läses `progressbar`-element upp med en generell etikett av skärmläsarna. Det gör dem oanvändbara för personer som behöver använda en skärmläsare. [Läs mer om hur du sätter etikett på `progressbar`-element](https://dequeuniversity.com/rules/axe/4.6/aria-progressbar-name)."}, "core/audits/accessibility/aria-progressbar-name.js | failureTitle": {"message": "Namnen för `progressbar`-elementen för ARIA är inte igenkännliga."}, "core/audits/accessibility/aria-progressbar-name.js | title": {"message": "Namnen för `progressbar`-elementen för ARIA är igenkännliga"}, "core/audits/accessibility/aria-required-attr.js | description": {"message": "<PERSON><PERSON> ARIA-roller har obligatoriska attribut som beskriver elementets tillstånd för skärmläsare. [<PERSON><PERSON><PERSON> mer om roller och obligatoriska attribut](https://dequeuniversity.com/rules/axe/4.6/aria-required-attr)."}, "core/audits/accessibility/aria-required-attr.js | failureTitle": {"message": "Vissa element med attributet `[role]` har inte alla obligatoriska attribut av typen `[aria-*]`"}, "core/audits/accessibility/aria-required-attr.js | title": {"message": "Alla element med `[role]`-attribut har alla obligatoriska attribut av typen `[aria-*]`"}, "core/audits/accessibility/aria-required-children.js | description": {"message": "Vissa överordnade element med ARIA-attributet role måste ha ett bestämt underordnat element med role för att hjälpmedlen ska fungera som avsett. [<PERSON><PERSON><PERSON> mer om attributet role och obligatoriska underordnade element](https://dequeuniversity.com/rules/axe/4.6/aria-required-children)."}, "core/audits/accessibility/aria-required-children.js | failureTitle": {"message": "Några eller alla obligatoriska underordnade element med `[role]` saknas för element med ARIA-rollen `[role]`."}, "core/audits/accessibility/aria-required-children.js | title": {"message": "Alla obligatoriska underordnade element med `[role]` används för element med ARIA-rollen `[role]`."}, "core/audits/accessibility/aria-required-parent.js | description": {"message": "Vissa underordnade element med ARIA-attributet role måste ha ett bestämt överordnat element med role för att hjälpmedlen ska fungera som avsett. [<PERSON><PERSON><PERSON> mer om ARIA-attributet role och obligatoriskt överordnat element](https://dequeuniversity.com/rules/axe/4.6/aria-required-parent)."}, "core/audits/accessibility/aria-required-parent.js | failureTitle": {"message": "Det finns element med `[role]`-attribut utan ett obligatoriskt överordnat element"}, "core/audits/accessibility/aria-required-parent.js | title": {"message": "Rätt överordnat element används för alla element med `[role]`-attribut"}, "core/audits/accessibility/aria-roles.js | description": {"message": "Alla ARIA-roller måste ha giltiga värden om de ska fungera som avsett med hjälpmedlen. [<PERSON><PERSON><PERSON> mer om giltiga ARIA-roller](https://dequeuniversity.com/rules/axe/4.6/aria-roles)."}, "core/audits/accessibility/aria-roles.js | failureTitle": {"message": "Vissa `[role]`-värden är inte giltiga"}, "core/audits/accessibility/aria-roles.js | title": {"message": "<PERSON>a `[role]`-värden är giltiga"}, "core/audits/accessibility/aria-toggle-field-name.js | description": {"message": "Utan en maskinläsbar etikett läses av/på-fält upp med en generell etikett av skärmläsarna. Det gör dem oanvändbara för personer som behöver använda en skärmläsare. [L<PERSON>s mer om att aktivera och inaktivera fält](https://dequeuniversity.com/rules/axe/4.6/aria-toggle-field-name)."}, "core/audits/accessibility/aria-toggle-field-name.js | failureTitle": {"message": "Namnen för på/av-fälten för ARIA är inte tillgängliga"}, "core/audits/accessibility/aria-toggle-field-name.js | title": {"message": "Namnen för på/av-fälten för ARIA är tillgängliga"}, "core/audits/accessibility/aria-tooltip-name.js | description": {"message": "Utan en maskinläsbar etikett läses beskrivningselement upp med en generell etikett av skärmläsare. Detta gör elementet oanvändbart för personer som behöver använda en skärmläsare. [L<PERSON>s mer om hur du sätter etikett på `tooltip` element](https://dequeuniversity.com/rules/axe/4.6/aria-tooltip-name)."}, "core/audits/accessibility/aria-tooltip-name.js | failureTitle": {"message": "Namnen för `tooltip`-elementen för ARIA är inte igenkännliga."}, "core/audits/accessibility/aria-tooltip-name.js | title": {"message": "Namnen för `tooltip`-elementen för ARIA är igenkännliga"}, "core/audits/accessibility/aria-treeitem-name.js | description": {"message": "Utan en maskinläs<PERSON> etikett läses `treeitem`-element upp med en generell etikett av skärmläsarna. Det gör dem oanvändbara för personer som behöver använda en skärmläsare. [Läs mer om att sätta etiketter på `treeitem`-element](https://dequeuniversity.com/rules/axe/4.6/aria-treeitem-name)."}, "core/audits/accessibility/aria-treeitem-name.js | failureTitle": {"message": "Namnen för `treeitem`-elementen för ARIA är inte igenkännliga."}, "core/audits/accessibility/aria-treeitem-name.js | title": {"message": "Namnen för `treeitem`-elementen för ARIA är igenkännliga"}, "core/audits/accessibility/aria-valid-attr-value.js | description": {"message": "Skärmlä<PERSON><PERSON> och andra h<PERSON>l kan inte tolka ARIA-attribut med ogiltiga värden. [<PERSON><PERSON><PERSON> mer om giltiga värden för ARIA-attribut](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr-value)."}, "core/audits/accessibility/aria-valid-attr-value.js | failureTitle": {"message": "Alla attribut av typen `[aria-*]` har inte ett giltigt värde"}, "core/audits/accessibility/aria-valid-attr-value.js | title": {"message": "Alla attribut av typen `[aria-*]` har giltiga värden"}, "core/audits/accessibility/aria-valid-attr.js | description": {"message": "Skärmläsare och annan hjälpmedelsteknik kan inte tolka ARIA-attribut med ogiltiga namn. [<PERSON><PERSON><PERSON> mer om giltiga ARIA-attribut](https://dequeuniversity.com/rules/axe/4.6/aria-valid-attr)."}, "core/audits/accessibility/aria-valid-attr.js | failureTitle": {"message": "Vissa attribut av typen `[aria-*]` är ogiltiga eller felstavade"}, "core/audits/accessibility/aria-valid-attr.js | title": {"message": "Alla attribut av typen `[aria-*]` är giltiga och rättstavade"}, "core/audits/accessibility/axe-audit.js | failingElementsHeader": {"message": "Element med fel"}, "core/audits/accessibility/button-name.js | description": {"message": "Utan en maskinläsbar etikett läses knappen upp som ”knapp” av skärmläsarna. Det gör knappen oanvändbar för den som behöver använda en skärmläsare. [Läs om hur du gör knappar mer tillgängliga](https://dequeuniversity.com/rules/axe/4.6/button-name)."}, "core/audits/accessibility/button-name.js | failureTitle": {"message": "<PERSON><PERSON> knappar har inte namn som hjälpmedlen kan använda"}, "core/audits/accessibility/button-name.js | title": {"message": "Alla knappar har namn som hjälpmedlen kan använda"}, "core/audits/accessibility/bypass.js | description": {"message": "Om du lägger till ett sätt att hoppa över innehåll som upprepas går det att navigera effektivare på sidan för den som använder tangentbordet. [L<PERSON><PERSON> mer om att hoppa över innehållsblock](https://dequeuniversity.com/rules/axe/4.6/bypass)."}, "core/audits/accessibility/bypass.js | failureTitle": {"message": "<PERSON><PERSON> saknar rubrik, överhoppningslänk eller landmärkesområde"}, "core/audits/accessibility/bypass.js | title": {"message": "<PERSON><PERSON> har en rubrik, en överhoppningslänk eller ett landmärkesområde"}, "core/audits/accessibility/color-contrast.js | description": {"message": "Text med låg kontrast blir svårläst eller oläslig för många användare. [L<PERSON>s mer om hur du tillhandahåller tillräcklig färgkontrast](https://dequeuniversity.com/rules/axe/4.6/color-contrast)."}, "core/audits/accessibility/color-contrast.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> mellan bakgrundsfärg och förgrundsfärg är inte tillr<PERSON>ck<PERSON>gt stor."}, "core/audits/accessibility/color-contrast.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> mellan bakgrundsfärg och förgrundsfärg är tillr<PERSON><PERSON><PERSON><PERSON> stor"}, "core/audits/accessibility/definition-list.js | description": {"message": "Om en definitionslista inte har märkts upp korrekt kan den läsas upp på ett missvisande eller felaktigt sätt av skärmläsare. [Läs mer om hur du strukturerar definitionslistor på rätt sätt](https://dequeuniversity.com/rules/axe/4.6/definition-list)."}, "core/audits/accessibility/definition-list.js | failureTitle": {"message": "Det finns `<dl>`-element som inte enbart består av `<dt>`- och `<dd>`-grupper, `<script>`-, `<template>`- eller `<div>`-element."}, "core/audits/accessibility/definition-list.js | title": {"message": "Alla `<dl>`-element best<PERSON>r enbart av `<dt>`- och `<dd>`-grupper i rätt ordning, `<script>`-,`<template>`- eller `<div>`-element."}, "core/audits/accessibility/dlitem.js | description": {"message": "Alla poster i definitionslistor (`<dt>` och `<dd>`) måste ha överordnade `<dl>`-element så att de kan presenteras korrekt av skärmläsare. [L<PERSON><PERSON> mer om hur du strukturerar definitionslistor på rätt sätt](https://dequeuniversity.com/rules/axe/4.6/dlitem)."}, "core/audits/accessibility/dlitem.js | failureTitle": {"message": "Vissa poster i definitionslistor har inte bäddats in i `<dl>`-element"}, "core/audits/accessibility/dlitem.js | title": {"message": "Alla poster i <PERSON>listor har bäddats in i `<dl>`-element"}, "core/audits/accessibility/document-title.js | description": {"message": "Titeln ger den som använder skärmläsare en uppfattning om vad sidan handlar om. Dessutom fyller sidtiteln en viktig funktion i sökmotorer när användarna väljer ut sidor som verkar vara relevanta för sökningen. [L<PERSON>s mer om dokumenttitlar](https://dequeuniversity.com/rules/axe/4.6/document-title)."}, "core/audits/accessibility/document-title.js | failureTitle": {"message": "Dokumentet har inget `<title>`-element"}, "core/audits/accessibility/document-title.js | title": {"message": "Dokumentet har ett `<title>`-element"}, "core/audits/accessibility/duplicate-id-active.js | description": {"message": "Alla fokuserbara element måste ha unika `id` så att de kan identifieras av hjälpmedelsteknik. [<PERSON><PERSON>s om hur du åtgärdar dubbletter av `id`](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-active)."}, "core/audits/accessibility/duplicate-id-active.js | failureTitle": {"message": "Alla `[id]`-attribut för aktiva fokuserbara element är inte unika"}, "core/audits/accessibility/duplicate-id-active.js | title": {"message": "Alla `[id]`-attribut för aktiva, fokuserbara element är unika"}, "core/audits/accessibility/duplicate-id-aria.js | description": {"message": "Alla ARIA-id:n måste ha unika värden. Hjälpmedelstekniken skulle annars hoppa över dubblettförekomsterna av ett värde. [<PERSON><PERSON><PERSON> mer om hur du åtgärdar dubbletter av ARIA-id:n](https://dequeuniversity.com/rules/axe/4.6/duplicate-id-aria)."}, "core/audits/accessibility/duplicate-id-aria.js | failureTitle": {"message": "Alla ARIA-id:n är inte unika"}, "core/audits/accessibility/duplicate-id-aria.js | title": {"message": "Alla ARIA-id:n är unika"}, "core/audits/accessibility/form-field-multiple-labels.js | description": {"message": "Formulärfält med flera etiketter kan läsas upp på ett förvirrande sätt av hjälpmedelstekniker, till exempel skärmläsare som använder antingen den första, sista eller alla etiketterna. [L<PERSON>s om hur du använder formuläretiketter](https://dequeuniversity.com/rules/axe/4.6/form-field-multiple-labels)."}, "core/audits/accessibility/form-field-multiple-labels.js | failureTitle": {"message": "Det finns formulärfält med flera etiketter"}, "core/audits/accessibility/form-field-multiple-labels.js | title": {"message": "Det finns inga formulärfält med flera etiketter"}, "core/audits/accessibility/frame-title.js | description": {"message": "Med en skärmläsare behövs namn på ramarna som beskriver vad ramen innehåller. [<PERSON><PERSON><PERSON> mer om ramnamn](https://dequeuniversity.com/rules/axe/4.6/frame-title)."}, "core/audits/accessibility/frame-title.js | failureTitle": {"message": "Vissa `<frame>`- eller `<iframe>`-element saknar titel"}, "core/audits/accessibility/frame-title.js | title": {"message": "Alla `<frame>`- eller `<iframe>`-element har en titel"}, "core/audits/accessibility/heading-order.js | description": {"message": "Med hjälp av ordentligt ordnade rubriker som inte hoppar över nivåer förmedlas den semantiska strukturen på sidan. Det gör det lättare för användare av hjälpmedelstekniker att navigera och hänga med. [<PERSON><PERSON><PERSON> mer om rubrikordning](https://dequeuniversity.com/rules/axe/4.6/heading-order)."}, "core/audits/accessibility/heading-order.js | failureTitle": {"message": "Rubrikelementen har inte ordnats i följd i fallande ordning"}, "core/audits/accessibility/heading-order.js | title": {"message": "Rubrikelementen visas i följd i fallande ordning"}, "core/audits/accessibility/html-has-lang.js | description": {"message": "Om inget `lang`-attribut har angetts för en sida används skärmläsarens standardspråk, det vill säga det språk som användaren valde när skärmläsaren konfigurerades. Om sidan inte är på det språket kanske texten inte läses upp korrekt. [<PERSON><PERSON><PERSON> mer om attributet `lang`](https://dequeuniversity.com/rules/axe/4.6/html-has-lang)."}, "core/audits/accessibility/html-has-lang.js | failureTitle": {"message": "`<html>`-elementet har inget `[lang]`-attribut"}, "core/audits/accessibility/html-has-lang.js | title": {"message": "`<html>`-elementet har ett `[lang]`-attribut"}, "core/audits/accessibility/html-lang-valid.js | description": {"message": "Om du anger ett giltigt [spr<PERSON><PERSON> enligt BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) uttalas texten korrekt av en skärmläsare. [Läs om hur du använder attributet `lang`](https://dequeuniversity.com/rules/axe/4.6/html-lang-valid)."}, "core/audits/accessibility/html-lang-valid.js | failureTitle": {"message": "`<html>`-elementets `[lang]`-attribut har inte ett giltigt värde."}, "core/audits/accessibility/html-lang-valid.js | title": {"message": "`<html>`-elementets `[lang]`-attribut har ett giltigt värde"}, "core/audits/accessibility/image-alt.js | description": {"message": "Element med informativ funktion bör ha en kort, beskrivande alternativ text. Element som bara har estetisk funktion kan ignoreras genom att alt-attributet lämnas tomt. [L<PERSON>s mer om attributet `alt`](https://dequeuniversity.com/rules/axe/4.6/image-alt)."}, "core/audits/accessibility/image-alt.js | failureTitle": {"message": "Alla bildelement har inte `[alt]`-attribut"}, "core/audits/accessibility/image-alt.js | title": {"message": "Alla bildelement har `[alt]`-attribut"}, "core/audits/accessibility/input-image-alt.js | description": {"message": "Om du anger en alternativ text när en bild används som `<input>`-knap<PERSON> blir det lättare för användare med skärmläsare att förstå hur knappen används. [Läs om alternativ text för indata](https://dequeuniversity.com/rules/axe/4.6/input-image-alt)."}, "core/audits/accessibility/input-image-alt.js | failureTitle": {"message": "Vissa `<input type=\"image\">`-element saknar `[alt]`-text"}, "core/audits/accessibility/input-image-alt.js | title": {"message": "Alla `<input type=\"image\">`-element har `[alt]`-text"}, "core/audits/accessibility/label.js | description": {"message": "Etiketterna gör att de olika delarna av ett formulär kan presenteras korrekt för användare med skärmläsare eller annan hjälpmedelsteknik. [<PERSON><PERSON><PERSON> mer om etiketter för olika formulärdelar](https://dequeuniversity.com/rules/axe/4.6/label)."}, "core/audits/accessibility/label.js | failureTitle": {"message": "Vissa formulärelement har inte etiketter"}, "core/audits/accessibility/label.js | title": {"message": "Alla formulärelement har etiketter"}, "core/audits/accessibility/link-name.js | description": {"message": "Det blir enklare att navigera för den som använder en skärmläsare om alla länktexter (och alternativtexter för alla bilder som används som länkar) är igenkännliga, unika och fokuserbara. [<PERSON><PERSON><PERSON> mer om hur du gör länkar tillgängliga](https://dequeuniversity.com/rules/axe/4.6/link-name)."}, "core/audits/accessibility/link-name.js | failureTitle": {"message": "<PERSON>issa länkar har inte ett igenkännligt namn"}, "core/audits/accessibility/link-name.js | title": {"message": "Alla länkar har igenkännliga namn"}, "core/audits/accessibility/list.js | description": {"message": "Listor presenteras på ett särskilt sätt av skärmläsare. Med rätt liststruktur kan skärmläsarna ge rätt information. [<PERSON><PERSON>s mer om en korrekt liststruktur](https://dequeuniversity.com/rules/axe/4.6/list)."}, "core/audits/accessibility/list.js | failureTitle": {"message": "Listor innehåller inte enbart `<li>`-element och stödelement för skript (`<script>` och `<template>`)."}, "core/audits/accessibility/list.js | title": {"message": "Alla listor innehåller enbart `<li>`-element eller stödelement för skript (`<script>` och `<template>`)."}, "core/audits/accessibility/listitem.js | description": {"message": "Alla listposter (`<li>`) måste ha ett överordnat `<ul>`-, `<ol>`- eller `<menu>`-element för att kunna presenteras korrekt av skärmläsare. [Läs mer om en korrekt liststruktur](https://dequeuniversity.com/rules/axe/4.6/listitem)."}, "core/audits/accessibility/listitem.js | failureTitle": {"message": "Vissa listposter (`<li>`) saknar ett överordnat `<ul>`-, `<ol>`- eller `<menu>`-element."}, "core/audits/accessibility/listitem.js | title": {"message": "Alla listposter (`<li>`) har ett överordnat `<ul>`-, `<ol>`, eller `<menu>`-element"}, "core/audits/accessibility/meta-refresh.js | description": {"message": "Användarna förväntar sig inte att en sida ska uppdateras automatiskt, och när det händer flyttas fokus tillbaka till sidans bör<PERSON>. Det kan vara både frustrerande och förvirrande. [<PERSON><PERSON><PERSON> mer om metataggen för uppdatering](https://dequeuniversity.com/rules/axe/4.6/meta-refresh)."}, "core/audits/accessibility/meta-refresh.js | failureTitle": {"message": "`<meta http-equiv=\"refresh\">` används i dokumentet"}, "core/audits/accessibility/meta-refresh.js | title": {"message": "`<meta http-equiv=\"refresh\">` används inte i dokumentet"}, "core/audits/accessibility/meta-viewport.js | description": {"message": "Att inaktivera förstoring leder till problem för användare med nedsatt syn, som behöver skärmförstoring för att kunna se webbsidan ordentligt. [Läs mer om metataggen för visningsområdet](https://dequeuniversity.com/rules/axe/4.6/meta-viewport)."}, "core/audits/accessibility/meta-viewport.js | failureTitle": {"message": "`[user-scalable=\"no\"]` används i elementet `<meta name=\"viewport\">`, eller också är värdet på attributet `[maximum-scale]` mindre än 5."}, "core/audits/accessibility/meta-viewport.js | title": {"message": "`[user-scalable=\"no\"]` används inte i elementet `<meta name=\"viewport\">` och attributet `[maximum-scale]` är inte mindre än 5."}, "core/audits/accessibility/object-alt.js | description": {"message": "En skärmläsare kan inte tolka innehåll som inte är text. Om du lägger till alternativ text i `<object>`-elementen kan skärmläsarna förmedla ett meningsfullt innehåll till användaren. [Läs mer om alternativ text för `object`-element](https://dequeuniversity.com/rules/axe/4.6/object-alt)."}, "core/audits/accessibility/object-alt.js | failureTitle": {"message": "`<object>` element saknar alt-text"}, "core/audits/accessibility/object-alt.js | title": {"message": "`<object>` element har alt-text"}, "core/audits/accessibility/tabindex.js | description": {"message": "Med värden större än noll anges en explicit ordningsföljd för navigeringen. Även om detta inte är fel rent tekniskt leder det ofta till en frustrerande upplevelse för den som är beroende av tekniska hjälpmedel. [<PERSON><PERSON><PERSON> mer om attributet `tabindex`](https://dequeuniversity.com/rules/axe/4.6/tabindex)."}, "core/audits/accessibility/tabindex.js | failureTitle": {"message": "Det finns element med ett `[tabindex]`-värde som är större än 0"}, "core/audits/accessibility/tabindex.js | title": {"message": "Det finns inga element med ett `[tabindex]`-värde som är större än 0"}, "core/audits/accessibility/td-headers-attr.js | description": {"message": "Skärmläsare har funktioner som gör det enklare att navigera i tabeller. <PERSON>an fungerar bättre för den som använder skärmläsare om attributet `[headers]` i `<td>`-celler bara refererar till andra celler i samma tabell. [<PERSON><PERSON><PERSON> mer om attributet `headers`](https://dequeuniversity.com/rules/axe/4.6/td-headers-attr)."}, "core/audits/accessibility/td-headers-attr.js | failureTitle": {"message": "Det finns celler i ett `<table>`-element där attributet `[headers]` hänvisar till ett `id`-element som inte finns i samma tabell."}, "core/audits/accessibility/td-headers-attr.js | title": {"message": "Det finns celler i ett `<table>`-element där attributet `[headers]` hänvisar till celler i samma tabell."}, "core/audits/accessibility/th-has-data-cells.js | description": {"message": "Skärmläsare har funktioner som gör det enklare att navigera i tabeller. Det fungerar bättre för den som använder skärmläsare om det inte finns några tabellrubriker som hänger i luften utan att referera till några dataceller. [<PERSON><PERSON><PERSON> mer om tabellrubriker](https://dequeuniversity.com/rules/axe/4.6/th-has-data-cells)."}, "core/audits/accessibility/th-has-data-cells.js | failureTitle": {"message": "Vissa `<th>`-element och element med `[role=\"columnheader\"/\"rowheader\"]` står inte som rubrik för några dataceller."}, "core/audits/accessibility/th-has-data-cells.js | title": {"message": "Alla `<th>`-element och element med `[role=\"columnheader\"/\"rowheader\"]` står som rubriker för andra dataceller."}, "core/audits/accessibility/valid-lang.js | description": {"message": "Om du anger ett giltigt [spr<PERSON><PERSON> enligt BCP 47](https://www.w3.org/International/questions/qa-choosing-language-tags#question) för elementen uttalas texten korrekt av en skärmläsare. [Läs om hur du använder attributet `lang`](https://dequeuniversity.com/rules/axe/4.6/valid-lang)."}, "core/audits/accessibility/valid-lang.js | failureTitle": {"message": "Vissa `[lang]`-attribut har inte ett giltigt värde"}, "core/audits/accessibility/valid-lang.js | title": {"message": "Alla `[lang]`-attribut har ett giltigt värde"}, "core/audits/accessibility/video-caption.js | description": {"message": "Det blir lättare för döva och hörselskadade att ta del av en video som är textad. [Läs mer om videotextning](https://dequeuniversity.com/rules/axe/4.6/video-caption)."}, "core/audits/accessibility/video-caption.js | failureTitle": {"message": "Alla `<video>`-element har inte ett underordnat `<track>`-element med `[kind=\"captions\"]`."}, "core/audits/accessibility/video-caption.js | title": {"message": "Alla `<video>`-element innehåller ett `<track>`-element med `[kind=\"captions\"]`"}, "core/audits/autocomplete.js | columnCurrent": {"message": "Aktuellt värde"}, "core/audits/autocomplete.js | columnSuggestions": {"message": "Föreslagen token"}, "core/audits/autocomplete.js | description": {"message": "Med hjälp av `autocomplete` kan användare fylla i formulär snabbare. Vi rekommenderar att du aktiverar detta genom att ställa in ett giltigt värde för attributet `autocomplete`. [<PERSON><PERSON><PERSON> mer om `autocomplete` i formulär](https://developers.google.com/web/fundamentals/design-and-ux/input/forms#use_metadata_to_enable_auto-complete)"}, "core/audits/autocomplete.js | failureTitle": {"message": "`<input>`-element har inte rätt `autocomplete`-attribut"}, "core/audits/autocomplete.js | manualReview": {"message": "Kräver manuell granskning"}, "core/audits/autocomplete.js | reviewOrder": {"message": "Granska ordningen på token"}, "core/audits/autocomplete.js | title": {"message": "`autocomplete` används korrekt för `<input>`-element"}, "core/audits/autocomplete.js | warningInvalid": {"message": "Token för `autocomplete`: {token} är ogiltig i {snippet}"}, "core/audits/autocomplete.js | warningOrder": {"message": "Granska ordningen på token: {tokens} i {snippet}"}, "core/audits/bf-cache.js | actionableFailureType": {"message": "Åtgärder kan vidtas"}, "core/audits/bf-cache.js | description": {"message": "Många navigeringar går tillbaka till en tidigare sida eller framåt till nästa. Vilocacheminnet kan göra returnavigeringen snabbare. [<PERSON><PERSON><PERSON> mer om vilocacheminnet](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)"}, "core/audits/bf-cache.js | displayValue": {"message": "{itemCount,plural, =1{1 felorsak}other{# felorsaker}}"}, "core/audits/bf-cache.js | failureReasonColumn": {"message": "Felorsak"}, "core/audits/bf-cache.js | failureTitle": {"message": "<PERSON><PERSON> förhindrade återställning av vilocacheminnet"}, "core/audits/bf-cache.js | failureTypeColumn": {"message": "Feltyp"}, "core/audits/bf-cache.js | notActionableFailureType": {"message": "Ingen åt<PERSON>ärd kan vidtas"}, "core/audits/bf-cache.js | supportPendingFailureType": {"message": "Vilande webbläsarstöd"}, "core/audits/bf-cache.js | title": {"message": "<PERSON><PERSON> förhindrade inte återställning av vilocacheminnet"}, "core/audits/bootup-time.js | chromeExtensionsWarning": {"message": "Inläsningen av den här sidan påverkas negativt av tillägg i Chrome. Testa att granska sidan i inkognitoläge eller med en Chrome-profil utan tillägg."}, "core/audits/bootup-time.js | columnScriptEval": {"message": "Utvärdering av skript"}, "core/audits/bootup-time.js | columnScriptParse": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/bootup-time.js | columnTotal": {"message": "Processortid totalt"}, "core/audits/bootup-time.js | description": {"message": "Minska tiden det tar att tolka, kompilera och köra JS-kod. Det brukar hjälpa att minska storleken på JS-resurserna som skickas. [<PERSON><PERSON><PERSON> mer om hur du minskar körningstiden för JavaScript](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/)."}, "core/audits/bootup-time.js | failureTitle": {"message": "Minska körningstiden för JavaScript"}, "core/audits/bootup-time.js | title": {"message": "Körningstid för JavaScript"}, "core/audits/byte-efficiency/duplicated-javascript.js | description": {"message": "<PERSON> bort stora, duplicerade JavaScript-moduler f<PERSON><PERSON><PERSON> paket så att färre onödiga byte skickas via nätverket. "}, "core/audits/byte-efficiency/duplicated-javascript.js | title": {"message": "Ta bort dubblettmoduler i JavaScript-paket"}, "core/audits/byte-efficiency/efficient-animated-content.js | description": {"message": "<PERSON>ora giffar är inte ett effektivt sätt att visa animerat innehåll. I stället för giffar kan du använda videor i MPEG4-/WebM-format för animationer och PNG-/WebP-format för statiska bilder. Då minskar antalet byte som skickas via nätverket. [Läs mer om effektiva videoformat](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)"}, "core/audits/byte-efficiency/efficient-animated-content.js | title": {"message": "Använd videoformat för animationer"}, "core/audits/byte-efficiency/legacy-javascript.js | description": {"message": "Med polyfill-koder och transformeringar går det att använda nya JavaScript-funktioner i äldre webbläsare. Många av dem behövs dock inte för moderna webbläsare. Om du har JavaScript-paket kan du använda en modern strategi för skriptimplementering där registrering av funktioner med eller utan moduler används för att minska mängden kod som skickas till moderna webbläsare, samtidigt som stöd för äldre webbläsare finns kvar. [Läs mer om hur du använder modern JavaScript](https://web.dev/publish-modern-javascript/)"}, "core/audits/byte-efficiency/legacy-javascript.js | title": {"message": "Undvik att skicka äldre JavaScript till moderna webbläsare"}, "core/audits/byte-efficiency/modern-image-formats.js | description": {"message": "Bildformat som WebP och AVIF ger ofta bättre komprimering än PNG eller JPEG. Det gör att nedladdningen går snabbare och ger minskad dataförbrukning. [Läs mer om moderna bildformat](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/)."}, "core/audits/byte-efficiency/modern-image-formats.js | title": {"message": "Skicka bilder i modernare bildformat"}, "core/audits/byte-efficiency/offscreen-images.js | description": {"message": "<PERSON><PERSON><PERSON> bilder utanför skärmen och dolda bilder läsas in med uppskjuten inläsning efter att alla viktiga resurser är inlästa så att tiden till interaktivt tillstånd minskar. [L<PERSON>s mer om hur du skjuter upp bilder utanför skärmen](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/)."}, "core/audits/byte-efficiency/offscreen-images.js | title": {"message": "Skjut upp inläsningen av bilder som inte visas på skärmen"}, "core/audits/byte-efficiency/render-blocking-resources.js | description": {"message": "Resurser blockerar sidans första rendering. Infoga nödvändig JS-/CSS-kod direkt på sidan och skjut upp inläsningen av JS-kod/formatmallar som är mindre viktiga. [<PERSON><PERSON><PERSON> mer om hur du tar bort resurser som blockerar renderingen](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/)."}, "core/audits/byte-efficiency/render-blocking-resources.js | title": {"message": "Ta bort resurser som blockerar renderingen"}, "core/audits/byte-efficiency/total-byte-weight.js | description": {"message": "Hög nätverksbelastning kostar användarna pengar och har ett starkt samband med lång hämtningstid. [<PERSON><PERSON>s mer om hur du minskar nätverksbelastningen](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/)."}, "core/audits/byte-efficiency/total-byte-weight.js | displayValue": {"message": "Den sammanlagda storleken var {totalBytes, number, bytes} Kibit"}, "core/audits/byte-efficiency/total-byte-weight.js | failureTitle": {"message": "Undvik enorm nätverksbelastning"}, "core/audits/byte-efficiency/total-byte-weight.js | title": {"message": "Undviker enorm nätverksbelastning"}, "core/audits/byte-efficiency/unminified-css.js | description": {"message": "Genom att minifiera CSS-filer kan du minska nätverksbelastningen. [<PERSON><PERSON><PERSON> mer om hur du minifierar CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/)."}, "core/audits/byte-efficiency/unminified-css.js | title": {"message": "Minifiera CSS"}, "core/audits/byte-efficiency/unminified-javascript.js | description": {"message": "Att minifiera JavaScript-filer kan minska nätverksbelastningen och tiden det tar att tolka skript. [Lär mer om att minifiera JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/)."}, "core/audits/byte-efficiency/unminified-javascript.js | title": {"message": "Minifiera JavaScript"}, "core/audits/byte-efficiency/unused-css-rules.js | description": {"message": "Reducera regler som inte används från formatmallar och skjut upp CSS som inte används för innehåll ovanför mitten så att färre byte skickas via nätverket. [L<PERSON><PERSON> mer om hur du minskar oanvänd CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/)."}, "core/audits/byte-efficiency/unused-css-rules.js | title": {"message": "Reducera CSS som inte används"}, "core/audits/byte-efficiency/unused-javascript.js | description": {"message": "Reducera JavaScript som inte används och skjut upp inläsningen av skript tills de krävs så att färre byte skickas via nätverket. [Läs mer om hur du minskar JavaScript som inte används](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/)."}, "core/audits/byte-efficiency/unused-javascript.js | title": {"message": "Reducera JavaScript som inte används"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | description": {"message": "Om filerna cachelagras under lä<PERSON>re tid kan upprepade besök på sidan gå snabbare. [<PERSON><PERSON><PERSON> mer om hur du tillämpar effektiva policyer för cachelagring](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/)."}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue": {"message": "{itemCount,plural, =1{1 resurs hittades}other{# resurser hittades}}"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | failureTitle": {"message": "Skicka statiska tillgångar med en effektiv cachelagringspolicy"}, "core/audits/byte-efficiency/uses-long-cache-ttl.js | title": {"message": "Använder en effektiv cachelagringspolicy för statiska tillgångar"}, "core/audits/byte-efficiency/uses-optimized-images.js | description": {"message": "Optimerade bilder läses in snabbare och förbrukar mindre mobildata. [Läs om hur du kodar bilder på ett effektivt sätt](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)."}, "core/audits/byte-efficiency/uses-optimized-images.js | title": {"message": "Koda bilder effektivt"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnActualDimensions": {"message": "Faktiska mått"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | columnDisplayedDimensions": {"message": "<PERSON><PERSON>"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | failureTitle": {"message": "Bilderna var större än visad storlek"}, "core/audits/byte-efficiency/uses-responsive-images-snapshot.js | title": {"message": "Bilderna matchade visad storlek"}, "core/audits/byte-efficiency/uses-responsive-images.js | description": {"message": "Minska mobildataförbrukningen och förbättra hämtningstiden genom att visa bilder i rätt storlek. [<PERSON><PERSON><PERSON> mer om hur du ändrar storlek på bilder](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/)."}, "core/audits/byte-efficiency/uses-responsive-images.js | title": {"message": "<PERSON><PERSON><PERSON>nd bilder med rätt storlek"}, "core/audits/byte-efficiency/uses-text-compression.js | description": {"message": "Textresurser bör visas komprimerade (gzip, deflate eller brotli) så att färre byte skickas via nätverket. [L<PERSON>s mer om textkomprimering](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/)."}, "core/audits/byte-efficiency/uses-text-compression.js | title": {"message": "Aktivera textkomprimering"}, "core/audits/content-width.js | description": {"message": "Om bredden på appens innehåll inte stämmer överens med bredden på visningsområdet kanske inte appen är optimerad för mobilskärmar. [Läs mer om hur du anpassar innehållet för visningsområdet](https://developer.chrome.com/docs/lighthouse/pwa/content-width/)."}, "core/audits/content-width.js | explanation": {"message": "Visningsområdets storlek på {innerWidth} pixlar stämmer inte överens med fönsterstorleken på {outerWidth} pixlar."}, "core/audits/content-width.js | failureTitle": {"message": "Innehållet har inte rätt storlek för visningsområdet"}, "core/audits/content-width.js | title": {"message": "Innehållet har rätt storlek för visningsområdet"}, "core/audits/critical-request-chains.js | description": {"message": "Kedjorna av kritiska förfrågningar nedan visar vilka resurser som läses in med hög prioritet. Se om du kan förbättra sidhämtningstiden genom att göra kedjorna kortare, minska storleken på resurser som laddas ned eller skjuta upp nedladdningen av onödiga resurser. [<PERSON><PERSON><PERSON> mer om hur du undviker att kedja kritiska förfrågningar](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/)."}, "core/audits/critical-request-chains.js | displayValue": {"message": "{itemCount,plural, =1{1 kedja hittades}other{# kedjor hittades}}"}, "core/audits/critical-request-chains.js | title": {"message": "Undvik att kedjekoppla kritiska förfrågningar"}, "core/audits/csp-xss.js | columnDirective": {"message": "Direktiv"}, "core/audits/csp-xss.js | columnSeverity": {"message": "Allvarlighetsgrad"}, "core/audits/csp-xss.js | description": {"message": "En stark säkerhetspolicy fö<PERSON> (CSP) minskar avsevärt risken för attacker med webbkodinjektion (XSS). [<PERSON><PERSON>s mer om hur du förhindrar XSS med en CSP](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)"}, "core/audits/csp-xss.js | itemSeveritySyntax": {"message": "Syntax"}, "core/audits/csp-xss.js | metaTagMessage": {"message": "<PERSON><PERSON> har en CSP som definieras i en <meta>-tagg. Du kan flytta CSP:n till ett HTTP-huvud eller definiera en annan strikt CSP i en HTTP-rubrik."}, "core/audits/csp-xss.js | noCsp": {"message": "CPS hittades inte i åtgärdsläget"}, "core/audits/csp-xss.js | title": {"message": "Kontrollera att CPS är effektiv mot XSS-attacker"}, "core/audits/deprecations.js | columnDeprecate": {"message": "Utfasning/varning"}, "core/audits/deprecations.js | columnLine": {"message": "<PERSON><PERSON>"}, "core/audits/deprecations.js | description": {"message": "Utfasade API:er tas bort från webbläsaren efter hand. [<PERSON><PERSON>s mer om utfasade API:er](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/)."}, "core/audits/deprecations.js | displayValue": {"message": "{itemCount,plural, =1{1 varning hittades}other{# varningar hittades}}"}, "core/audits/deprecations.js | failureTitle": {"message": "Utfasade API:er används"}, "core/audits/deprecations.js | title": {"message": "Utfasade API:er und<PERSON>s"}, "core/audits/dobetterweb/charset.js | description": {"message": "En deklaration av teckenkodning krävs. Den kan lämnas med en `<meta>`-tagg i HTML-kodens första 1 024 byte eller i HTTP-svarshuvudet för Content-Type. [Läs mer om hur du deklarerar teckenkodningen](https://developer.chrome.com/docs/lighthouse/best-practices/charset/)."}, "core/audits/dobetterweb/charset.js | failureTitle": {"message": "Deklaration av teckenuppsättning saknas eller förekommer för sent i HTML-koden"}, "core/audits/dobetterweb/charset.js | title": {"message": "Definierar teckenuppsättning korrekt"}, "core/audits/dobetterweb/doctype.js | description": {"message": "Om du anger en doctype för<PERSON><PERSON>r det webbläsaren att byta till quirks-läget. [<PERSON><PERSON><PERSON> mer om deklarationen av doctype](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/)."}, "core/audits/dobetterweb/doctype.js | explanationBadDoctype": {"message": "Doctype-namnet måste vara strängen `html`"}, "core/audits/dobetterweb/doctype.js | explanationLimitedQuirks": {"message": "Dokumentet innehåller en `doctype` som utlöser `limited-quirks-mode`"}, "core/audits/dobetterweb/doctype.js | explanationNoDoctype": {"message": "Dokument måste innehålla en doctype"}, "core/audits/dobetterweb/doctype.js | explanationPublicId": {"message": "publicId förväntades vara en tom sträng"}, "core/audits/dobetterweb/doctype.js | explanationSystemId": {"message": "systemId förväntades vara en tom sträng"}, "core/audits/dobetterweb/doctype.js | explanationWrongDoctype": {"message": "Dokumentet innehåller en `doctype` som utlöser `quirks-mode`"}, "core/audits/dobetterweb/doctype.js | failureTitle": {"message": "<PERSON><PERSON> har inte HTML som doctype, vilket aktiverar quirks-läge"}, "core/audits/dobetterweb/doctype.js | title": {"message": "<PERSON><PERSON> har HTML som doctype"}, "core/audits/dobetterweb/dom-size.js | columnStatistic": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | columnValue": {"message": "<PERSON><PERSON>"}, "core/audits/dobetterweb/dom-size.js | description": {"message": "En stor DOM leder till att minnesanvändning ökar, [formatberäkningarna](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations) förlängs och att kostsamma [flödesomformningar för layout](https://developers.google.com/speed/articles/reflow) produceras. [Läs om hur du undviker en för stor DOM-storlek](https://developer.chrome.com/docs/lighthouse/performance/dom-size/)."}, "core/audits/dobetterweb/dom-size.js | displayValue": {"message": "{itemCount,plural, =1{1 element}other{# element}}"}, "core/audits/dobetterweb/dom-size.js | failureTitle": {"message": "Undvik ett onödigt stort DOM-träd"}, "core/audits/dobetterweb/dom-size.js | statisticDOMDepth": {"message": "Största DOM-djup"}, "core/audits/dobetterweb/dom-size.js | statisticDOMElements": {"message": "Totalt antal DOM-element"}, "core/audits/dobetterweb/dom-size.js | statisticDOMWidth": {"message": "Högsta antal underordnade element"}, "core/audits/dobetterweb/dom-size.js | title": {"message": "Undviker ett onödigt stort DOM-träd"}, "core/audits/dobetterweb/geolocation-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> blir misstänksamma eller förvirrade av webbplatser som ber om åtkomst till deras plats utan sammanhang. Det kan vara bättre att koppla begäran till något användaren gör. [<PERSON><PERSON><PERSON> mer om behörigheten för geolokalisering](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/)."}, "core/audits/dobetterweb/geolocation-on-start.js | failureTitle": {"message": "Begär åtkomst till geografisk plats vid sidinlä<PERSON>ning"}, "core/audits/dobetterweb/geolocation-on-start.js | title": {"message": "Undviker att begära åtkomst till geografisk plats vid sidinlä<PERSON>ning"}, "core/audits/dobetterweb/inspector-issues.js | columnIssueType": {"message": "Typ av fel"}, "core/audits/dobetterweb/inspector-issues.js | description": {"message": "Fel som loggas i `Issues`-panelen i Chromes verktyg för programmerare indikerar olösta problem. De kan bero på fel i nätverksförfrågningar, otillräckliga säkerhetskontroller och andra webbläsarproblem. Öppna felpanelen i Chromes verktyg för programmerare för detaljerad information om varje fel."}, "core/audits/dobetterweb/inspector-issues.js | failureTitle": {"message": "Felen loggades i `Issues`-panelen i Chromes verktyg för programmerare"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeBlockedByResponse": {"message": "Blockerades av principen mot korsursprung"}, "core/audits/dobetterweb/inspector-issues.js | issueTypeHeavyAds": {"message": "Hög resursanvändning av annonser"}, "core/audits/dobetterweb/inspector-issues.js | title": {"message": "Det finns inga fel i `Issues`-panelen i Chromes verktyg för programmerare"}, "core/audits/dobetterweb/js-libraries.js | columnVersion": {"message": "Version"}, "core/audits/dobetterweb/js-libraries.js | description": {"message": "Alla JavaScript-bibliotek i användargränssnittet har identifierats på den här sidan. [<PERSON><PERSON><PERSON> mer om denna diagnostikgranskning för identifiering av JavaScript-bibliotek](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/)."}, "core/audits/dobetterweb/js-libraries.js | title": {"message": "JavaScript-bibliotek har identifierats"}, "core/audits/dobetterweb/no-document-write.js | description": {"message": "<PERSON><PERSON><PERSON> användare med långsam anslutning kan externa skript som infogas dynamiskt via `document.write()` fördr<PERSON>ja sidinläsningen med tiotals sekunder. [Läs om hur du undviker document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/)."}, "core/audits/dobetterweb/no-document-write.js | failureTitle": {"message": "Undvik `document.write()`"}, "core/audits/dobetterweb/no-document-write.js | title": {"message": "`document.write()` undviks"}, "core/audits/dobetterweb/notification-on-start.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> blir misstänksamma eller förvirrade av webbplatser som ber om åtkomst att skicka aviseringar utan sammanhang. Det kan vara bättre att koppla förfrågan till rörelser. [<PERSON><PERSON><PERSON> mer om hur du får behörighet till aviseringar på ett ansvarsfullt sätt](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/)."}, "core/audits/dobetterweb/notification-on-start.js | failureTitle": {"message": "Begär aviseringsbehörighet vid sidinläsning"}, "core/audits/dobetterweb/notification-on-start.js | title": {"message": "Undviker att begära aviseringsbehörighet vid sidinläsning"}, "core/audits/dobetterweb/paste-preventing-inputs.js | description": {"message": "Preventing input pasting is a UX anti-pattern, and undermines good security policy. [Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/)."}, "core/audits/dobetterweb/paste-preventing-inputs.js | failureTitle": {"message": "Prevents users from pasting into input fields"}, "core/audits/dobetterweb/paste-preventing-inputs.js | title": {"message": "Allows users to paste into input fields"}, "core/audits/dobetterweb/uses-http2.js | columnProtocol": {"message": "Protokoll"}, "core/audits/dobetterweb/uses-http2.js | description": {"message": "HTTP/2 ger många fördelar jämfört med HTTP/1.1, inklusive binära fält och multiplexning. [Läs mer om HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/)."}, "core/audits/dobetterweb/uses-http2.js | displayValue": {"message": "{itemCount,plural, =1{1 begäran visades inte via HTTP/2}other{# begäranden visades inte via HTTP/2}}"}, "core/audits/dobetterweb/uses-http2.js | title": {"message": "Använd HTTP/2"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | description": {"message": "Det kan vara bra att märka dina händelselyssnare för tryck och hjul som `passive` för att förbättra sidans scrollningsfunktion. [Läs mer om hur du använder passiva händelselyssnare](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/)."}, "core/audits/dobetterweb/uses-passive-event-listeners.js | failureTitle": {"message": "Passiva lyssnare används inte för att förbättra scrollningsprestanda"}, "core/audits/dobetterweb/uses-passive-event-listeners.js | title": {"message": "Passiva lyssnare används för att förbättra scrollningsprestanda"}, "core/audits/errors-in-console.js | description": {"message": "Fel som loggats i konsolen indikerar olösta problem. De kan bero på fel i nätverksförfrågningar och andra webbläsarproblem. [Läs mer om dessa fel i diagnostikgranskningen på konsolen](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)"}, "core/audits/errors-in-console.js | failureTitle": {"message": "Webbläsarfel loggades i konsolen"}, "core/audits/errors-in-console.js | title": {"message": "Inga webbläsarfel loggades i konsolen"}, "core/audits/font-display.js | description": {"message": "Använd CSS-funktionen `font-display` för att säkerställa att texten är synlig för användaren medan webbteckensnitten läses in. [<PERSON><PERSON><PERSON> mer om `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/)."}, "core/audits/font-display.js | failureTitle": {"message": "Se till att all text förb<PERSON>r synlig medan webbteckensnitten läses in"}, "core/audits/font-display.js | title": {"message": "All text förb<PERSON>r synlig medan webbteckensnitten läses in"}, "core/audits/font-display.js | undeclaredFontOriginWarning": {"message": "{fontCountFor<PERSON>rigin,plural, =1{Det gick inte att kontrollera `font-display`-värdet för ursprunget {fontOrigin} automatiskt i Lighthouse.}other{Det gick inte att kontrollera `font-display`-värdena för ursprunget {fontOrigin} automatiskt i Lighthouse.}}"}, "core/audits/image-aspect-ratio.js | columnActual": {"message": "Bildformat (faktiska)"}, "core/audits/image-aspect-ratio.js | columnDisplayed": {"message": "Bildformat (visade)"}, "core/audits/image-aspect-ratio.js | description": {"message": "Bildens visningsformat ska matcha det naturliga bildformatet. [<PERSON><PERSON><PERSON> mer om bildformat](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/)."}, "core/audits/image-aspect-ratio.js | failureTitle": {"message": "Visar bilder med felaktigt bildformat"}, "core/audits/image-aspect-ratio.js | title": {"message": "Bilder visas med korrekt bildformat"}, "core/audits/image-size-responsive.js | columnActual": {"message": "Faktisk storlek"}, "core/audits/image-size-responsive.js | columnDisplayed": {"message": "Visad storlek"}, "core/audits/image-size-responsive.js | columnExpected": {"message": "Förväntad storlek"}, "core/audits/image-size-responsive.js | description": {"message": "Bildens faktiska mått ska vara proportionella mot skärmstorleken och pixelmåtten för tydligast möjliga bildvisning. [<PERSON><PERSON><PERSON> mer om hur du tillhandahåller responsiva bilder](https://web.dev/serve-responsive-images/)."}, "core/audits/image-size-responsive.js | failureTitle": {"message": "Bilder visas med låg upplösning"}, "core/audits/image-size-responsive.js | title": {"message": "Bilder visas med lämplig upplösning"}, "core/audits/installable-manifest.js | already-installed": {"message": "Appen har redan installerats"}, "core/audits/installable-manifest.js | cannot-download-icon": {"message": "Det gick inte att ladda ned en obligatorisk ikon från manifestet"}, "core/audits/installable-manifest.js | columnValue": {"message": "Felorsak"}, "core/audits/installable-manifest.js | description": {"message": "Tjänstefunktioner är en teknik som gör det möjligt att använda flera progressiva webbappsfunktioner i appen, till exempel offlineanvändning, pushmeddelanden och att lägga till den på startskärmen. Med rätt tjänstefunktion och manifestimplementeringar kan webbläsare i förväg uppmana användare att lägga till appen på startskärmen, vilket kan leda till större engagemang. [Läs mer om kraven för manifestinstallation](https://developer.chrome.com/docs/lighthouse/pwa/installable-manifest/)."}, "core/audits/installable-manifest.js | displayValue": {"message": "{itemCount,plural, =1{1 anledning}other{# anledningar}}"}, "core/audits/installable-manifest.js | failureTitle": {"message": "Webbappens manifest eller tjänstefunktion uppfyller inte kraven för installation"}, "core/audits/installable-manifest.js | ids-do-not-match": {"message": "Appadressen för Play Butik matchar inte id för Play Butik"}, "core/audits/installable-manifest.js | in-incognito": {"message": "<PERSON><PERSON> l<PERSON> in i ett inkognitofönster"}, "core/audits/installable-manifest.js | manifest-display-not-supported": {"message": "Egenskapen display i manifestet måste ha värdet standalone, fullscreen eller minimal-ui"}, "core/audits/installable-manifest.js | manifest-display-override-not-supported": {"message": "Manifestet innehåller fältet display_override och det första visningsläget som stöds måste vara antingen standalone, fullscreen eller minimal-ui."}, "core/audits/installable-manifest.js | manifest-empty": {"message": "Manifestet kunde inte hämtas, är tomt eller kunde inte analyseras"}, "core/audits/installable-manifest.js | manifest-location-changed": {"message": "Manifestets webbadress ändrades under tiden som manifestet hämtades."}, "core/audits/installable-manifest.js | manifest-missing-name-or-short-name": {"message": "Manifestet innehåller inte fältet name el<PERSON> short_name"}, "core/audits/installable-manifest.js | manifest-missing-suitable-icon": {"message": "Manifestet innehåller ingen lämplig ikon – PNG-, SVG- eller WebP-format på minst {value0} px krävs, sizes-attributet måste anges och purpose-attributet, om det anges, måste innehålla any."}, "core/audits/installable-manifest.js | no-acceptable-icon": {"message": "Ingen av de ikoner som tillhandahålls är minst {value0} px och i PNG-, SVG- eller WebP-format med purpose-attributet utan inställning eller inställt på any"}, "core/audits/installable-manifest.js | no-icon-available": {"message": "Ikonen som laddades ned var tom eller skadad"}, "core/audits/installable-manifest.js | no-id-specified": {"message": "Inget Play Butik-id har till<PERSON>"}, "core/audits/installable-manifest.js | no-manifest": {"message": "<PERSON><PERSON> har ingen webbadress för <link> i manifestet"}, "core/audits/installable-manifest.js | no-matching-service-worker": {"message": "Ingen matchande tjänstefunktion hittades. Du kanske måste läsa in sidan igen eller kontrollera att tjänstefunktionens omfattning för den aktuella sidan inbegriper omfattningen och startwebbadressen i manifestet."}, "core/audits/installable-manifest.js | no-url-for-service-worker": {"message": "Det går inte att kontrollera tjänstefunktionen utan fältet start_url i manifestet."}, "core/audits/installable-manifest.js | noErrorId": {"message": "Installationens fel-id {errorId} känns inte igen"}, "core/audits/installable-manifest.js | not-from-secure-origin": {"message": "Sidan visas inte från en säker källa"}, "core/audits/installable-manifest.js | not-in-main-frame": {"message": "<PERSON><PERSON> lä<PERSON> inte in i huvudramen"}, "core/audits/installable-manifest.js | not-offline-capable": {"message": "<PERSON><PERSON> fungerar inte offline"}, "core/audits/installable-manifest.js | pipeline-restarted": {"message": "PWA har avinstallerats. Kontrollen av om PWA kan installeras återställs."}, "core/audits/installable-manifest.js | platform-not-supported-on-android": {"message": "Applattformen som anges stöds inte på Android"}, "core/audits/installable-manifest.js | prefer-related-applications": {"message": "Manifestet anger prefer_related_applications: true"}, "core/audits/installable-manifest.js | prefer-related-applications-only-beta-stable": {"message": "prefer_related_applications stöds bara i Chrome Beta och Chrome Stable på Android."}, "core/audits/installable-manifest.js | protocol-timeout": {"message": "Lighthouse kunde inte avgöra om en tjänstefunktion används. Testa med en senare version av Chrome."}, "core/audits/installable-manifest.js | scheme-not-supported-for-webapk": {"message": "Manifestets webbadresschema ({scheme}) stöds inte på Android."}, "core/audits/installable-manifest.js | start-url-not-valid": {"message": "Startwebbadressen i manifestet är ogiltig"}, "core/audits/installable-manifest.js | title": {"message": "Webbappens manifest och tjänstefunktion uppfyller kraven för installation"}, "core/audits/installable-manifest.js | url-not-supported-for-webapk": {"message": "En webbadress i manifestet innehåller ett användarnamn, ett lösenord eller en port"}, "core/audits/installable-manifest.js | warn-not-offline-capable": {"message": "Sidan fungerar inte offline. <PERSON><PERSON> kommer inte att anses vara installerbar efter Chrome 93, den stabila version som lanseras i augusti 2021."}, "core/audits/is-on-https.js | allowed": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/is-on-https.js | blocked": {"message": "Blockerad"}, "core/audits/is-on-https.js | columnInsecureURL": {"message": "<PERSON><PERSON><PERSON><PERSON> webbadress"}, "core/audits/is-on-https.js | columnResolution": {"message": "Hantering av begäran"}, "core/audits/is-on-https.js | description": {"message": "<PERSON>a webbplatser ska skyddas med HTTPS, även de som inte hanterar känsliga uppgifter. En del av detta är att undvika [blandat innehåll](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), där vissa resurser läses in via HTTP trots att den första begäran gjordes via HTTPS. HTTPS förhindrar att inkräktare påverkar eller passivt avlyssnar kommunikationen mellan din app och dina användare, och är ett krav för HTTP/2 och många nya API:er för webbplattformar. [Läs mer om HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/)."}, "core/audits/is-on-https.js | displayValue": {"message": "{itemCount,plural, =1{1 osäker begäran hittades}other{# osäkra begäranden hittades}}"}, "core/audits/is-on-https.js | failureTitle": {"message": "Använder inte HTTPS"}, "core/audits/is-on-https.js | title": {"message": "Använder HTTPS"}, "core/audits/is-on-https.js | upgraded": {"message": "Uppgraderas automatiskt till HTTPS"}, "core/audits/is-on-https.js | warning": {"message": "Tillåts med varning"}, "core/audits/largest-contentful-paint-element.js | description": {"message": "Detta är det största innehållselementet som ritades upp i visningsområdet. [<PERSON><PERSON><PERSON> mer om elementet för den största uppritningen av innehåll](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/largest-contentful-paint-element.js | title": {"message": "Element som största uppritningen av innehåll gjordes för"}, "core/audits/layout-shift-elements.js | columnContribution": {"message": "Bidrag till CLS"}, "core/audits/layout-shift-elements.js | description": {"message": "Dessa DOM-element bidrog mest till sidans CLS. [<PERSON><PERSON><PERSON> mer om hur du kan förbättra CLS](https://web.dev/optimize-cls/)"}, "core/audits/layout-shift-elements.js | title": {"message": "Undvik större layoutförskjutningar"}, "core/audits/lcp-lazy-loaded.js | description": {"message": "Bilder o<PERSON><PERSON><PERSON><PERSON> mitten med uppskjuten inläsning renderas senare än sidans liv<PERSON>kel, vilket kan fördröja den största uppritningen av innehåll. [L<PERSON>s mer om optimal uppskjuten inläsning](https://web.dev/lcp-lazy-loading/)."}, "core/audits/lcp-lazy-loaded.js | failureTitle": {"message": "Bildinläsningen var uppskjuten vid största uppritningen av innehåll"}, "core/audits/lcp-lazy-loaded.js | title": {"message": "Bildinläsningen var inte uppskjuten vid största uppritningen av innehåll"}, "core/audits/long-tasks.js | description": {"message": "<PERSON>r de uppgifter i huvudtråden som har körts under lä<PERSON><PERSON> tid, vilket kan identifiera de viktigaste flaskhalsarna som fördröjer inmatningen. [Läs om hur du undviker uppgifter i huvudtrådar som har körts under lång tid](https://web.dev/long-tasks-devtools/)"}, "core/audits/long-tasks.js | displayValue": {"message": "{itemCount,plural, =1{# uppgift som körts under lång tid hittades}other{# uppgifter som körts under lång tid hittades}}"}, "core/audits/long-tasks.js | title": {"message": "Undvik uppgifter som körs under lång tid i huvudtråden"}, "core/audits/mainthread-work-breakdown.js | columnCategory": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/mainthread-work-breakdown.js | description": {"message": "Minska tiden det tar att tolka, kompilera och köra JS-kod. Det brukar hjälpa att minska storleken på JS-resurserna som skickas. [Lär dig att minimera arbeten i huvudtrådar](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)"}, "core/audits/mainthread-work-breakdown.js | failureTitle": {"message": "Minska arbetsbelastningen på modertråden"}, "core/audits/mainthread-work-breakdown.js | title": {"message": "Minskar arbetsbelastningen på modertråden"}, "core/audits/manual/pwa-cross-browser.js | description": {"message": "Webbplatser ska fungera i alla stora webbläsare för att nå så många användare som möjligt. [Läs om kompatibilitet över flera webbläsare](https://developer.chrome.com/docs/lighthouse/pwa/pwa-cross-browser/)."}, "core/audits/manual/pwa-cross-browser.js | title": {"message": "Webbläsaren fungerar i olika webbläsare"}, "core/audits/manual/pwa-each-page-has-url.js | description": {"message": "Kontrollera att enskilda sidor går att djuplänka via en webbadress och att webbadresserna är unika för syftet att dela sidorna på sociala medier. [<PERSON><PERSON><PERSON> mer om djuplänkar](https://developer.chrome.com/docs/lighthouse/pwa/pwa-each-page-has-url/)."}, "core/audits/manual/pwa-each-page-has-url.js | title": {"message": "Varje sida har en webbadress"}, "core/audits/manual/pwa-page-transitions.js | description": {"message": "Övergångar ska gå snabbt när du trycker i appen, även på långsamma nätverk. Detta är avgörande för vad användarna uppfattar som prestanda. [<PERSON><PERSON><PERSON> mer om sidövergångar](https://developer.chrome.com/docs/lighthouse/pwa/pwa-page-transitions/)."}, "core/audits/manual/pwa-page-transitions.js | title": {"message": "Det ska inte kännas som om övergångar mellan sidor blockeras på nätverket"}, "core/audits/maskable-icon.js | description": {"message": "En maskerbar ikon gör att bilden fyller ut hela formen utan att vara avskärmad upptill och nedtill när appen installeras på en enhet. [<PERSON><PERSON>s mer om maskerbara manifestikoner](https://developer.chrome.com/docs/lighthouse/pwa/maskable-icon-audit/)."}, "core/audits/maskable-icon.js | failureTitle": {"message": "Manifestet har ingen maskerbar ikon"}, "core/audits/maskable-icon.js | title": {"message": "Manifest har en maskerbar ikon"}, "core/audits/metrics/cumulative-layout-shift.js | description": {"message": "Kumulativ layoutförskjutning mäter rörelsen hos synliga element inom visningsområdet. [<PERSON><PERSON><PERSON> mer om mätvärdet Kumulativ layoutförskjutning](https://web.dev/cls/)."}, "core/audits/metrics/experimental-interaction-to-next-paint.js | description": {"message": "Interaktion till nästa uppritning mäter sidans responsivitet, hur lång tid det tar innan det syns att en sida svarar på indata från användare. [Läs mer om mätvärdet Interakation till nästa uppritning](https://web.dev/inp/)."}, "core/audits/metrics/first-contentful-paint.js | description": {"message": "<PERSON><PERSON>rsta innehållsrenderingen anger när den första texten eller bilden ritades upp. [<PERSON><PERSON><PERSON> mer om mätvärdet Första innehållsrenderingen](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/)."}, "core/audits/metrics/first-meaningful-paint.js | description": {"message": "<PERSON><PERSON>rsta användbara renderingen anger när sidans primära innehåll blev synligt. [<PERSON><PERSON><PERSON> mer om mätvärdet Första användbara renderingen](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/)."}, "core/audits/metrics/interactive.js | description": {"message": "Tid till interaktivt tillstånd är den tid det tar innan sidan är fullständigt interaktiv. [<PERSON><PERSON><PERSON> mer om mätvärdet Tid till interaktivt tillstånd](https://developer.chrome.com/docs/lighthouse/performance/interactive/)."}, "core/audits/metrics/largest-contentful-paint.js | description": {"message": "Största uppritningen av innehåll anger tidpunkten då den största texten eller bilden ritades upp. [<PERSON><PERSON><PERSON> mer om mätvärdet Största uppritningen av innehåll](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)"}, "core/audits/metrics/max-potential-fid.js | description": {"message": "Den högsta potentiella fördröjningen till första inmatningen som användarna kan få är längden på den längsta uppgiften. [L<PERSON>s mer om mätvärdet Maximum Potential First Input Delay](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/)."}, "core/audits/metrics/speed-index.js | description": {"message": "Hastighetsindexet visar hur snabbt en sida fylls med synligt innehåll. [<PERSON><PERSON>s mer om mätvärdet Speed Index](https://developer.chrome.com/docs/lighthouse/performance/speed-index/)."}, "core/audits/metrics/total-blocking-time.js | description": {"message": "Summan av alla tidsperioder mellan FCP och Tid till interaktivt tillstånd när uppgiftstiden överskred 50 ms, uttryckt i millisekunder. [Läs mer om mätvärdet Total Blocking Time](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/)."}, "core/audits/network-rtt.js | description": {"message": "Nätverkets RTT-tider (Round Trip Times) har stor inverkan på prestanda. Om RTT-tiden till ett ursprung är för hög tyder det på att servrar närmare användaren skulle kunna förbättra prestandan. [<PERSON><PERSON><PERSON> mer om RTT-tid](https://hpbn.co/primer-on-latency-and-bandwidth/)."}, "core/audits/network-rtt.js | title": {"message": "Nätverkets RTT-tider"}, "core/audits/network-server-latency.js | description": {"message": "Serverlatens kan påverka webbprestanda. Om serverlatensen är hög för ett ursprung tyder det på att servern är överbelastad eller har dålig backend-prestanda. [<PERSON><PERSON><PERSON> mer om serversvarstid](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall)."}, "core/audits/network-server-latency.js | title": {"message": "Serverlatens"}, "core/audits/no-unload-listeners.js | description": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> `unload` aktiveras inte på ett tillförlitligt sätt och att lyssna efter den kan förhindra webbläsaroptimeringar, t.ex. vilocacheminne. Använd händelserna `pagehide` eller `visibilitychange` i stället. [<PERSON><PERSON><PERSON> mer om att ta bort händelselyssnare](https://web.dev/bfcache/#never-use-the-unload-event)"}, "core/audits/no-unload-listeners.js | failureTitle": {"message": "Registrerar en lyssnare för `unload`"}, "core/audits/no-unload-listeners.js | title": {"message": "Undviker händelselyssnare för `unload`"}, "core/audits/non-composited-animations.js | description": {"message": "Animationer som inte är sammansatta kan vara hackiga och orsaka ökad CLS. [Läs om hur du undviker animationer som inte är sammansatta](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)"}, "core/audits/non-composited-animations.js | displayValue": {"message": "{itemCount,plural, =1{# animerat element hittades}other{# animerade element hittades}}"}, "core/audits/non-composited-animations.js | filterMayMovePixels": {"message": "Den filterrelaterade egenskapen kan flytta pixlar"}, "core/audits/non-composited-animations.js | incompatibleAnimations": {"message": "Målet har en annan animation som är inkompatibel"}, "core/audits/non-composited-animations.js | nonReplaceCompositeMode": {"message": "Effekten har ett annat sammansatt läge än ”replace”"}, "core/audits/non-composited-animations.js | title": {"message": "Undvik icke sammansatta animationer"}, "core/audits/non-composited-animations.js | transformDependsBoxSize": {"message": "En ”transform”-<PERSON><PERSON> egenskap beror på rutans storlek"}, "core/audits/non-composited-animations.js | unsupportedCSSProperty": {"message": "{propertyCount,plural, =1{CSS-egenskap som inte stöds: {properties}}other{CSS-egenskaper som inte stöds: {properties}}}"}, "core/audits/non-composited-animations.js | unsupportedTimingParameters": {"message": "Effekten har tidsparametrar som inte stöds"}, "core/audits/performance-budget.js | description": {"message": "<PERSON><PERSON><PERSON> antal och storlek för nätverksbegäranden under de mål som anges i den angivna prestandabudgeten. [<PERSON><PERSON><PERSON> mer om prestandabudgetar](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/performance-budget.js | requestCountOverBudget": {"message": "{count,plural, =1{1 begäran}other{# begäranden}}"}, "core/audits/performance-budget.js | title": {"message": "<PERSON>stand<PERSON><PERSON><PERSON>"}, "core/audits/preload-fonts.js | description": {"message": "<PERSON><PERSON><PERSON> in teckensnitt i förväg med `optional` så att förstagångsbesökare kan använda dem. [Läs mer om att läsa in teckensnitt i förväg](https://web.dev/preload-optional-fonts/)"}, "core/audits/preload-fonts.js | failureTitle": {"message": "Teckensnitt med `font-display: optional` läses inte in i förväg"}, "core/audits/preload-fonts.js | title": {"message": "Teckensnitt med `font-display: optional` läses in i förväg"}, "core/audits/prioritize-lcp-image.js | description": {"message": "Om LCP-elementet läggs till på sidan dynamiskt bör du läsa in bilden i förväg för att förbättra LCP. [<PERSON><PERSON><PERSON> mer om att läsa in LCP-element i förväg](https://web.dev/optimize-lcp/#optimize-when-the-resource-is-discovered)."}, "core/audits/prioritize-lcp-image.js | title": {"message": "<PERSON><PERSON><PERSON> in bilden i förväg för största uppritningen av innehåll"}, "core/audits/redirects.js | description": {"message": "Omdirigeringar medför en ytterligare fördröjning innan sidan kan läsas in. [<PERSON><PERSON><PERSON> mer om hur du undviker omdirigeringar av sidor](https://developer.chrome.com/docs/lighthouse/performance/redirects/)."}, "core/audits/redirects.js | title": {"message": "Undvik upprepade omdirigeringar"}, "core/audits/resource-summary.js | description": {"message": "<PERSON>ägg till en budget.json-fil för att ange budgetar för kvantitet och storlek på sidresurser. [<PERSON><PERSON><PERSON> mer om prestandabudgetar](https://web.dev/use-lighthouse-for-performance-budgets/)."}, "core/audits/resource-summary.js | displayValue": {"message": "{requestCount,plural, =1{1 beg<PERSON>ran • {byteCount, number, bytes} Kibit}other{# förfrågningar • {byteCount, number, bytes} Kibit}}"}, "core/audits/resource-summary.js | title": {"message": "Begränsa antalet begäranden och storleken på överföringar"}, "core/audits/seo/canonical.js | description": {"message": "Kanoniska länkar föreslår vilka webbadresser som ska visas i sökresultat. [<PERSON><PERSON><PERSON> mer om kanoniska länkar](https://developer.chrome.com/docs/lighthouse/seo/canonical/)."}, "core/audits/seo/canonical.js | explanationConflict": {"message": "Flera webbadresser som står i konflikt ({urlList})"}, "core/audits/seo/canonical.js | explanationInvalid": {"message": "Ogi<PERSON><PERSON> webbadress ({url})"}, "core/audits/seo/canonical.js | explanationPointsElsewhere": {"message": "<PERSON><PERSON><PERSON> på en annan `hreflang`-plats ({url})"}, "core/audits/seo/canonical.js | explanationRelative": {"message": "Är inte en absolut webbadress ({url})"}, "core/audits/seo/canonical.js | explanationRoot": {"message": "Pekar på domänens rotadress (startsidan) i stället för motsvarande sida med innehåll"}, "core/audits/seo/canonical.js | failureTitle": {"message": "Dokumentet har ingen giltig länk med `rel=canonical`"}, "core/audits/seo/canonical.js | title": {"message": "Dokumentet har ett giltigt `rel=canonical`-värde"}, "core/audits/seo/crawlable-anchors.js | columnFailingLink": {"message": "<PERSON><PERSON><PERSON> som inte kan genomsökas"}, "core/audits/seo/crawlable-anchors.js | description": {"message": "Sökmotorer kan genomsöka webbplatser med hjälp av länkattributet `href`. Kontrollera att attributet `href` i ankarelement länkar till en lämplig målsida, så att det går att hitta fler sidor på webbplatsen. [L<PERSON><PERSON> mer om hur du gör länkar sökbara](https://support.google.com/webmasters/answer/9112205)"}, "core/audits/seo/crawlable-anchors.js | failureTitle": {"message": "Länkarna är inte genomsökningsbara"}, "core/audits/seo/crawlable-anchors.js | title": {"message": "Länkarna är genomsökningsbara"}, "core/audits/seo/font-size.js | additionalIllegibleText": {"message": "Ytterligare oläslig text"}, "core/audits/seo/font-size.js | columnFontSize": {"message": "Teckensnittsstorlek"}, "core/audits/seo/font-size.js | columnPercentPageText": {"message": "Andel text på sidan"}, "core/audits/seo/font-size.js | columnSelector": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/seo/font-size.js | description": {"message": "Teckenstorlekar under 12 pixlar är för små för att kunna läsas och kräver att telefonbesökare zoomar genom att nypa för att kunna läsa. Försök ha minst 60 % av texten i 12 pixlar eller mer. [<PERSON><PERSON><PERSON> mer om läsbara teckenstorlekar](https://developer.chrome.com/docs/lighthouse/seo/font-size/)."}, "core/audits/seo/font-size.js | displayValue": {"message": "{decimalProportion, number, extendedPercent} läslig text"}, "core/audits/seo/font-size.js | explanationViewport": {"message": "Text är oläslig eftersom det inte finns någon metatagg för visningsområde som är optimerad för mobila skärmar."}, "core/audits/seo/font-size.js | failureTitle": {"message": "Dokumentet har inga läsliga teckenstorlekar"}, "core/audits/seo/font-size.js | legibleText": {"message": "Läslig text"}, "core/audits/seo/font-size.js | title": {"message": "Dokumentet har läsliga teckenstorlekar"}, "core/audits/seo/hreflang.js | description": {"message": "Hreflang-länkar informerar sökmotorer om vilken version av en sida de ska visa i sökresultaten för ett visst språk eller område. [L<PERSON>s mer om `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/)."}, "core/audits/seo/hreflang.js | failureTitle": {"message": "Dokumentet har inte ett giltigt `hreflang`-värde"}, "core/audits/seo/hreflang.js | notFullyQualified": {"message": "Relativt href-värde"}, "core/audits/seo/hreflang.js | title": {"message": "Dokumentet har ett giltigt `hreflang`-värde"}, "core/audits/seo/hreflang.js | unexpectedLanguage": {"message": "Oväntad språkkod"}, "core/audits/seo/http-status-code.js | description": {"message": "Sidor med HTTP-statuskoder som indikerar att begäran misslyckades kanske inte indexeras korrekt. [Läs mer om HTTP-statuskoder](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/)."}, "core/audits/seo/http-status-code.js | failureTitle": {"message": "<PERSON><PERSON> har en HTTP-statuskod som visar att begäran inte lyckades"}, "core/audits/seo/http-status-code.js | title": {"message": "<PERSON><PERSON> har en giltig HTTP-statuskod"}, "core/audits/seo/is-crawlable.js | description": {"message": "Sökmotorer kan inte inkludera dina sidor i sökresultat om de inte har behörighet att genomsöka dem. [Läs mer om direktiv för sökrobot](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/)."}, "core/audits/seo/is-crawlable.js | failureTitle": {"message": "Sidan är blockerad för indexering"}, "core/audits/seo/is-crawlable.js | title": {"message": "Sidan är inte blockerad från indexering"}, "core/audits/seo/link-text.js | description": {"message": "Beskrivande länktext hjälper sökmotorer att förstå innehållet. [<PERSON><PERSON><PERSON> mer om hur du gör länkar mer tillgängliga](https://developer.chrome.com/docs/lighthouse/seo/link-text/)."}, "core/audits/seo/link-text.js | displayValue": {"message": "{itemCount,plural, =1{1 länk hittades}other{# länkar hittades}}"}, "core/audits/seo/link-text.js | failureTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> har inte beskrivande text"}, "core/audits/seo/link-text.js | title": {"message": "<PERSON><PERSON><PERSON><PERSON> har beskrivande text"}, "core/audits/seo/manual/structured-data.js | description": {"message": "<PERSON><PERSON><PERSON> [testverktyget för strukturerad data](https://search.google.com/structured-data/testing-tool/) och [Structured Data Linter](http://linter.structured-data.org/) för att validera strukturerad data. [L<PERSON>s mer om strukturerad data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/)."}, "core/audits/seo/manual/structured-data.js | title": {"message": "Strukturerad data är giltig"}, "core/audits/seo/meta-description.js | description": {"message": "Metabeskrivningar kan inkluderas i sökresultat för att sammanfatta sidinnehållet. [<PERSON><PERSON><PERSON> mer om metabeskrivningar](https://developer.chrome.com/docs/lighthouse/seo/meta-description/)."}, "core/audits/seo/meta-description.js | explanation": {"message": "Beskrivningstexten är tom."}, "core/audits/seo/meta-description.js | failureTitle": {"message": "Dokumentet har ingen metabeskrivning"}, "core/audits/seo/meta-description.js | title": {"message": "Dokumentet har en metabeskrivning"}, "core/audits/seo/plugins.js | description": {"message": "Sökmotorer kan inte indexera plugin-innehåll och många enheter begränsar plugin-program eller stöder dem inte. [<PERSON><PERSON><PERSON> mer om hur du undviker plugin-program](https://developer.chrome.com/docs/lighthouse/seo/plugins/)."}, "core/audits/seo/plugins.js | failureTitle": {"message": "Dokumentet använder plugin-program"}, "core/audits/seo/plugins.js | title": {"message": "Dokumentet undviker plugin-program"}, "core/audits/seo/robots-txt.js | description": {"message": "Om robots.txt-filen har felaktigt format kan sökrobotarna inte förstå hur du vill att din webbplats ska genomsökas eller indexeras. [Läs mer om robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/)."}, "core/audits/seo/robots-txt.js | displayValueHttpBadCode": {"message": "Begäran om robots.txt returnerade HTTP-status: {statusCode}"}, "core/audits/seo/robots-txt.js | displayValueValidationError": {"message": "{itemCount,plural, =1{Ett fel hittades}other{# fel hittades}}"}, "core/audits/seo/robots-txt.js | explanation": {"message": "Lighthouse kunde inte ladda ned en robots.txt-fil"}, "core/audits/seo/robots-txt.js | failureTitle": {"message": "robots.txt är inte giltig"}, "core/audits/seo/robots-txt.js | title": {"message": "robots.txt är giltig"}, "core/audits/seo/tap-targets.js | description": {"message": "Interaktiva element som knappar och länkar ska vara tillräckligt stora (48 × 48 pixlar) och ha tillräckligt mycket utrymme runt omkring för att vara lätta att trycka på utan att överlappa andra element. [<PERSON><PERSON><PERSON> mer om tryckytor](https://developer.chrome.com/docs/lighthouse/seo/tap-targets/)."}, "core/audits/seo/tap-targets.js | displayValue": {"message": "{decimalProportion, number, percent} av tryckmålen har lämplig storlek"}, "core/audits/seo/tap-targets.js | explanationViewportMetaNotOptimized": {"message": "Tryckmålen är för små eftersom det inte finns någon metatagg för visningsområde som är optimerad för mobilskärmar"}, "core/audits/seo/tap-targets.js | failureTitle": {"message": "Tryckmålen har inte lämplig storlek"}, "core/audits/seo/tap-targets.js | overlappingTargetHeader": {"message": "Överlappande tryckmål"}, "core/audits/seo/tap-targets.js | tapTargetHeader": {"message": "<PERSON>ckm<PERSON><PERSON>"}, "core/audits/seo/tap-targets.js | title": {"message": "Tryckm<PERSON><PERSON> har lämplig storlek"}, "core/audits/server-response-time.js | description": {"message": "Se till att serverns svarstid för huvuddokumentet är kort, eftersom alla andra förfrågningar är beroende av det. [<PERSON><PERSON><PERSON> mer om mätvärdet Time to First Byte](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/)."}, "core/audits/server-response-time.js | displayValue": {"message": "Rotdokumentet tog {timeInMs, number, milliseconds} ms"}, "core/audits/server-response-time.js | failureTitle": {"message": "Minska serverns första svarstid"}, "core/audits/server-response-time.js | title": {"message": "Serverns första svarstid var kort"}, "core/audits/service-worker.js | description": {"message": "Tjänstefunktioner är en teknik som gör det möjligt att använda flera funktioner för progressiva webbappar i appen, till exempel offlineanvändning, pushmeddelanden och att lägga till den på startskärmen. [<PERSON><PERSON><PERSON> mer om tjänstefunktioner](https://developer.chrome.com/docs/lighthouse/pwa/service-worker/)."}, "core/audits/service-worker.js | explanationBadManifest": {"message": "<PERSON>an styrs av en tjänstefunktion, men `start_url` hittades inte eftersom det inte gick att analysera manifestet som giltigt JSON-format."}, "core/audits/service-worker.js | explanationBadStartUrl": {"message": "Den här sidan styrs av en tjänstefunktion, men `start_url` ({startUrl}) är inte inom tjänstefunktionens omfattning ({scopeUrl})"}, "core/audits/service-worker.js | explanationNoManifest": {"message": "<PERSON><PERSON> styrs av en tjänstefunktion, men `start_url` hittades inte eftersom inget manifest hämtades."}, "core/audits/service-worker.js | explanationOutOfScope": {"message": "Ursp<PERSON>et har en eller flera tjänstefunktioner, men sidan ({pageUrl}) är inte inom omfattningen."}, "core/audits/service-worker.js | failureTitle": {"message": "Registrerar inte en tjänstefunktion som styr sidan och `start_url`"}, "core/audits/service-worker.js | title": {"message": "Registrerar en tjänstefunktion som styr sidan och `start_url`"}, "core/audits/splash-screen.js | description": {"message": "Med hjälp av en välkomstskärm med ett tema som visas när användarna startar appen på startskärmen kan du se till att de får en bra upplevelse. [Läs mer om välkomstskärmar](https://developer.chrome.com/docs/lighthouse/pwa/splash-screen/)."}, "core/audits/splash-screen.js | failureTitle": {"message": "Har inte konfigurerats för en anpassad välkomstskärm"}, "core/audits/splash-screen.js | title": {"message": "Konfigurerad för en anpassad välkomstskärm"}, "core/audits/themed-omnibox.js | description": {"message": "Det går att ändra temat för webbläsarens adressfält så att det matchar din webbplats. [<PERSON><PERSON><PERSON> mer om att ändra temat för adressfältet](https://developer.chrome.com/docs/lighthouse/pwa/themed-omnibox/)."}, "core/audits/themed-omnibox.js | failureTitle": {"message": "Anger inte ett färgtema för adressfältet."}, "core/audits/themed-omnibox.js | title": {"message": "Anger ett färgtema för adressfältet."}, "core/audits/third-party-facades.js | categoryCustomerSuccess": {"message": "{productName} (framgångshistorier)"}, "core/audits/third-party-facades.js | categoryMarketing": {"message": "{productName} (marknadsföring)"}, "core/audits/third-party-facades.js | categorySocial": {"message": "{productName} (socialt)"}, "core/audits/third-party-facades.js | categoryVideo": {"message": "{productName} (video)"}, "core/audits/third-party-facades.js | columnProduct": {"message": "Produkt"}, "core/audits/third-party-facades.js | description": {"message": "Vissa inbäddningar från tredje part har stöd för uppskjuten inläsning. Du kan ersätta dem med en fasad tills de behövs. [Läs om hur du skjuter upp inbäddningar från tredje part med en fasad](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/)."}, "core/audits/third-party-facades.js | displayValue": {"message": "{itemCount,plural, =1{# fasadalternativ är tillgängligt}other{# fasadalternativ är tillgängliga}}"}, "core/audits/third-party-facades.js | failureTitle": {"message": "En del resurser från tredje part kan läsas in med lat inläsning med hjälp av en fasad"}, "core/audits/third-party-facades.js | title": {"message": "<PERSON><PERSON><PERSON> in resurser från tredje part med lat inläsning och fasader"}, "core/audits/third-party-summary.js | columnThirdParty": {"message": "Tredje part"}, "core/audits/third-party-summary.js | description": {"message": "Kod från tredje part kan påverka inläsningsprestandan betydligt. Begränsa antalet överflödiga tredjepartsleverantörer och testa att låta tredjepartskod läsas in efter att sidans huvudinneh<PERSON>ll har lästs in helt. [<PERSON><PERSON><PERSON> mer om hur du minimerar inverkan från tredje part](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/)."}, "core/audits/third-party-summary.js | displayValue": {"message": "Tredjepartskod blockerade huvudtråden i {timeInMs, number, milliseconds} ms"}, "core/audits/third-party-summary.js | failureTitle": {"message": "Minska påverkan från tredjepartskod"}, "core/audits/third-party-summary.js | title": {"message": "Minimera användning av tredjepartskod"}, "core/audits/timing-budget.js | columnMeasurement": {"message": "<PERSON><PERSON><PERSON>"}, "core/audits/timing-budget.js | columnTimingMetric": {"message": "Mätvärden"}, "core/audits/timing-budget.js | description": {"message": "<PERSON><PERSON><PERSON> in en tidsbudget om du vill hålla ett öga på webbplatsens prestanda. Bra webbplatser läses in och svarar snabbt på användarens indatahändelser. [<PERSON><PERSON><PERSON> mer om prestandabudgetar](https://developers.google.com/web/tools/lighthouse/audits/budgets)."}, "core/audits/timing-budget.js | title": {"message": "Tidsbudget"}, "core/audits/unsized-images.js | description": {"message": "Reducera layoutförskjutningarna och förbättra CLS genom att ange explicit bredd och höjd för bildelement. [<PERSON><PERSON><PERSON> mer om hur du ställer in bildmått](https://web.dev/optimize-cls/#images-without-dimensions)"}, "core/audits/unsized-images.js | failureTitle": {"message": "Alla bildelement har inte `width` och `height`"}, "core/audits/unsized-images.js | title": {"message": "<PERSON>a bilde<PERSON> har explicit `width` och `height`"}, "core/audits/user-timings.js | columnType": {"message": "<PERSON><PERSON>"}, "core/audits/user-timings.js | description": {"message": "Om du bygger in User Timing API i appen kan du mäta appens prestanda i realtid i samband med viktiga användarupplevelser. [Läs mer om User Timing-tidsstämplar](https://developer.chrome.com/docs/lighthouse/performance/user-timings/)."}, "core/audits/user-timings.js | displayValue": {"message": "{itemCount,plural, =1{1 användartimer}other{# användartimer}}"}, "core/audits/user-timings.js | title": {"message": "User Timing API – tidsstämplar och mått"}, "core/audits/uses-rel-preconnect.js | crossoriginWarning": {"message": "En `<link rel=preconnect>` hittad<PERSON> för {securityO<PERSON>in} men ignorerades av webbläsaren. Kontrollera att attributet `crossorigin` används korrekt."}, "core/audits/uses-rel-preconnect.js | description": {"message": "<PERSON>ä<PERSON> till signaler för `preconnect` eller `dns-prefetch` så att viktiga anslutningar till tredje part upprättas tidigt. [<PERSON><PERSON><PERSON> mer om hur du förhandsansluter till de ursprung som krävs](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/)."}, "core/audits/uses-rel-preconnect.js | title": {"message": "Föranslut till obligatoriska källor"}, "core/audits/uses-rel-preconnect.js | tooManyPreconnectLinksWarning": {"message": "Fler än 2 `<link rel=preconnect>`-anslutningar hittades. Dessa ska användas sparsamt och bara med de viktigaste källorna."}, "core/audits/uses-rel-preconnect.js | unusedWarning": {"message": "En `<link rel=preconnect>` hittad<PERSON> för {securityO<PERSON>in} men ignorerades av webbläsaren. Använd endast `preconnect` för viktiga ursprung som sidan säkerligen kommer att begära."}, "core/audits/uses-rel-preload.js | crossoriginWarning": {"message": "En `<link>` som läses in i förväg hittades för {preloadURL} men ignorerades av webbläsaren. Kontrollera att attributet `crossorigin` används korrekt."}, "core/audits/uses-rel-preload.js | description": {"message": "Det kan vara bra att använda `<link rel=preload>` för att prioritera hämtning av resurser som kommer att begäras senare i sidinläsningen. [L<PERSON><PERSON> mer om hur du läser in viktiga förfrågningar i förväg](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preload/)."}, "core/audits/uses-rel-preload.js | title": {"message": "<PERSON><PERSON><PERSON> in viktiga resurser i förväg"}, "core/audits/valid-source-maps.js | columnMapURL": {"message": "Kartans webbadress"}, "core/audits/valid-source-maps.js | description": {"message": "Källkartor översätter minifierad kod till den ursprungliga källkoden. Detta hjälper utvecklare att felsöka i produktionsläget. Lighthouse kan dessutom ge ytterligare insikter. Överväg att implementera källkartor för att dra nytta av dessa fördelar. [<PERSON><PERSON><PERSON> mer om källkartor](https://developer.chrome.com/docs/devtools/javascript/source-maps/)."}, "core/audits/valid-source-maps.js | failureTitle": {"message": "Det saknas källkartor för stor JavaScript från första part"}, "core/audits/valid-source-maps.js | missingSourceMapErrorMessage": {"message": "En källkarta saknas för en stor JavaScript-fil"}, "core/audits/valid-source-maps.js | missingSourceMapItemsWarningMesssage": {"message": "{missingItems,plural, =1{Varning: 1 element saknas i `.sourcesContent`}other{Varning: # element saknas i `.sourcesContent`}}"}, "core/audits/valid-source-maps.js | title": {"message": "Sidan har giltiga källkartor"}, "core/audits/viewport.js | description": {"message": "En `<meta name=\"viewport\">` optimerar appen för mobila skärmstorlekar och förhindrar även [en fördröjning på 300 millisekunder vid indata från användare](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [<PERSON><PERSON><PERSON> mer om hur du använder metataggen för visningsområdet](https://developer.chrome.com/docs/lighthouse/pwa/viewport/)."}, "core/audits/viewport.js | explanationNoTag": {"message": "Ingen `<meta name=\"viewport\">`-tagg hittades"}, "core/audits/viewport.js | failureTitle": {"message": "Har inte en `<meta name=\"viewport\">`-tagg med `width` eller `initial-scale`"}, "core/audits/viewport.js | title": {"message": "Har en `<meta name=\"viewport\">`-tagg med `width` eller `initial-scale`"}, "core/audits/work-during-interaction.js | description": {"message": "Det här är det trådblockerande arbetet som sker under mätningen av interaktion till nästa uppritning. [Läs mer om mätvärdet Interakation till nästa uppritning](https://web.dev/inp/)."}, "core/audits/work-during-interaction.js | displayValue": {"message": "H<PERSON><PERSON><PERSON>en {interactionType} behandlades i {timeInMs, number, milliseconds} ms"}, "core/audits/work-during-interaction.js | eventTarget": {"message": "Händelsemål"}, "core/audits/work-during-interaction.js | failureTitle": {"message": "Minimera arbetet under viktiga interaktioner"}, "core/audits/work-during-interaction.js | inputDelay": {"message": "Inmatningsfördröjning"}, "core/audits/work-during-interaction.js | presentationDelay": {"message": "Presentations<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "core/audits/work-during-interaction.js | processingTime": {"message": "Bearbetningstid"}, "core/audits/work-during-interaction.js | title": {"message": "Minimerar arbetet under viktiga interaktioner"}, "core/config/default-config.js | a11yAriaGroupDescription": {"message": "Det här är förslag på hur ARIA kan förbättras i appen så att den fungerar bättre för den som använder skärmläsare eller andra h<PERSON>."}, "core/config/default-config.js | a11yAriaGroupTitle": {"message": "ARIA"}, "core/config/default-config.js | a11yAudioVideoGroupDescription": {"message": "Det här är möjligheter att tillhandahålla alternativt innehåll för ljud och video. Detta kan förbättra upplevelsen för användare med nedsatt syn eller hörsel."}, "core/config/default-config.js | a11yAudioVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> och bild"}, "core/config/default-config.js | a11yBestPracticesGroupDescription": {"message": "Dessa punkter visar bra metoder för vanliga hjälpmedel."}, "core/config/default-config.js | a11yBestPracticesGroupTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yCategoryDescription": {"message": "Dessa kontroller visar möjligheter att [förbättra tillgängligheten för din webbapp](https://developer.chrome.com/docs/lighthouse/accessibility/). Alla tillgänglighetsproblem kan inte identifieras automatiskt. Du bör därför även testa manuellt."}, "core/config/default-config.js | a11yCategoryManualDescription": {"message": "Dessa punkter beskriver områden som inte kan testas automatiskt. Läs mer i vår guide om att [granska tillgängligheten](https://web.dev/how-to-review/)."}, "core/config/default-config.js | a11yCategoryTitle": {"message": "Tillgänglighet"}, "core/config/default-config.js | a11yColorContrastGroupDescription": {"message": "Det här är förslag på hur du kan göra innehållet lättare att läsa."}, "core/config/default-config.js | a11yColorContrastGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | a11yLanguageGroupDescription": {"message": "Det här är förslag på hur du kan göra det lättare för användare med olika språkinställningar att tolka innehållet."}, "core/config/default-config.js | a11yLanguageGroupTitle": {"message": "Internationalisering och lokalisering"}, "core/config/default-config.js | a11yNamesLabelsGroupDescription": {"message": "Det här är förslag på hur du kan göra det tydligare vad olika objekt i appens gränssnitt är. Det kan förenkla för den som använder skärmläsare eller andra hj<PERSON>."}, "core/config/default-config.js | a11yNamesLabelsGroupTitle": {"message": "<PERSON><PERSON> och etiketter"}, "core/config/default-config.js | a11yNavigationGroupDescription": {"message": "Det här är möjligheter att förbättra tangentbordsnavigeringen i appen."}, "core/config/default-config.js | a11yNavigationGroupTitle": {"message": "Navigering"}, "core/config/default-config.js | a11yTablesListsVideoGroupDescription": {"message": "Det här är förslag till att förbättra upplevelsen av att läsa tabeller eller listor med skärmläsare eller annan hjälpmedelsteknik."}, "core/config/default-config.js | a11yTablesListsVideoGroupTitle": {"message": "<PERSON><PERSON><PERSON> och listor"}, "core/config/default-config.js | bestPracticesBrowserCompatGroupTitle": {"message": "Kompatibilitet med webbläsare"}, "core/config/default-config.js | bestPracticesCategoryTitle": {"message": "<PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesGeneralGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "core/config/default-config.js | bestPracticesTrustSafetyGroupTitle": {"message": "Tillförlitlighet och säkerhet"}, "core/config/default-config.js | bestPracticesUXGroupTitle": {"message": "Användarupplevelse"}, "core/config/default-config.js | budgetsGroupDescription": {"message": "Prestandabudget anger standard för webbplatsens prestanda."}, "core/config/default-config.js | budgetsGroupTitle": {"message": "Budgetar"}, "core/config/default-config.js | diagnosticsGroupDescription": {"message": "Mer information om appens prestanda. Värdena påverkar inte prestandapoängen [direkt](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | diagnosticsGroupTitle": {"message": "Diagnostik"}, "core/config/default-config.js | firstPaintImprovementsGroupDescription": {"message": "Den viktigaste delen av sidans prestanda är hur snabbt pixlarna renderas på skärmen. Viktiga mätvärden: Första uppritningen av innehåll, <PERSON><PERSON>rsta meningsfulla skärmuppritningen"}, "core/config/default-config.js | firstPaintImprovementsGroupTitle": {"message": "Förbättringar av första skärmuppritningen"}, "core/config/default-config.js | loadOpportunitiesGroupDescription": {"message": "Dessa förslag kan hjälpa sidan att läsas in snabbare. De påverkar inte prestandavärdet [direkt](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/)."}, "core/config/default-config.js | loadOpportunitiesGroupTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>gh<PERSON>"}, "core/config/default-config.js | metricGroupTitle": {"message": "Mätvärden"}, "core/config/default-config.js | overallImprovementsGroupDescription": {"message": "Förbättra inläsningstiden överlag så att sidan upplevs som responsiv och blir klar att använda så snabbt som möjligt. Viktiga mätvärden: Tid till interaktivt tillstånd, Hastighetsindex"}, "core/config/default-config.js | overallImprovementsGroupTitle": {"message": "Övergripande förbättringar"}, "core/config/default-config.js | performanceCategoryTitle": {"message": "Prestanda"}, "core/config/default-config.js | pwaCategoryDescription": {"message": "Med dessa kontroller verifieras att webbplatsen är en progressiv webbapp. [<PERSON><PERSON>s mer om vad som gör en progressiv webbapp bra](https://web.dev/pwa-checklist/)."}, "core/config/default-config.js | pwaCategoryManualDescription": {"message": "<PERSON>sa kontroller krävs enligt [checklistan för progressiva webbappar](https://web.dev/pwa-checklist/) som används som baslinje, men de kontrolleras inte automatiskt av Lighthouse. De påverkar inte resultatet, men det är viktigt att du verifierar dem manuellt."}, "core/config/default-config.js | pwaCategoryTitle": {"message": "PWA"}, "core/config/default-config.js | pwaInstallableGroupTitle": {"message": "<PERSON>n <PERSON>era<PERSON>"}, "core/config/default-config.js | pwaOptimizedGroupTitle": {"message": "Optimerad för progressiv webbapp"}, "core/config/default-config.js | seoCategoryDescription": {"message": "<PERSON><PERSON> kontroller ser till att din sida följer de grundläggande råden för sökmotoroptimering. Det finns fler faktorer som Lighthouse inte bedömer här som kan påverka rankningen i sökresultat, däribland prestandan för [Viktiga webbvärden](https://web.dev/learn-core-web-vitals/). [<PERSON><PERSON><PERSON> mer om Google Search Essentials](https://support.google.com/webmasters/answer/35769)."}, "core/config/default-config.js | seoCategoryManualDescription": {"message": "Kontrollera att fler av de bästa metoderna för sökmotoroptimering följs på din webbplats genom att köra dessa extra valideringar."}, "core/config/default-config.js | seoCategoryTitle": {"message": "SEO"}, "core/config/default-config.js | seoContentGroupDescription": {"message": "Formatera HTML-koden på ett sätt som gör det enklare för sökrobotar att tolka appens innehåll."}, "core/config/default-config.js | seoContentGroupTitle": {"message": "<PERSON><PERSON><PERSON> metoder för <PERSON>"}, "core/config/default-config.js | seoCrawlingGroupDescription": {"message": "Du måste ge sökrobotar tillgång till appen om den ska kunna visas i sökresultaten."}, "core/config/default-config.js | seoCrawlingGroupTitle": {"message": "Genomsökning och indexering"}, "core/config/default-config.js | seoMobileGroupDescription": {"message": "G<PERSON>r sidorna mobilanpassade så att användarna kan läsa dem utan att behöva nypa eller zooma in. [<PERSON><PERSON><PERSON> mer om hur du gör sidor mobilanpassade](https://developers.google.com/search/mobile-sites/)."}, "core/config/default-config.js | seoMobileGroupTitle": {"message": "Mobilanpassad"}, "core/gather/driver/environment.js | warningSlowHostCpu": {"message": "Enheten som testas verkar ha en lägre CPU-hastighet än vad som förväntas i Lighthouse. Det här kan påverka prestandavärdet negativt. Läs mer om att [kalibrera en lämplig CPU-multiplicerare](https://github.com/GoogleChrome/lighthouse/blob/main/docs/throttling.md#cpu-throttling)."}, "core/gather/driver/navigation.js | warningRedirected": {"message": "<PERSON><PERSON> kanske inte läses in eftersom testwebbadressen ({requested}) omdirigerades till {final}. <PERSON>a den andra webbadressen direkt i stället."}, "core/gather/driver/navigation.js | warningTimeout": {"message": "<PERSON><PERSON> l<PERSON> in för långsamt för att slutföra sidhämtningen inom tidsgränsen. Resultatet kan vara ofullständigt."}, "core/gather/driver/storage.js | warningCacheTimeout": {"message": "Rensningen av webbläsarens cachelagring överskred tidsgränsen. Testa att granska sidan igen och skicka en felrapport om problemet kvarstår."}, "core/gather/driver/storage.js | warningData": {"message": "{locationCount,plural, =1{På följande plats kan det finnas lagrad data som påverkar inläsningen: {locations}. Granska sidan i ett inkognitofönster för att förhindra att resurserna påverkar dina resultat.}other{På följande platser kan det finnas lagrad data som påverkar inläsningen: {locations}. Granska sidan i ett inkognitofönster för att förhindra att resurserna påverkar dina resultat.}}"}, "core/gather/driver/storage.js | warningOriginDataTimeout": {"message": "Rensningen av ursprungsdata överskred tidsgränsen. Testa att granska sidan igen och skicka en felrapport om problemet kvarstår."}, "core/lib/bf-cache-strings.js | HTTPMethodNotGET": {"message": "Bara sidor som lästs in via en GET-begäran kan placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | HTTPStatusNotOK": {"message": "Bara sidor med statuskoden 2XX kan cachelagras."}, "core/lib/bf-cache-strings.js | JavaScriptExecution": {"message": "Ett försök att köra JavaScript i cacheminnet upptäcktes."}, "core/lib/bf-cache-strings.js | appBanner": {"message": "<PERSON>or som har begärt en AppBanner kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabled": {"message": "Vilocacheminnet har inaktiverats med hjälp av en flagga. Öppna chrome://flags/#back-forward-cache om du vill aktivera det lokalt på enheten."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByCommandLine": {"message": "Vilocacheminnet har inaktiverats från kommandoraden."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledByLowMemory": {"message": "Vilocacheminnet har inaktiverats därför att minnet inte räckte till."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForDelegate": {"message": "Vilocacheminnet stöds inte vid delegering."}, "core/lib/bf-cache-strings.js | backForwardCacheDisabledForPrerender": {"message": "Vilocacheminnet har inaktiverats för fö<PERSON>."}, "core/lib/bf-cache-strings.js | broadcastChannel": {"message": "<PERSON><PERSON> kan inte cachelagras eftersom den har en BroadcastChannel-instans med registrerade lyssnare."}, "core/lib/bf-cache-strings.js | cacheControlNoStore": {"message": "Sidor med fältet cache-control:no-store i huvudet kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | cacheFlushed": {"message": "Cacheminnet rensades avsiktligen."}, "core/lib/bf-cache-strings.js | cacheLimit": {"message": "<PERSON><PERSON> togs bort från cacheminnet så att en annan sida skulle kunna cachelagras."}, "core/lib/bf-cache-strings.js | containsPlugins": {"message": "<PERSON>or som använder en plugin kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentFileChooser": {"message": "Sidor som använder FileChooser API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentFileSystemAccess": {"message": "Sidor som använder File System Access API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentMediaDevicesDispatcherHost": {"message": "Sidor som använder Media Device Dispatcher kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentMediaPlay": {"message": "Uppspelning pågick i en mediespelare när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | contentMediaSession": {"message": "Sidor som använder MediaSession API och ställer in en uppspelningsstatus kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentMediaSessionService": {"message": "Sidor som använder MediaSession API och ställer in åtgärdshanterare kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentScreenReader": {"message": "Vilocacheminne har inaktiverats på grund av att skärmläsare används."}, "core/lib/bf-cache-strings.js | contentSecurityHandler": {"message": "Sidor som använder SecurityHandler kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentSerial": {"message": "Sidor som använder Serial API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentWebAuthenticationAPI": {"message": "Sidor som använder WebAuthentication API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentWebBluetooth": {"message": "Sidor som använder WebBluetooth API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | contentWebUSB": {"message": "Sidor som använder WebUSB API kan inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | dedicatedWorkerOrWorklet": {"message": "Sidor som använder en dedikerad tjänstefunktion eller worklet kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | documentLoaded": {"message": "Dokumentet lästes inte in helt innan användaren navigerade bort från det."}, "core/lib/bf-cache-strings.js | embedderAppBannerManager": {"message": "<PERSON><PERSON><PERSON><PERSON> visades när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderChromePasswordManagerClientBindCredentialManager": {"message": "Chrome <PERSON>tering kördes när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderDomDistillerSelfDeletingRequestDelegate": {"message": "DOM-destillering pågick när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderDomDistillerViewerSource": {"message": "DOM Distiller Viewer kördes när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderExtensionMessaging": {"message": "Vilocacheminnet har inaktiverats på grund av att tillägg använder API:et för meddelanden."}, "core/lib/bf-cache-strings.js | embedderExtensionMessagingForOpenPort": {"message": "Tillägg med långvarig anslutning ska koppla från innan lagras i vilocacheminne."}, "core/lib/bf-cache-strings.js | embedderExtensionSentMessageToCachedFrame": {"message": "Tillägg med långvarig anslutning försökte skicka meddelanden till bildrutor i vilocacheminnet."}, "core/lib/bf-cache-strings.js | embedderExtensions": {"message": "Vilocacheminnet har inaktiverats på grund av till<PERSON>gg."}, "core/lib/bf-cache-strings.js | embedderModalDialog": {"message": "En modal dialogruta, till exempel en återsändning av ett formulär eller en dialogruta för http-autentisering, visades när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderOfflinePage": {"message": "Offlinesidan visades när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderOomInterventionTabHelper": {"message": "Fältet Out-Of-Memory Intervention visades när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderPermissionRequestManager": {"message": "Det fanns en aktiv begäran om behörighet när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderPopupBlockerTabHelper": {"message": "Popupblockerare kördes när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingThreatDetails": {"message": "Information från <PERSON> webbsökning visades när användaren navigerade bort från sidan."}, "core/lib/bf-cache-strings.js | embedderSafeBrowsingTriggeredPopupBlocker": {"message": "Säker webbsökning identifierade den här sidan som olämplig och blockerade popupfönstret."}, "core/lib/bf-cache-strings.js | enteredBackForwardCacheBeforeServiceWorkerHostAdded": {"message": "En tjänstefunktion aktiverades medan sidan lagrades i vilocacheminnet."}, "core/lib/bf-cache-strings.js | errorDocument": {"message": "Vilocacheminnet har inaktiverats på grund av dokumentfel."}, "core/lib/bf-cache-strings.js | fencedFramesEmbedder": {"message": "Det går inte att lagra sidor med FencedFrames i bfcache."}, "core/lib/bf-cache-strings.js | foregroundCacheLimit": {"message": "<PERSON><PERSON> togs bort från cacheminnet så att en annan sida skulle kunna cachelagras."}, "core/lib/bf-cache-strings.js | grantedMediaStreamAccess": {"message": "Sidor med åtkomstbehörighet till en mediestream kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | haveInnerContents": {"message": "<PERSON>or som använder en portal kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | idleManager": {"message": "Sidor som använder IdleManager kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | indexedDBConnection": {"message": "Sidor med en öppen IndexedDB-anslutning kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | ineligibleAPI": {"message": "Otillåtna API:er användes."}, "core/lib/bf-cache-strings.js | injectedJavascript": {"message": "Sidor med ett JavaScript som injicerats av ett tillägg kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | injectedStyleSheet": {"message": "Sidor med ett StyleSheet som injicerats av ett tillägg kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | internalError": {"message": "Internt fel."}, "core/lib/bf-cache-strings.js | keepaliveRequest": {"message": "Vilocacheminnet har inaktiverats på grund av en keepalive-begäran."}, "core/lib/bf-cache-strings.js | keyboardLock": {"message": "<PERSON><PERSON> som använder tangentbordslås kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | loading": {"message": "<PERSON><PERSON> hade inte lästs in helt innan användaren navigerade bort från den."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoCache": {"message": "Sidor vars huvudresurs har cache-control:no-cache kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | mainResourceHasCacheControlNoStore": {"message": "Sidor vars huvudresurs har cache-control:no-store kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | navigationCancelledWhileRestoring": {"message": "Navigeringen avbröts innan sidan kunde återställas från vilocacheminnet."}, "core/lib/bf-cache-strings.js | networkExceedsBufferLimit": {"message": "Sidan togs bort från cacheminnet eftersom för mycket data skickades till den via en aktiv nätverksanslutning. Det finns en gräns för hur mycket data en cachelagrad sida får ta emot i Chrome."}, "core/lib/bf-cache-strings.js | networkRequestDatapipeDrainedAsBytesConsumer": {"message": "Sidor med utestående fetch() eller XHR kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | networkRequestRedirected": {"message": "Sidan togs bort från vilocacheminnet eftersom en aktiv nätverksbegäran medförde en omdirigering."}, "core/lib/bf-cache-strings.js | networkRequestTimeout": {"message": "Sidan togs bort från cacheminnet eftersom en nätverksanslutning förblev öppen för länge. Det finns en gräns för hur länge en cachelagrad sida får ta emot data i Chrome."}, "core/lib/bf-cache-strings.js | noResponseHead": {"message": "<PERSON>or utan giltigt svarshuvud kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | notMainFrame": {"message": "Navigeringen gjordes i en annan ram än den överordnade ramen."}, "core/lib/bf-cache-strings.js | outstandingIndexedDBTransaction": {"message": "Sidor med pågående transaktioner i en indexerad databas kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestDirectSocket": {"message": "Sidor med en utestående nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestFetch": {"message": "Sidor med en utestående hämtningsbegäran i nätverket kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestOthers": {"message": "Sidor med en utestående nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | outstandingNetworkRequestXHR": {"message": "Sidor med en utestående XHR-nätverksbegäran kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | paymentManager": {"message": "Sidor som använder PaymentManager kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | pictureInPicture": {"message": "<PERSON>or som använder bild-i-bild kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | portal": {"message": "<PERSON>or som använder en portal kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | printing": {"message": "Sidor där användargränssnittet för utskrift visas kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | relatedActiveContentsExist": {"message": "<PERSON><PERSON> ö<PERSON>nades med `window.open()` och en annan flik refererar till den, eller också öppnade sidan ett fönster."}, "core/lib/bf-cache-strings.js | rendererProcessCrashed": {"message": "Renderingen av sidan i vilocacheminnet kraschade."}, "core/lib/bf-cache-strings.js | rendererProcessKilled": {"message": "Renderingsprocessen för sidan i vilocacheminnet avslutades."}, "core/lib/bf-cache-strings.js | requestedAudioCapturePermission": {"message": "Sidor som har begärt behörighet att spela in ljud kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedBackForwardCacheBlockedSensors": {"message": "Sidor som har begärt sensorbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedBackgroundWorkPermission": {"message": "Sidor som har begärt synkronisering i bakgrunden eller hämtningsbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedMIDIPermission": {"message": "Sidor som har begärt MIDI-behörighet kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedNotificationsPermission": {"message": "Sidor som har begärt aviseringsbehörighet kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedStorageAccessGrant": {"message": "Sidor som har begärt åtkomst till lagringsutrymme kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | requestedVideoCapturePermission": {"message": "Sidor som har begärt behörighet till videoinspelning kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | schemeNotHTTPOrHTTPS": {"message": "Bara sidor med webbprotokollet HTTP eller HTTPS kan cachelagras."}, "core/lib/bf-cache-strings.js | serviceWorkerClaim": {"message": "En tjänstefunktion gjorde anspråk på sidan medan den lagrades i vilocacheminnet."}, "core/lib/bf-cache-strings.js | serviceWorkerPostMessage": {"message": "En tjänstefunktion försökte skicka `MessageEvent` till sidan i vilocacheminnet."}, "core/lib/bf-cache-strings.js | serviceWorkerUnregistration": {"message": "En ServiceWorker avregistrerades medan sidan lagrades i vilocacheminnet."}, "core/lib/bf-cache-strings.js | serviceWorkerVersionActivation": {"message": "<PERSON><PERSON> togs bort från vilocacheminnet därför att en tjänstefunktion aktiverades."}, "core/lib/bf-cache-strings.js | sessionRestored": {"message": "Chrome har startats om och vilocacheminnet nollställts."}, "core/lib/bf-cache-strings.js | sharedWorker": {"message": "Sidor som använder SharedWorker kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | speechRecognizer": {"message": "Sidor som använder SpeechRecognizer kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | speechSynthesis": {"message": "<PERSON><PERSON> som använder SpeechSynthesis kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | subframeIsNavigating": {"message": "En iframe på sidan initierade en navigering som aldrig slutfördes."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoCache": {"message": "Sidor som har en underresurs med cache-control:no-cache kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | subresourceHasCacheControlNoStore": {"message": "Sidor som har en underresurs med cache-control:no-store kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | timeout": {"message": "Sidan finns inte kvar eftersom tidsgränsen för lagring i vilocacheminnet överskreds."}, "core/lib/bf-cache-strings.js | timeoutPuttingInCache": {"message": "Tidsgränsen överskreds när sidan skulle lagras i vilocacheminnet (troligen på grund av en pagehide-hanterare som kördes under lång tid)."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInMainFrame": {"message": "Det finns en unload-hanterare i sidans överordnade ram."}, "core/lib/bf-cache-strings.js | unloadHandlerExistsInSubFrame": {"message": "<PERSON><PERSON> har en unload-ha<PERSON>are i en underordnad ram."}, "core/lib/bf-cache-strings.js | userAgentOverrideDiffers": {"message": "Webbläsaren har ändrat fältet för åsidosättning av användaragent."}, "core/lib/bf-cache-strings.js | wasGrantedMediaAccess": {"message": "<PERSON>or med behörighet att spela in video eller ljud kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webDatabase": {"message": "Sidor som använder WebDatabase kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webHID": {"message": "Sidor som använder WebHID kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webLocks": {"message": "Sidor som använder WebLocks kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webNfc": {"message": "Sidor som använder WebNfc kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webOTPService": {"message": "Sidor som använder WebOTPService kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webRTC": {"message": "Sidor med WebRTC kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webShare": {"message": "Sidor som använder WebShare kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webSocket": {"message": "Sidor med WebSocket kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webTransport": {"message": "Sidor med WebTransport kan inte lagras i vilocacheminnet."}, "core/lib/bf-cache-strings.js | webXR": {"message": "Sidor som använder WebXR kan för närvarande inte placeras i vilocacheminnet."}, "core/lib/csp-evaluator.js | allowlistFallback": {"message": "Du kan lägga till webbadressscheman https: och http: (ignoreras av webbläsare med stöd för strict-dynamic) för bakåtkompatibilitet med äldre webbläsare."}, "core/lib/csp-evaluator.js | deprecatedDisownOpener": {"message": "disown-opener har utfasats sedan CSP3. Använd huvudet Cross-Origin-Opener-Policy i stället."}, "core/lib/csp-evaluator.js | deprecatedReferrer": {"message": "referrer har utfasats sedan CSP2. Använd huvudet Referrer-Policy i stället."}, "core/lib/csp-evaluator.js | deprecatedReflectedXSS": {"message": "reflected-xss har utfasats sedan CSP2. Använd huvudet X-XSS-Protection i stället."}, "core/lib/csp-evaluator.js | missingBaseUri": {"message": "Om base-uri saknas går det att använda <base>-tag<PERSON> för att ställa in baswebbadressen för alla relativa webbadresser (t.ex. skript) till en domän som styrs av en angripare. Du kan ställa in base-uri på none eller self."}, "core/lib/csp-evaluator.js | missingObjectSrc": {"message": "Om object-src saknas går det att använda plugins som kör osäkra skript. Du kan ställa in object-src på none."}, "core/lib/csp-evaluator.js | missingScriptSrc": {"message": "Direktivet script-src saknas. Detta kan möjliggöra att osäkra skript körs."}, "core/lib/csp-evaluator.js | missingSemicolon": {"message": "Glömde du semikolon? {keyword} verkar vara ett direktiv, inte ett sökord."}, "core/lib/csp-evaluator.js | nonceCharset": {"message": "Noncevärden bör använda teckenuppsättningen base64."}, "core/lib/csp-evaluator.js | nonceLength": {"message": "Noncevärden måste bestå av minst åtta tecken."}, "core/lib/csp-evaluator.js | plainUrlScheme": {"message": "Undvik att använda vanliga webbadresscheman ({keyword}) i det här direktivet. Vanliga webbadresscheman gör det möjligt att hämta skript från en osäker domän."}, "core/lib/csp-evaluator.js | plainWildcards": {"message": "Undvik att använda vanliga jokertecken ({keyword}) i det här direktivet. Vanliga jokertecken gör det möjligt att hämta skript från en osäker domän."}, "core/lib/csp-evaluator.js | reportToOnly": {"message": "Rapporteringsdestinationen konfigureras enbart via direktivet report-to. Direktivet stöds endast i Chromium-baserade webbläsare och vi rekommenderar därför att du även använder direktivet report-uri."}, "core/lib/csp-evaluator.js | reportingDestinationMissing": {"message": "Det finns ingen CPS som konfigurerar en rapporteringsdestination. Detta gör det svårt att bevara CSP med tiden och övervaka eventuella intrång."}, "core/lib/csp-evaluator.js | strictDynamic": {"message": "Värdens godkännandelistor kan ofta passeras. Du kan i stället använda CSP-noncevärden eller hashvärden i kombination med strict-dynamic vid behov."}, "core/lib/csp-evaluator.js | unknownDirective": {"message": "Okänt CSP-direktiv."}, "core/lib/csp-evaluator.js | unknownKeyword": {"message": "{keyword} verkar vara ett ogiltigt sökord."}, "core/lib/csp-evaluator.js | unsafeInline": {"message": "unsafe-inline möjliggör att osäkra skript och händelsehanterare körs på sidan. Du kan använda CSP-noncevärden eller -hashvärden om du vill godkänna skript separat."}, "core/lib/csp-evaluator.js | unsafeInlineFallback": {"message": "Du kan lägga till unsafe-inline (ignoreras av webbläsare med stöd för noncevärden/hashvärden) för bakåtkompatibilitet med äldre webbläsare."}, "core/lib/deprecations-strings.js | authorizationCoveredByWildcard": {"message": "Auktorisering omfattas inte av jokertecknet (*) i CORS `Access-Control-Allow-Headers`-hantering."}, "core/lib/deprecations-strings.js | canRequestURLHTTPContainingNewline": {"message": "Resursförfrågningar vars webbadresser innehöll både de borttagna blankstegstecknen `(n|r|t)` och mindre än-tecknen (`<`) blockeras. Ta bort radbrytningstecken och koda mindre än-tecken från t.ex. värden för elementattribut om du vill läsa in dessa resurser."}, "core/lib/deprecations-strings.js | chromeLoadTimesConnectionInfo": {"message": "`chrome.loadTimes()` är utfasat. Använd det standardiserade API:et Navigation Timing 2 i stället."}, "core/lib/deprecations-strings.js | chromeLoadTimesFirstPaintAfterLoadTime": {"message": "`chrome.loadTimes()` är utfasat. Använd i stället det standardiserade API:et Paint Timing."}, "core/lib/deprecations-strings.js | chromeLoadTimesWasAlternateProtocolAvailable": {"message": "`chrome.loadTimes()` är utfasat. Använd det standardiserade API:et `nextHopProtocol` i Navigation Timing 2 i stället."}, "core/lib/deprecations-strings.js | cookieWithTruncatingChar": {"message": "Cookies som innehåller ett `(0|r|n)`-tecken avvisas i stället för att trunkeras."}, "core/lib/deprecations-strings.js | crossOriginAccessBasedOnDocumentDomain": {"message": "Funktionen för att lätta på principen gällande samma ursprung genom att ange `document.domain` är utfasad och inaktiveras som standard. Denna varning om utfasning gäller åtkomst via korsursprung som aktiverades genom inställning av `document.domain`."}, "core/lib/deprecations-strings.js | crossOriginWindowApi": {"message": "Aktivering av {PH1} fr<PERSON>n iframes för korsursprung har fasats ut och kommer att tas bort."}, "core/lib/deprecations-strings.js | cssSelectorInternalMediaControlsOverlayCastButton": {"message": "Attributet `disableRemotePlayback` ska användas för att inaktivera standardintegreringen av Cast i stället för väljaren `-internal-media-controls-overlay-cast-button`."}, "core/lib/deprecations-strings.js | deprecatedWithReplacement": {"message": "{PH1} är utfasat. Använd {PH2} i stället."}, "core/lib/deprecations-strings.js | deprecationExample": {"message": "Detta är ett exempel på ett översatt felmeddelande om utfasning."}, "core/lib/deprecations-strings.js | documentDomainSettingWithoutOriginAgentClusterHeader": {"message": "Funktionen för att lätta på principen gällande samma ursprung genom att ange `document.domain` är utfasad och inaktiveras som standard. Om du vill fortsätta använda den här funktionen ska du välja bort ursprungsbundna agentkluster genom att skicka en `Origin-Agent-Cluster: ?0`-rubrik tillsammans med HTTP-svaret för dokumentet och ramarna. Läs mer på https://developer.chrome.com/blog/immutable-document-domain/."}, "core/lib/deprecations-strings.js | eventPath": {"message": "`Event.path` är utfasat och kommer att tas bort. Använd `Event.composedPath()` i stället."}, "core/lib/deprecations-strings.js | expectCTHeader": {"message": "Huvudet `Expect-CT` är utfasat och kommer att tas bort. Chrome kräver certifikattransparens för alla offentligt betrodda certifikat som utfärdats efter den 30 april 2018."}, "core/lib/deprecations-strings.js | feature": {"message": "Du hittar mer information på sidan för funktionsstatus."}, "core/lib/deprecations-strings.js | geolocationInsecureOrigin": {"message": "`getCurrentPosition()` och `watchPosition()` fungerar inte längre i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | geolocationInsecureOriginDeprecatedNotRemoved": {"message": "`getCurrentPosition()` och `watchPosition()` är utfasade i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | getUserMediaInsecureOrigin": {"message": "`getUserMedia()` fungerar inte längre i osäkra ursprung. Om du vill använda den här funktionen rekommenderar vi att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | hostCandidateAttributeGetter": {"message": "`RTCPeerConnectionIceErrorEvent.hostCandidate` är utfasat. Använd `RTCPeerConnectionIceErrorEvent.address` eller `RTCPeerConnectionIceErrorEvent.port` i stället."}, "core/lib/deprecations-strings.js | identityInCanMakePaymentEvent": {"message": "Säljarursprunget och arbiträr data från tjänstefunktionens `canmakepayment`-händelse har fasats ut och tas bort: `topOrigin`, `paymentRequestOrigin`, `methodData`, `modifiers`."}, "core/lib/deprecations-strings.js | insecurePrivateNetworkSubresourceRequest": {"message": "Webbplatsen begärde en underresurs från ett nätverk som den enbart hade åtkomst till på grund av att användaren hade särskild nätverksbehörighet. Med dessa förfrågningar får enheter och servrar som inte är offentliga åtkomst till internet, vilket ökar risken för bedrägerier med begäran mellan webbplatser och/eller läckage av uppgifter. I syfte att minska dessa risker fasar Chrome ut förfrågningar till icke-offentliga underresurser som har startats i osäkra kontexter och kommer att blockera dem."}, "core/lib/deprecations-strings.js | localCSSFileExtensionRejected": {"message": "CSS går inte att läsas in från `file:`-webbadresser om de inte slutar med ett `.css`-filnamnstillägg."}, "core/lib/deprecations-strings.js | mediaSourceAbortRemove": {"message": "Användning av `SourceBuffer.abort()` i syfte att avbryta borttagning av asynkrona intervall med `remove()` har fasats ut på grund av en ändring av specifikationen. Stödet tas bort i framtiden. Lyssna efter händelsen `updateend` i stället. `abort()` ska endast användas till att avbryta en asynkron mediebilaga eller återställa tolkningsläget."}, "core/lib/deprecations-strings.js | mediaSourceDurationTruncatingBuffered": {"message": "Funktionen för att ställa in `MediaSource.duration` under den högsta visningstidsstämpeln för buffrade kodade ramar har fasats ut på grund av en ändring av specifikationen. Stödet för implicit borttagning av trunkerad buffrad media kommer att tas bort. Du ska i stället genomföra explicit `remove(newDuration, oldDuration)` för alla `sourceBuffers`, om `newDuration < oldDuration`."}, "core/lib/deprecations-strings.js | milestone": {"message": "Ändringen träder i kraft vid milstolpen {milestone}."}, "core/lib/deprecations-strings.js | noSysexWebMIDIWithoutPermission": {"message": "Webb-MIDI ber om behörighet om användning även om SysEx inte har angetts i `MIDIOptions`."}, "core/lib/deprecations-strings.js | notificationInsecureOrigin": {"message": "Notification API får inte längre användas från osäkra ursprung. Vi rekommenderar att du flyttar appen till ett säkert ursprung, till exempel HTTPS. Läs mer på https://goo.gle/chrome-insecure-origins."}, "core/lib/deprecations-strings.js | notificationPermissionRequestedIframe": {"message": "Behörighet för aviserings-API:et kan inte längre begäras via en iframe för korsursprung. Vi rekommenderar att du begär behörighet från en ram på toppnivå eller öppnar ett nytt fönster i stället."}, "core/lib/deprecations-strings.js | obsoleteWebRtcCipherSuite": {"message": "Partnern förhandlar med en föråldrad version av (D)TLS. Kontakta partnern om att åtgärda detta."}, "core/lib/deprecations-strings.js | openWebDatabaseInsecureContext": {"message": "WebSQL i osäkra sammanhang har fasats ut och kommer snart att tas bort. Använd webblagring eller indexerade databaser."}, "core/lib/deprecations-strings.js | overflowVisibleOnReplacedElement": {"message": "Om du anger `overflow: visible` för img-, video- och canvas-taggar kan det leda till att visuellt innehåll visas utanför elementets gränser. Läs mer på https://github.com/WICG/shared-element-transitions/blob/main/debugging_overflow_on_images.md."}, "core/lib/deprecations-strings.js | paymentInstruments": {"message": "`paymentManager.instruments` har fasats ut. Använd just-in-time-installation för betalningshanterare i stället."}, "core/lib/deprecations-strings.js | paymentRequestCSPViolation": {"message": "`PaymentRequest`-anropet åsidosatte CSP-direktivet `connect-src`. Denna åsidosättning är utfasad. Lägg till betalningsmetodens identifierare från `PaymentRequest` API (i fältet `supportedMethods`) i CSP-direktivet `connect-src`."}, "core/lib/deprecations-strings.js | persistentQuotaType": {"message": "`StorageType.persistent` är utfasad. Använd `navigator.storage` i stället."}, "core/lib/deprecations-strings.js | pictureSourceSrc": {"message": "`<source src>` med `<picture>` som överordnat element är ogiltigt och ignoreras därför. Använd `<source srcset>` i stället."}, "core/lib/deprecations-strings.js | prefixedStorageInfo": {"message": "`window.webkitStorageInfo` är utfasat. Använd `navigator.storage` i stället."}, "core/lib/deprecations-strings.js | requestedSubresourceWithEmbeddedCredentials": {"message": "Förfrågningar från underresurser vars webbadresser innehåller inbäddade användaruppgifter (t.ex. `**********************/`) blockeras."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpFalse": {"message": "Begränsningen `DtlsSrtpKeyAgreement` har tagits bort. Du har angett ett `false`-värde för denna begr<PERSON>, vilket tolkas som ett försök att använda den borttagna `SDES key negotiation`-metoden. Denna funktion har tagits bort. Använd i stället en tjänst med stöd för `DTLS key negotiation`."}, "core/lib/deprecations-strings.js | rtcConstraintEnableDtlsSrtpTrue": {"message": "Begränsningen `DtlsSrtpKeyAgreement` har tagits bort. Du har angett ett `true`-värde för denna begrä<PERSON>, vilket inte hade någon inverkan, men du kan ta bort begränsningen för att göra det tydligare."}, "core/lib/deprecations-strings.js | rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics": {"message": "`Complex Plan B SDP` hittades. Den här dialekten av `Session Description Protocol` stöds inte längre. Använd `Unified Plan SDP` i stället."}, "core/lib/deprecations-strings.js | rtcPeerConnectionSdpSemanticsPlanB": {"message": "`Plan B SDP semantics`, som används när du skapar en `RTCPeerConnection` med `{sdpSemantics:plan-b}`, är en äldre, icke-standardiserad version av `Session Description Protocol` som har raderats permanent från webbplatformen. Den är fortfarande tillgänglig när du skapar med `IS_FUCHSIA`, men vi planerar att ta bort den möjligheten så snart som möjligt. Du ska inte förlita dig på den. Du hittar mer om statusen på https://crbug.com/1302249."}, "core/lib/deprecations-strings.js | rtcpMuxPolicyNegotiate": {"message": "Alternativet `rtcpMuxPolicy` är utfasat och kommer att tas bort."}, "core/lib/deprecations-strings.js | sharedArrayBufferConstructedWithoutIsolation": {"message": "`SharedArrayBuffer` kräver isolering för korsursprung. Läs mer på https://developer.chrome.com/blog/enabling-shared-array-buffer/."}, "core/lib/deprecations-strings.js | textToSpeech_DisallowedByAutoplay": {"message": "`speechSynthesis.speak()` utan användaraktivering har fasats ut och kommer att tas bort."}, "core/lib/deprecations-strings.js | title": {"message": "En utfasad funktion användes"}, "core/lib/deprecations-strings.js | v8SharedArrayBufferConstructedInExtensionWithoutIsolation": {"message": "Till<PERSON>gg måste isoleras för korsursprung om du vill fortsätta använda `SharedArrayBuffer`. Läs mer på https://developer.chrome.com/docs/extensions/mv3/cross-origin-isolation/."}, "core/lib/deprecations-strings.js | vendorSpecificApi": {"message": "{PH1} är specifik för leverantören. Använd standardversionen av {PH2} i stället."}, "core/lib/deprecations-strings.js | xhrJSONEncodingDetection": {"message": "UTF-16 stöds inte av json-svar i `XMLHttpRequest`"}, "core/lib/deprecations-strings.js | xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload": {"message": "Synkron `XMLHttpRequest` i huvudtråden har fasats ut på grund av dess negativa effekt på slutanvändarens upplevelse. Besök https://xhr.spec.whatwg.org/ om du behöver hjälp."}, "core/lib/deprecations-strings.js | xrSupportsSession": {"message": "`supportsSession()` är utfasat. Använd `isSessionSupported()` och kontrollera det matchade booleska värdet i stället."}, "core/lib/i18n/i18n.js | columnBlockingTime": {"message": "Tidsåtgång för blockering av huvudtråd"}, "core/lib/i18n/i18n.js | columnCacheTTL": {"message": "Lagringstid i cacheminnet"}, "core/lib/i18n/i18n.js | columnDescription": {"message": "Beskrivning"}, "core/lib/i18n/i18n.js | columnDuration": {"message": "Varaktighet"}, "core/lib/i18n/i18n.js | columnElement": {"message": "Element"}, "core/lib/i18n/i18n.js | columnFailingElem": {"message": "Element med fel"}, "core/lib/i18n/i18n.js | columnLocation": {"message": "Plats"}, "core/lib/i18n/i18n.js | columnName": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnOverBudget": {"message": "Över budget"}, "core/lib/i18n/i18n.js | columnRequests": {"message": "Begäranden"}, "core/lib/i18n/i18n.js | columnResourceSize": {"message": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>k"}, "core/lib/i18n/i18n.js | columnResourceType": {"message": "Resurstyp"}, "core/lib/i18n/i18n.js | columnSize": {"message": "Storlek"}, "core/lib/i18n/i18n.js | columnSource": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnStartTime": {"message": "Starttid"}, "core/lib/i18n/i18n.js | columnTimeSpent": {"message": "Tid som använts"}, "core/lib/i18n/i18n.js | columnTransferSize": {"message": "Överföringsstorlek"}, "core/lib/i18n/i18n.js | columnURL": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | columnWastedBytes": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> besparing"}, "core/lib/i18n/i18n.js | columnWastedMs": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> besparing"}, "core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric": {"message": "Cumulative Layout Shift"}, "core/lib/i18n/i18n.js | displayValueByteSavings": {"message": "Möjlig databesparing {wastedBytes, number, bytes} Kibit"}, "core/lib/i18n/i18n.js | displayValueElementsFound": {"message": "{nodeCount,plural, =1{1 element hittades}other{# element hittades}}"}, "core/lib/i18n/i18n.js | displayValueMsSavings": {"message": "Möjlig tidsbesparing: {wastedMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | documentResourceType": {"message": "Dokument"}, "core/lib/i18n/i18n.js | firstContentfulPaintMetric": {"message": "First Contentful Paint"}, "core/lib/i18n/i18n.js | firstMeaningfulPaintMetric": {"message": "F<PERSON>rsta meningsfulla skärmuppritningen"}, "core/lib/i18n/i18n.js | fontResourceType": {"message": "Teckensnitt"}, "core/lib/i18n/i18n.js | imageResourceType": {"message": "Bild"}, "core/lib/i18n/i18n.js | interactionToNextPaint": {"message": "Interaktion till nästa uppritning"}, "core/lib/i18n/i18n.js | interactiveMetric": {"message": "Time to Interactive"}, "core/lib/i18n/i18n.js | itemSeverityHigh": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityLow": {"message": "<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | itemSeverityMedium": {"message": "Medelstor"}, "core/lib/i18n/i18n.js | largestContentfulPaintMetric": {"message": "Largest Contentful Paint"}, "core/lib/i18n/i18n.js | maxPotentialFIDMetric": {"message": "Högsta potentiella fördröjning till första inmatningen"}, "core/lib/i18n/i18n.js | mediaResourceType": {"message": "Media"}, "core/lib/i18n/i18n.js | ms": {"message": "{timeInMs, number, milliseconds} ms"}, "core/lib/i18n/i18n.js | otherResourceType": {"message": "<PERSON><PERSON>"}, "core/lib/i18n/i18n.js | otherResourcesLabel": {"message": "<PERSON><PERSON><PERSON><PERSON> resurser"}, "core/lib/i18n/i18n.js | scriptResourceType": {"message": "S<PERSON><PERSON><PERSON>"}, "core/lib/i18n/i18n.js | seconds": {"message": "{timeInMs, number, seconds} s"}, "core/lib/i18n/i18n.js | speedIndexMetric": {"message": "Speed Index"}, "core/lib/i18n/i18n.js | stylesheetResourceType": {"message": "Formatmall"}, "core/lib/i18n/i18n.js | thirdPartyResourceType": {"message": "Tredje part"}, "core/lib/i18n/i18n.js | totalBlockingTimeMetric": {"message": "Total Blocking Time"}, "core/lib/i18n/i18n.js | totalResourceType": {"message": "Totalt"}, "core/lib/lh-error.js | badTraceRecording": {"message": "<PERSON>tt fel uppstod när spårningen skulle registreras för sidinläsningen. Kör Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | criTimeout": {"message": "Tidsgränsen överskreds under väntan på den första anslutningen till felsökningsprotokollet."}, "core/lib/lh-error.js | didntCollectScreenshots": {"message": "Inga skärmbilder togs i Chrome medan sidan lästes in. Kontrollera att det finns synligt innehåll på sidan och kör Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | dnsFailure": {"message": "Uppslagningen av den angivna domänen misslyckades på DNS-servern."}, "core/lib/lh-error.js | erroredRequiredArtifact": {"message": "En obligatorisk {artifactName}-insamlare påträffade ett fel: {errorMessage}"}, "core/lib/lh-error.js | internalChromeError": {"message": "<PERSON>tt internt fel har uppstått i Chrome. Starta om Chrome och testa att köra Lighthouse igen."}, "core/lib/lh-error.js | missingRequiredArtifact": {"message": "En {artifactName}-sam<PERSON>e som krävs kördes inte."}, "core/lib/lh-error.js | noFcp": {"message": "<PERSON><PERSON> renderade inget innehåll. Se till att du håller webbläsarfönstret öppet i förgrunden under inläsningen och försök igen. ({errorCode})"}, "core/lib/lh-error.js | noLcp": {"message": "Sidan visade inte innehåll som klassas som största uppritningen av innehåll (LCP). Se till att sidan har ett giltigt LCP-element och försök sedan igen. ({errorCode})"}, "core/lib/lh-error.js | notHtml": {"message": "<PERSON> tillhandahållna sidan är inte HTML (visas som MIME-typen {mimeType})."}, "core/lib/lh-error.js | oldChromeDoesNotSupportFeature": {"message": "Den här versionen av Chrome är för gammal för {featureName}. Använd en nyare version för att visa de fullständiga resultaten."}, "core/lib/lh-error.js | pageLoadFailed": {"message": "Det gick inte att läsa in den begärda sidan i Lighthouse. Kontrollera att du testar rätt webbadress och att servern svarar korrekt."}, "core/lib/lh-error.js | pageLoadFailedHung": {"message": "Det gick inte att läsa in den begärda webbadressen med Lighthouse eftersom sidan slutade svara."}, "core/lib/lh-error.js | pageLoadFailedInsecure": {"message": "Den webbadress du angav har inget giltigt säkerhetscertifikat. {securityMessages}"}, "core/lib/lh-error.js | pageLoadFailedInterstitial": {"message": "Chrome förhindrade sidhämtning med en mellansidesannons. Kontrollera att du testar rätt webbadress och att servern svarar korrekt."}, "core/lib/lh-error.js | pageLoadFailedWithDetails": {"message": "Det gick inte att läsa in den begärda sidan i Lighthouse. Kontrollera att du testar rätt webbadress och att servern svarar korrekt. (Mer information: {errorDetails})"}, "core/lib/lh-error.js | pageLoadFailedWithStatusCode": {"message": "Det gick inte att läsa in den begärda sidan i Lighthouse. Kontrollera att du testar rätt webbadress och att servern svarar korrekt. (Statuskod: {statusCode})"}, "core/lib/lh-error.js | pageLoadTookTooLong": {"message": "Det tog för lång tid att läsa in sidan. Minska sidans inläsningstid genom att följa förslagen i rapporten och kör sedan Lighthouse igen. ({errorCode})"}, "core/lib/lh-error.js | protocolTimeout": {"message": "Den angivna väntetiden för svar med DevTools-protokollet har överskridits. (Metod: {protocolMethod})"}, "core/lib/lh-error.js | requestContentTimeout": {"message": "Den angivna tiden för att hämta resurser har överskridits"}, "core/lib/lh-error.js | urlInvalid": {"message": "Den angivna webbadressen verkar vara ogiltig."}, "core/lib/navigation-error.js | warningXhtml": {"message": "Sidans MIME-typ är XHTML: Lighthouse har inte direkt stöd för den här dokumenttypen"}, "core/user-flow.js | defaultFlowName": {"message": "Användarflöde ({url})"}, "core/user-flow.js | defaultNavigationName": {"message": "Rapport över navigering ({url})"}, "core/user-flow.js | defaultSnapshotName": {"message": "Rapport med ögonblicksbild ({url})"}, "core/user-flow.js | defaultTimespanName": {"message": "Rapport över tidsperiod ({url})"}, "flow-report/src/i18n/ui-strings.js | allReports": {"message": "<PERSON>a rapporter"}, "flow-report/src/i18n/ui-strings.js | categories": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryAccessibility": {"message": "Tillgänglighet"}, "flow-report/src/i18n/ui-strings.js | categoryBestPractices": {"message": "<PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | categoryPerformance": {"message": "Prestanda"}, "flow-report/src/i18n/ui-strings.js | categoryProgressiveWebApp": {"message": "Progressiv webbapp"}, "flow-report/src/i18n/ui-strings.js | categorySeo": {"message": "SEO"}, "flow-report/src/i18n/ui-strings.js | desktop": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | helpDialogTitle": {"message": "Information om rapporten över flöde i Lighthouse"}, "flow-report/src/i18n/ui-strings.js | helpLabel": {"message": "Information om flöden"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionNavigation": {"message": "Du kan använda rapporter över navigering till följande:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionSnapshot": {"message": "Du kan använda rapporter med ögonblicksbilder till följande:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseInstructionTimespan": {"message": "Du kan använda rapporter över tidsperiod till följande:"}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation1": {"message": "Hämta ett prestandavärde för Lighthouse."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation2": {"message": "Mät resultatmätvärden för <PERSON>, t.ex. Största uppritningen av innehåll och hastighetsindex."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseNavigation3": {"message": "Testa funktioner för progressiva webbappar."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot1": {"message": "Hitta tillgänglighetsproblem i appar för en sida eller komplexa formulär."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseSnapshot2": {"message": "Utvärdera rekommenderade metoder för menyer och UI-element som döljs bakom interaktioner."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan1": {"message": "Mäta layoutförskjutningar och körningstider för JavaScript i en serie interaktioner."}, "flow-report/src/i18n/ui-strings.js | helpUseCaseTimespan2": {"message": "Upptäck prestandamöjligheter och förbättra upplevelsen på långlivade sidor och appar för en sida."}, "flow-report/src/i18n/ui-strings.js | highestImpact": {"message": "<PERSON><PERSON><PERSON> e<PERSON>"}, "flow-report/src/i18n/ui-strings.js | informativeAuditCount": {"message": "{numInformative,plural, =1{{numInformative} informativ granskning}other{{numInformative} informativa granskningar}}"}, "flow-report/src/i18n/ui-strings.js | mobile": {"message": "Mobil"}, "flow-report/src/i18n/ui-strings.js | navigationDescription": {"message": "Sidhämtning"}, "flow-report/src/i18n/ui-strings.js | navigationLongDescription": {"message": "Med rapporter över navigering kan du analysera en enskild sidhämtning, precis som med de ursprungliga Lighthouse-rapporterna."}, "flow-report/src/i18n/ui-strings.js | navigationReport": {"message": "Rapport över navigering"}, "flow-report/src/i18n/ui-strings.js | navigationReportCount": {"message": "{numNavigation,plural, =1{{numNavigation} rapport över navigering}other{{numNavigation} rapporter över navigering}}"}, "flow-report/src/i18n/ui-strings.js | passableAuditCount": {"message": "{numPassableAudits,plural, =1{{numPassableAudits} granskning som kan godkännas}other{{numPassableAudits} granskningar som kan godkännas}}"}, "flow-report/src/i18n/ui-strings.js | passedAuditCount": {"message": "{numPassed,plural, =1{{numPassed} godkänd granskning}other{{numPassed} godkända granskningar}}"}, "flow-report/src/i18n/ui-strings.js | ratingAverage": {"message": "Genomsnittlig"}, "flow-report/src/i18n/ui-strings.js | ratingError": {"message": "<PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingFail": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "flow-report/src/i18n/ui-strings.js | ratingPass": {"message": "Bra"}, "flow-report/src/i18n/ui-strings.js | save": {"message": "Spara"}, "flow-report/src/i18n/ui-strings.js | snapshotDescription": {"message": "Sidans status vid en viss tidpunkt"}, "flow-report/src/i18n/ui-strings.js | snapshotLongDescription": {"message": "Med rapporter med ögonblicksbilder går det att analysera en sida i ett visst läge, van<PERSON><PERSON><PERSON> efter interaktioner från användare."}, "flow-report/src/i18n/ui-strings.js | snapshotReport": {"message": "Rapport med ögonblicksbild"}, "flow-report/src/i18n/ui-strings.js | snapshotReportCount": {"message": "{numSnapshot,plural, =1{{numSnapshot} rapporter med ögonblicksbild}other{{numSnapshot} rapporter med ögonblicksbilder}}"}, "flow-report/src/i18n/ui-strings.js | summary": {"message": "Översikt"}, "flow-report/src/i18n/ui-strings.js | timespanDescription": {"message": "Användarinteraktioner"}, "flow-report/src/i18n/ui-strings.js | timespanLongDescription": {"message": "Med rapporter över tidsintervall kan du analysera en slumpmässig tidsperiod som oftast innehåller användarinteraktioner."}, "flow-report/src/i18n/ui-strings.js | timespanReport": {"message": "Rapport över tidsperiod"}, "flow-report/src/i18n/ui-strings.js | timespanReportCount": {"message": "{numTimespan,plural, =1{{numTimespan} rapport över tidsperiod}other{{numTimespan} rapporter över tidsperioder}}"}, "flow-report/src/i18n/ui-strings.js | title": {"message": "Rapport över användarflöde i Lighthouse"}, "node_modules/lighthouse-stack-packs/packs/amp.js | efficient-animated-content": {"message": "<PERSON><PERSON>r <PERSON>rat innehåll använder du [`amp-anim`](https://amp.dev/documentation/components/amp-anim/) till att minimera CPU-användningen när innehållet inte visas på skärmen."}, "node_modules/lighthouse-stack-packs/packs/amp.js | modern-image-formats": {"message": "Vi rekommenderar att du visar alla [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites)-komponenter i WebP-format och anger ett lämpligt alternativ för andra. [<PERSON><PERSON><PERSON> mer](https://amp.dev/documentation/components/amp-img/#example:-specifying-a-fallback-image)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | offscreen-images": {"message": "Kontrollera att du använder [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) så att uppskjuten inläsning tillämpas automatiskt för bilder. [<PERSON><PERSON><PERSON> mer](https://amp.dev/documentation/guides-and-tutorials/develop/media_iframes_3p/?format=websites#images)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | render-blocking-resources": {"message": "[Rendera AMP-layouter på serversidan](https://amp.dev/documentation/guides-and-tutorials/optimize-and-measure/server-side-rendering/) med hjälp av verktyg som [AMP Optimizer](https://github.com/ampproject/amp-toolbox/tree/master/packages/optimizer)."}, "node_modules/lighthouse-stack-packs/packs/amp.js | unminified-css": {"message": "<PERSON><PERSON><PERSON> [AMP-dokumentationen](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/style_pages/) och kontrollera att alla format stöds."}, "node_modules/lighthouse-stack-packs/packs/amp.js | uses-responsive-images": {"message": "Komponenten [`amp-img`](https://amp.dev/documentation/components/amp-img/?format=websites) har stöd för attributet [`srcset`](https://web.dev/use-srcset-to-automatically-choose-the-right-image/) så att du kan ange vilka bildtillgångar som ska användas utifrån skärmstorlek. [<PERSON><PERSON><PERSON> mer](https://amp.dev/documentation/guides-and-tutorials/develop/style_and_layout/art_direction/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | dom-size": {"message": "Vi rekommenderar virtuell scrollning med Component Dev Kit (CDK) om mycket stora listor renderas. [<PERSON><PERSON><PERSON> mer](https://web.dev/virtualize-lists-with-angular-cdk/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | total-byte-weight": {"message": "Använd [koduppdelning på dirigeringsnivå](https://web.dev/route-level-code-splitting-in-angular/) för att minimera JavaScript-paketens storlek. Vi rekommenderar även att du cachelagrar tillgångar i förväg med [tjänstefunktionen Angular](https://web.dev/precaching-with-the-angular-service-worker/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unminified-warning": {"message": "Kontrollera att versioner genereras i produktionsläge om du använder Angular CLI. [<PERSON><PERSON><PERSON> mer](https://angular.io/guide/deployment#enable-runtime-production-mode)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | unused-javascript": {"message": "Om du använder Angular CLI kontrollerar du dina paket genom att inkludera källkartor i produktionsversionen. [<PERSON><PERSON><PERSON> mer](https://angular.io/guide/deployment#inspect-the-bundles)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-rel-preload": {"message": "<PERSON><PERSON><PERSON> in dirigeringar i förväg så att navigeringen går snabbare. [<PERSON><PERSON><PERSON> mer](https://web.dev/route-preloading-in-angular/)."}, "node_modules/lighthouse-stack-packs/packs/angular.js | uses-responsive-images": {"message": "Vi rekommenderar att du hanterar bildernas brytpunkter med hjälp av verktyget `BreakpointObserver` i Component Dev Kit (CDK). [<PERSON><PERSON><PERSON> mer](https://material.angular.io/cdk/layout/overview)."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | efficient-animated-content": {"message": "Ladda upp GIF-filen till en tjänst som kan göra den tillgänglig för inbäddning som HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | font-display": {"message": "<PERSON><PERSON> `@font-display` när du definierar anpassade teckensnitt i temat."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | modern-image-formats": {"message": "Vi rekommenderar att du konfigurerar [WebP-bildformat med ett Convert-bildformatering](https://www.drupal.org/docs/core-modules-and-themes/core-modules/image-module/working-with-images#styles) på webbplatsen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | offscreen-images": {"message": "Installera [en Drupal-modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A67&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=%22lazy+load%22&solrsort=iss_project_release_usage+desc&op=Search) som kan utnyttja uppskjuten inläsning av bilder. Med sådana moduler går det att skjuta upp inläsningen av bilder som inte visas på skärmen i syfte att förbättra prestandan."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | render-blocking-resources": {"message": "Du kan använda en modul för att lägga till kritiska CSS- och JavaScript-tillgångar eller läsa in tillgångar asynkront via JavaScript, t.ex. modulen [Advanced CSS/JS Aggregation](https://www.drupal.org/project/advagg). Tänk på att optimeringarna som denna modul gör kan leda till att webbplatsen slutar att fungera, så du kan behöva ändra i koden."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | server-response-time": {"message": "Serverns svarstider påverkas av teman, moduler och serverns prestanda. Du kan använda ett mer optimerat tema, välja en optimeringsmodul och/eller uppgradera servern. Värdservrarna ska använda cachelagring av OP-kod för PHP och minnescachelagring för att minska sökfrågetiderna för databasen, t.ex. Redis eller Memcached, samt en optimerad applogik för att förbereda sidor snabbare."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | total-byte-weight": {"message": "Du kan använda [responsiva bildformat](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) för att minska storleken på bilderna som läses in på sidan. Om du använder Views för att visa flera innehållsobjekt på en sida kan du implementera sidnumrering om du vill begränsa antalet innehållsobjekt som visas på en viss sida."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-css": {"message": "Kontrollera att du har aktiverat Aggregate CSS files under Administration » Configuration » Development. Du kan även konfigurera mer avancerade sammanställningsalternativ via [ytterligare moduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=css+aggregation&solrsort=iss_project_release_usage+desc&op=Search) och göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera CSS-format."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unminified-javascript": {"message": "Kontrollera att du har aktiverat Aggregate JavaScript files under Administration » Configuration » Development. Du kan även konfigurera mer avancerade sammanställningsalternativ via [ytterligare moduler](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=javascript+aggregation&solrsort=iss_project_release_usage+desc&op=Search) och göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera JavaScript-tillgångar."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-css-rules": {"message": "Vi rekommenderar att du tar bort CSS-regler som inte används och endast bifogar nödvändiga Drupal-bibliotek till den relevanta sidan eller komponenten på en sida. Du hittar mer information i [dokumentationen om Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Testa fliken för [kodtäckning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes utvecklarverktyg om du vill se vilka bibliotek som lägger till överflödig CSS. Du ser på formatmallens webbadress vilket tema eller vilken modul som koden kommer från när CSS-sammanställning är inaktiverat för Drupal-webbplatsen. Titta efter teman eller moduler med många CSS-formatmallar på listan där en stor del av stapeln är röd. Ett tema eller en modul ska bara ställa en formatmall i kö för inläsning om det faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | unused-javascript": {"message": "Vi rekommenderar att du tar bort JavaScript-tillgångar som inte används och endast bifogar nödvändiga Drupal-bibliotek till den relevanta sidan eller komponenten på en sida. Du hittar mer information i [dokumentationen om Drupal](https://www.drupal.org/docs/8/creating-custom-modules/adding-stylesheets-css-and-javascript-js-to-a-drupal-8-module#library). Testa fliken för [kodtäckning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes utvecklarverktyg om du vill se vilka bibliotek som lägger till överflödig JavaScript. Du ser på skriptets webbadress vilket tema eller vilken modul som koden kommer ifrån när JavaScript-sammanställning är inaktiverat på Drupal-webbplatsen. Titta efter teman/moduler med många skript på listan där en stor del av stapeln är röd. Ett tema eller en modul ska bara ställa ett skript i kö för inläsning om det faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON> and proxy cache maximum age under Administration » Configuration » Development. Läs mer om [cachelagring i Drupal och optimering för prestanda.](https://www.drupal.org/docs/7/managing-site-performance-and-scalability/caching-to-improve-performance/caching-overview#s-drupal-performance-resources)"}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-optimized-images": {"message": "Du kan använda en [modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=im_vid_3%3A123&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=optimize+images&solrsort=iss_project_release_usage+desc&op=Search) som automatiskt optimerar och minskar storleken på bilderna som laddas upp via webbplatsen utan att göra avkall på kvaliteten. Kontrollera även att du använder de integrerade [responsiva bildformaten](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) från Drupal (tillgängliga i Drupal 8 och senare) för alla bilder som renderas på webbplatsen."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-rel-preconnect": {"message": "Signaler för förhandsanslutning eller DNS-förhandshämtning kan läggas till genom att installera och konfigurera [en modul](https://www.drupal.org/project/project_module?f%5B0%5D=&f%5B1%5D=&f%5B2%5D=&f%5B3%5D=&f%5B4%5D=sm_field_project_type%3Afull&f%5B5%5D=&f%5B6%5D=&text=dns-prefetch&solrsort=iss_project_release_usage+desc&op=Search) med funktioner för signaler för användaragenter."}, "node_modules/lighthouse-stack-packs/packs/drupal.js | uses-responsive-images": {"message": "Kontrollera att du använder de integrerade [responsiva bildformaten](https://www.drupal.org/docs/8/mobile-guide/responsive-images-in-drupal-8) fr<PERSON><PERSON> (tillgängliga i Drupal 8 och senare). Använd de responsiva bildformaten när du renderar bildfält via visningslägen, vyer eller bilder som laddats upp via WYSIWYG-redigeraren."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | font-display": {"message": "Använd [<PERSON><PERSON> Leap](https://pubdash.ezoic.com/speed) och aktivera `Optimize Fonts` så att du automatiskt kan dra nytta av CSS-funktionen `font-display`, som gör text synlig för användaren medan webbteckensnitten läses in."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | modern-image-formats": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Next-Gen Formats` så att bilder konverteras till WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | offscreen-images": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `<PERSON>zy <PERSON>ad Images` så att bilder utanför skärmen inte läses in förrän de behövs."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | render-blocking-resources": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Critical CSS` och `Script Delay`, så att mindre viktig JS/CSS skjuts upp."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | server-response-time": {"message": "Använd [Ezoic Cloud Caching](https://pubdash.ezoic.com/speed/caching) så att innehållet cachelagras i vårt världsomspännande nätverk, vilket förbättrar tiden till första byte."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-css": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Minify CSS` så att CSS-koden minifieras automatiskt, vilket minskar mängden data som överförs i nätverket."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unminified-javascript": {"message": "Anv<PERSON>nd [E<PERSON> Leap](https://pubdash.ezoic.com/speed) och aktivera `Minify Javascript` så att JS-koden minifieras automatiskt, vilket minskar mängden data som överförs i nätverket."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | unused-css-rules": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Remove Unused CSS` mot detta problem. Då identifieras de CSS-klasser som faktiskt används på var och en av webbplatsens sidor medan andra CSS-klasser tas bort så att filstorleken hålls nere."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-long-cache-ttl": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Efficient Static Cache Policy`, som ställer in rekommenderade värden för statiska tillgångar i huvudets cachelagringsfält."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-optimized-images": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Next-Gen Formats` så att bilder konverteras till WebP."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preconnect": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Pre-Connect Origins`. Då läggs hintar om `preconnect`-resurser till automatiskt så att anslutningar till viktiga tredjepartsursprung upprättas tidigt."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-rel-preload": {"message": "Använd [E<PERSON> Leap](https://pubdash.ezoic.com/speed) och aktivera `Preload Fonts` och `Preload Background Images`. <PERSON>ta lägger till `preload`-l<PERSON><PERSON><PERSON>, vilket prioriterar hämtningen av resurser som för närvarande begärs senare under sidhämtningen."}, "node_modules/lighthouse-stack-packs/packs/ezoic.js | uses-responsive-images": {"message": "Använd [Ezoic Leap](https://pubdash.ezoic.com/speed) och aktivera `Resize Images` så att bildernas storlek anpassas efter enheten, vilket minskar mängden data som överförs i nätverket."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | efficient-animated-content": {"message": "Ladda upp GIF-filen till en tjänst som kan göra den tillgänglig för inbäddning som HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | modern-image-formats": {"message": "Det kan vara bra att använda en [plugin](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=webp) eller tjänst som automatiskt konverterar uppladdade bilder till optimalt format."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | offscreen-images": {"message": "Installera en [Joomla-plugin för lat inläsning](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=lazy%20loading) som ger möjlighet att skjuta upp inläsningen av bilder som inte visas på skärmen, eller byt till en mall som har den funktionen. Från och med Joomla 4.0 får alla nya bilder [automatiskt](https://github.com/joomla/joomla-cms/pull/30748) attributet `loading` fr<PERSON>n bö<PERSON>."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | render-blocking-resources": {"message": "Det finns ett antal pluginmoduler för Jo<PERSON>la som kan hjälpa dig att [lägga till kritiska tillgångar direkt på sidan](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) eller [skjuta upp inläsningen av mindre viktiga resurser](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance). Tänk på att optimeringarna som dessa pluginmoduler gör kan leda till att funktioner i mallarna eller andra pluginmoduler slutar fungera, så du kan behöva köra noggranna tester."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | server-response-time": {"message": "Serverns svarstider påverkas av mallar, till<PERSON><PERSON> och serverns prestanda. Du kan använda en mer optimerad mall, välja ett optimeringstillägg och/eller uppgradera servern."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | total-byte-weight": {"message": "Du kan visa utdrag i artikelkategorierna (t.ex. via en läs mer-länk), minska antalet artiklar på sidan, dela upp långa inlägg på flera sidor eller använda en plugin som läser in kommentarer med uppskjuten inläsning."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-css": {"message": "Det finns ett antal [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) som kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera CSS-format. Det finns även mallar som gör samma sak."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unminified-javascript": {"message": "Det finns ett antal [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) som kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera skript. Det finns även mallar som gör samma sak."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-css-rules": {"message": "Du kan minska antalet [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) som läser in CSS som inte används på sidan, eller byta ut dem. Testa fliken för [kodt<PERSON><PERSON><PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes utvecklarverktyg om du vill se vilka tillägg som lägger till överflödig CSS. Du ser på CSS-formatmallens webbadress vilket tema eller vilken plugin som koden kommer från. Titta efter pluginmoduler med många CSS-formatmallar på listan där en stor del av stapeln är röd. En plugin ska bara ställa en formatmall i kö för inläsning om den faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | unused-javascript": {"message": "Du kan minska antalet [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](https://extensions.joomla.org/) som läser in JavaScript som inte används på sidan, eller byta ut dem. Testa fliken för [kodt<PERSON><PERSON><PERSON>](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes utvecklarverktyg om du vill se vilka pluginmoduler som lägger till överflödig JS. Du ser på formatmallens webbadress vilket tillägg som koden kommer från. Titta efter tillägg med många skript på listan där en stor del av stapeln är röd. Ett tillägg ska bara ställa ett skript i kö för inläsning om det faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> mer om [webblä<PERSON><PERSON> cachelagring och Joomla](https://docs.joomla.org/Cache)."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-optimized-images": {"message": "Du kan använda en [WordPress-plugin för bildoptimering](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=performance) som komprimerar dina bilder utan att göra avkall på kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-responsive-images": {"message": "Vi rekommenderar att du använder en [pluginmodul för responsiva bilder](https://extensions.joomla.org/instant-search/?jed_live%5Bquery%5D=responsive%20images) så att du kan använda responsiva bilder i innehållet."}, "node_modules/lighthouse-stack-packs/packs/joomla.js | uses-text-compression": {"message": "Du kan aktivera textkomprimering genom att aktivera Gzip Page Compression i Joomla (System > Global configuration > Server)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | critical-request-chains": {"message": "Om du paketerar JavaScript-tillgångarna rekommenderar vi att du använder [baler](https://github.com/magento/baler)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | disable-bundling": {"message": "Vi rekommenderar att du inaktiverar Magentos inbyggda [paketering och minifiering av JavaScript](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html) och använder [baler](https://github.com/magento/baler/) i stället."}, "node_modules/lighthouse-stack-packs/packs/magento.js | font-display": {"message": "<PERSON><PERSON> `@font-display` när du [definierar anpassade teckensnitt](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/css-topics/using-fonts.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | modern-image-formats": {"message": "Vi rekommenderar att du söker på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=webp) efter olika tillägg från tredje part så att du kan använda nyare bildformat."}, "node_modules/lighthouse-stack-packs/packs/magento.js | offscreen-images": {"message": "Vi rekommenderar att du ändrar dina produkt- och katalogmallar för att dra nytta av webbplattformens funktion för [uppskjuten inläsning](https://web.dev/native-lazy-loading)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | server-response-time": {"message": "<PERSON><PERSON><PERSON><PERSON> [Varnish-integrering](https://devdocs.magento.com/guides/v2.3/config-guide/varnish/config-varnish.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-css": {"message": "Aktivera alternativet Minify CSS Files (minifiera CSS-filer) i butikens utvecklarinställningar. [<PERSON><PERSON><PERSON> mer](https://devdocs.magento.com/guides/v2.3/performance-best-practices/configuration.html?itm_source=devdocs&itm_medium=search_page&itm_campaign=federated_search&itm_term=minify%20css%20files)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unminified-javascript": {"message": "<PERSON>v<PERSON><PERSON> [Terser](https://www.npmjs.com/package/terser) till att minifiera alla JavaScript-tillgångar från statisk innehållsimplementering och inaktivera den inbyggda minifieringsfunktionen."}, "node_modules/lighthouse-stack-packs/packs/magento.js | unused-javascript": {"message": "Inaktivera Magentos inbyggda [JavaScript-paketering](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/themes/js-bundling.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-optimized-images": {"message": "Vi rekommenderar att du söker på [Magento Marketplace](https://marketplace.magento.com/catalogsearch/result/?q=optimize%20image) efter olika tillägg från tredje part för att optimera bilder."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preconnect": {"message": "Signaler för för<PERSON>sanslutning eller DNS-förhandshämtning kan läggas till genom att [ändra ett temas layout](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/magento.js | uses-rel-preload": {"message": "`<link rel=preload>`-taggar kan läggas till genom att [ändra ett temas layout](https://devdocs.magento.com/guides/v2.3/frontend-dev-guide/layouts/xml-manage.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | modern-image-formats": {"message": "Optimera bildformat automatiskt med komponenten `next/image` i stället för `<img>`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | offscreen-images": {"message": "Skjut upp inläsningen av bilder automatiskt med komponenten `next/image` i stället för `<img>`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | prioritize-lcp-image": {"message": "Använd komponenten `next/image` och ge priority värdet true så att LCP-bilder (största uppritningen av innehåll) läses in i förväg. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/api-reference/next/image#priority)."}, "node_modules/lighthouse-stack-packs/packs/next.js | render-blocking-resources": {"message": "Skjut upp inläsningen av mindre viktiga skript från tredje part med komponenten `next/script`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/basic-features/script)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unsized-images": {"message": "Anv<PERSON>nd komponenten `next/image` för att säkerställa att bilder alltid får lämplig storlek. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/api-reference/next/image#width)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-css-rules": {"message": "<PERSON><PERSON><PERSON> in `PurgeCSS` i `Next.js`-konfigurationen så att regler som inte används tas bort från formatmallarna. [<PERSON><PERSON><PERSON> mer](https://purgecss.com/guides/next.html)."}, "node_modules/lighthouse-stack-packs/packs/next.js | unused-javascript": {"message": "Identifiera JavaScript-kod som inte används med `Webpack Bundle Analyzer`. [<PERSON><PERSON><PERSON> mer](https://github.com/vercel/next.js/tree/canary/packages/next-bundle-analyzer)"}, "node_modules/lighthouse-stack-packs/packs/next.js | user-timings": {"message": "Du kan mäta appens faktiska prestanda med `Next.js Analytics`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/advanced-features/measuring-performance)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-long-cache-ttl": {"message": "Konfigurera cachning av tillgångar och SSR-sidor (`Server-side Rendered`) som inte förändras. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/going-to-production#caching)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-optimized-images": {"message": "Justera bildkvalitet med komponenten `next/image` i stället för `<img>`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/basic-features/image-optimization)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-responsive-images": {"message": "<PERSON><PERSON>ll in `sizes` med komponenten `next/image`. [<PERSON><PERSON><PERSON> mer](https://nextjs.org/docs/api-reference/next/image#sizes)."}, "node_modules/lighthouse-stack-packs/packs/next.js | uses-text-compression": {"message": "Aktivera komprimering på Next.js-servern. [<PERSON><PERSON><PERSON> me<PERSON>](https://nextjs.org/docs/api-reference/next.config.js/compression)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | modern-image-formats": {"message": "Använd komponenten `nuxt/image` och ange `format=\"webp\"`. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#format)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | offscreen-images": {"message": "Använd komponenten `nuxt/image` och ange `loading=\"lazy\"` f<PERSON>r bilder som inte visas på skärmen. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#loading)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | prioritize-lcp-image": {"message": "Använd komponenten `nuxt/image` och ange `preload` f<PERSON><PERSON>. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#preload)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | unsized-images": {"message": "Använd komponenten `nuxt/image` och ange en specifik `width` och `height`. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#width--height)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-optimized-images": {"message": "Använd komponenten `nuxt/image` och ange önskad `quality`. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#quality)."}, "node_modules/lighthouse-stack-packs/packs/nuxt.js | uses-responsive-images": {"message": "Använd komponenten `nuxt/image` och ange önskad `sizes`. [<PERSON><PERSON><PERSON> mer](https://image.nuxtjs.org/components/nuxt-img#sizes)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | efficient-animated-content": {"message": "[Ersätt animerade GIF-bilder med videor](https://web.dev/replace-gifs-with-videos/) så att webbsidan läses in snabbare. Du kan även använda moderna filformat som [WebM](https://web.dev/replace-gifs-with-videos/#create-webm-videos) eller [AV1](https://developers.google.com/web/updates/2018/09/chrome-70-media-updates#av1-decoder) för att förbättra komprimeringens effektivitet med mer än 30 % jämfört med den nuvarande toppmoderna videokodeken VP9."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | modern-image-formats": {"message": "Det kan vara bra att använda en [plugin](https://octobercms.com/plugins?search=image) eller tjänst som automatiskt konverterar uppladdade bilder till optimalt format. [Förlustfria WebP-bilder](https://developers.google.com/speed/webp) är 26 % mindre än PNG-bilder och 25–34 % mindre än jämförbara JPEG-bilder med samma upplösning enligt SSIM-kvalitetsindex. Ett annat modernare bildformat är [AVIF](https://jakearchibald.com/2020/avif-has-landed/)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | offscreen-images": {"message": "Du kan installera [en plugin för lat inläsning av bilder](https://octobercms.com/plugins?search=lazy) som gör det möjligt att skjuta upp inläsningen av bilder som inte visas på skärmen eller byta till ett tema som erbjuder den funktionen. Du kan även använda [plugin-programmet AMP](https://octobercms.com/plugins?search=Accelerated+Mobile+Pages)."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | render-blocking-resources": {"message": "Det finns många plugins som kan hjälpa dig att [lägga till kritiska tillgångar direkt på sidan](https://octobercms.com/plugins?search=css). Dessa plugins kan få andra plugins att sluta fungera, så du bör testa noga."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | server-response-time": {"message": "Serverns svarstider påverkas av teman, plugins och serverns prestanda. Du kan använda ett mer optimerat tema, välja en optimeringsplugin och/eller uppgradera servern. Med October CMS kan utvecklare även använda [`Queues`](https://octobercms.com/docs/services/queues) för att skjuta upp bearbetningen av en tidskrävande uppgift, till exempel att skicka ett e-postmeddelande. Detta gör webbförfrågningarna mycket snabbare."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | total-byte-weight": {"message": "Du kan visa utdrag i inläggslistan (t.ex. med en `show more`-knapp), minska antalet inlägg på webbsidan, dela upp långa inlägg på flera webbsidor eller använda en plugin som läser in kommentarer med lat inläsning."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-css": {"message": "Det finns många [plugins](https://octobercms.com/plugins?search=css) som kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera formatmallarna. Du kan också göra minifieringen direkt i konstruktionsfasen för att snabba på utvecklingsarbetet."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unminified-javascript": {"message": "Det finns många [plugins](https://octobercms.com/plugins?search=javascript) som kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera skripten. Du kan också göra minifieringen direkt i konstruktionsfasen för att snabba på utvecklingsarbetet."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-css-rules": {"message": "Du kan se över [de plugins](https://octobercms.com/plugins) som läser in CSS som inte används på webbplatsen. Du kan identifiera vilka plugins som lägger till onödig CSS genom att köra [kodanvändning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes verktyg för programmerare. Du ser på formatmallens webbadress vilket tema eller vilken plugin som koden kommer från. Titta efter plugins med många formatmallar där en stor del av stapeln är röd i kodanvändningen. En plugin ska endast lägga till en formatmall om den faktiskt används på webbsidan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | unused-javascript": {"message": "Du kan se över [plugin-programmen](https://octobercms.com/plugins?search=javascript) som läser in JavaScript som inte används på webbsidan. Du kan identifiera vilka plugins som lägger till onödig JavaScript genom att köra [kodanvändning](https://developers.google.com/web/updates/2017/04/devtools-release-notes#coverage) i Chromes verktyg för programmerare. Du ser på skriptets webbadress vilket tema eller vilken plugin som koden kommer från. Titta efter plugins med många skript där en stor del av stapeln är röd i kodanvändningen. En plugin ska endast lägga till ett skript om det faktiskt används på webbsidan."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> om att [för<PERSON>dra onödiga nätverksförfrågningar med HTTP-cacheminnet](https://web.dev/http-cache/#caching-checklist). Det finns många [plugins](https://octobercms.com/plugins?search=Caching) som kan användas för att snabba på cachelagringen."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-optimized-images": {"message": "Du kan använda en [plugin för bildoptimering](https://octobercms.com/plugins?search=image) som komprimerar dina bilder utan att göra avkall på kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-responsive-images": {"message": "Säkerställ att de bildstorlekar som krävs finns tillgängliga genom att ladda upp bilderna direkt via mediahanteraren. Du kan använda [filtret för att ändra storlek](https://octobercms.com/docs/markup/filter-resize) eller en [plugin som ändrar storleken](https://octobercms.com/plugins?search=image) så att optimala bildstorlekar används."}, "node_modules/lighthouse-stack-packs/packs/octobercms.js | uses-text-compression": {"message": "Aktivera textkomprimering i webbserverns konfiguration."}, "node_modules/lighthouse-stack-packs/packs/react.js | dom-size": {"message": "Du kan använda ett bibliotek för fönstersystem som `react-window` för att minimera antalet DOM-noder som skapas om du renderar många upprepade element på sidan. [<PERSON><PERSON><PERSON> mer](https://web.dev/virtualize-long-lists-react-window/). Minimera även onödiga omrenderingar med [`shouldComponentUpdate`](https://reactjs.org/docs/optimizing-performance.html#shouldcomponentupdate-in-action), [`PureComponent`](https://reactjs.org/docs/react-api.html#reactpurecomponent) eller [`React.memo`](https://reactjs.org/docs/react-api.html#reactmemo) och [hoppa endast över effekter](https://reactjs.org/docs/hooks-effect.html#tip-optimizing-performance-by-skipping-effects) tills vissa beroenden har ändrats om du använder hooken `Effect` till att förbättra körningsresultatet."}, "node_modules/lighthouse-stack-packs/packs/react.js | redirects": {"message": "Om du använder React Router minimerar du användningen av komponenten `<Redirect>` för [navigering av dirigeringar](https://reacttraining.com/react-router/web/api/Redirect)."}, "node_modules/lighthouse-stack-packs/packs/react.js | server-response-time": {"message": "Om du renderar React-komponenter på serversidan rekommenderar vi att du använder `renderToPipeableStream()` eller `renderToStaticNodeStream()` till att tillåta att klienten tar emot och hydrerar olika delar av uppmärkningen i stället för alla på en gång. [<PERSON><PERSON><PERSON> mer](https://reactjs.org/docs/react-dom-server.html#renderToPipeableStream)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-css": {"message": "Om versionssystemet minifierar CSS-filerna automatiskt kontrollerar du att du implementerar appens produktionsversion. Du kan kontrollera detta med tillägget React Developer Tools. [<PERSON><PERSON>s mer](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unminified-javascript": {"message": "Om versionssystemet minifierar JS-filerna automatiskt kontrollerar du att du implementerar appens produktionsversion. Du kan kontrollera detta med tillägget React Developer Tools. [<PERSON><PERSON>s mer](https://reactjs.org/docs/optimizing-performance.html#use-the-production-build)."}, "node_modules/lighthouse-stack-packs/packs/react.js | unused-javascript": {"message": "Om du inte renderar på serversidan [delar du upp JavaScript-paketen](https://web.dev/code-splitting-suspense/) med `React.lazy()`. I annat fall kan du dela upp koden med tredjepartsbibliotek som [loadable-components](https://www.smooth-code.com/open-source/loadable-components/docs/getting-started/)."}, "node_modules/lighthouse-stack-packs/packs/react.js | user-timings": {"message": "Mät renderingens resultat för dina komponenter med hjälp av React DevTools Profiler, som drar nytta av Profiler API. [Läs mer.](https://reactjs.org/blog/2018/09/10/introducing-the-react-profiler.html)"}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | efficient-animated-content": {"message": "Ladda upp GIF-filen till en tjänst som kan göra den tillgänglig för inbäddning som HTML5-video."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | modern-image-formats": {"message": "Du kan använda pluginprogrammet [Performance Lab](https://wordpress.org/plugins/performance-lab/) för att automatiskt konvertera dina uppladdade JPEG-bilder till WebP-format där detta stöds."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | offscreen-images": {"message": "Installera en [WordPress-plugin för lat inläsning](https://wordpress.org/plugins/search/lazy+load/) som ger möjlighet att skjuta upp inläsningen av bilder som inte visas på skärmen, eller byt till ett tema som har den funktionen. Du kan även använda [AMP-pluginmodulen](https://wordpress.org/plugins/amp/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | render-blocking-resources": {"message": "Det finns ett antal pluginmoduler för WordPress som kan hjälpa dig att [lägga till kritiska tillgångar direkt på sidan](https://wordpress.org/plugins/search/critical+css/) el<PERSON> [skjuta upp inläsningen av mindre viktiga resurser](https://wordpress.org/plugins/search/defer+css+javascript/). Tänk på att optimeringarna som dessa pluginmoduler gör kan leda till att funktioner i teman eller andra pluginmoduler slutar fungera, så du kan behöva ändra i koden."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | server-response-time": {"message": "Serverns svarstider påverkas av teman, pluginmoduler och serverns prestanda. Du kan använda ett mer optimerat tema, välja en optimeringsplugin och/eller uppgradera servern."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | total-byte-weight": {"message": "Du kan visa utdrag i inläggslistan (t.ex. via en more-tagg), minska antalet inlägg på sidan, dela upp långa inlägg på flera sidor eller använda en plugin som läser in kommentarer med lat inläsning."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-css": {"message": "Det finns ett antal [pluginmoduler för WordPress](https://wordpress.org/plugins/search/minify+css/) som kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera skript. Du kan också göra minifieringen direkt i konstruktionsfasen om möjligt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unminified-javascript": {"message": "<PERSON>tt antal [pluginmoduler för WordPress](https://wordpress.org/plugins/search/minify+javascript/) kan göra webbplatsen snabbare genom att sammanfoga, minifiera och komprimera skript. Du kan också göra minifieringen direkt i konstruktionsfasen om möjligt."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-css-rules": {"message": "Du kan minska antalet [WordPress-pluginmoduler](https://wordpress.org/plugins/) som läser in CSS som inte används på sidan, eller byta ut dem. Testa fliken för [kodtäck<PERSON>](https://developer.chrome.com/docs/devtools/coverage/) i Chromes utvecklarverktyg om du vill se vilka pluginmoduler som lägger till överflödig CSS. Du ser på CSS-formatmallens webbadress vilket tema eller vilken plugin som koden kommer från. Titta efter pluginmoduler med många CSS-formatmallar på listan där en stor del av stapeln är röd. En plugin ska bara ställa en formatmall i kö för inläsning om den faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | unused-javascript": {"message": "Du kan minska antalet [WordPress-pluginmoduler](https://wordpress.org/plugins/) som läser in JavaScript som inte används på sidan, eller byta ut dem. Testa fliken för [kodtäck<PERSON>](https://developer.chrome.com/docs/devtools/coverage/) i Chromes utvecklarverktyg om du vill se vilka pluginmoduler som lägger till överflödig JS. Du ser på skriptets webbadress vilket tema eller vilken plugin som koden kommer från. Titta efter pluginmoduler med många skript på listan där en stor del av stapeln är röd. En plugin ska bara ställa ett skript i kö för inläsning om det faktiskt används på sidan."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-long-cache-ttl": {"message": "<PERSON><PERSON><PERSON> mer om [cachelagring i webbläsaren och WordPress](https://wordpress.org/support/article/optimization/#browser-caching)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-optimized-images": {"message": "Du kan använda en [WordPress-plugin för bildoptimering](https://wordpress.org/plugins/search/optimize+images/) som komprimerar dina bilder utan att göra avkall på kvaliteten."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-responsive-images": {"message": "Säkerställ att de bildstorlekar som krävs finns tillgängliga genom att ladda upp bilderna direkt via [mediebiblioteket](https://wordpress.org/support/article/media-library-screen/) och infoga dem sedan från mediebiblioteket eller med bildwidgeten, så att de optimala bildstorlekarna används (även för brytpunkterna för responsiv design). Undvik att använda `Full Size`-bilder såvida de inte har mått som passar där bilderna ska användas. [Läs mer](https://wordpress.org/support/article/inserting-images-into-posts-and-pages/)."}, "node_modules/lighthouse-stack-packs/packs/wordpress.js | uses-text-compression": {"message": "Du kan aktivera textkomprimering i webbserverns konfiguration."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | modern-image-formats": {"message": "Aktivera Imagify på fliken Image Optimization i WP Rocket för att konvertera dina bilder till WebP."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | offscreen-images": {"message": "Aktivera [LazyLoad](https://docs.wp-rocket.me/article/1141-lazyload-for-images) i WP Rocket för att åtgärda enligt rekommendationen. Med den här funktionen fördröjs inläsningen av bilderna tills besökaren scrollar nedåt på sidan och behöver få se dem."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | render-blocking-resources": {"message": "Aktivera [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) och [Load JavaScript deferred](https://docs.wp-rocket.me/article/1265-load-javascript-deferred) i WP Rocket för att följa rekommendationen. Dessa funktioner optimerar respektive CSS- och JavaScript-filer så att de inte blockerar renderingen av sidan."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-css": {"message": "Aktivera [Minify CSS files](https://docs.wp-rocket.me/article/1350-css-minify-combine) i WP Rocket för att åtgärda problemet. Alla mellanrum och kommentarer tas bort från webbplatsens CSS-filer för att göra filen mindre så den går snabbare att ladda ned."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unminified-javascript": {"message": "Aktivera [Minify JavaScript files](https://docs.wp-rocket.me/article/1351-javascript-minify-combine) i WP Rocket för att åtgärda problemet. Mellanrum och kommentarer tas bort från JavaScript-filer för att göra filen mindre så den går snabbare att ladda ned."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-css-rules": {"message": "Aktivera [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) i WP Rocket för att åtgärda problemet. Funktionen minskar sidstorleken genom att ta bort all CSS och alla formatmallar som inte används och endast behålla den CSS som används för varje sida."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | unused-javascript": {"message": "Aktivera [Delay JavaScript execution](https://docs.wp-rocket.me/article/1349-delay-javascript-execution) i WP Rocket för att åtgärda problemet. Då förbättras inläsningen av sidan genom att körningen av skript fördröjs tills användaren interagerar. Om du har iframes på webbplatsen kan du även använda WP Rockets [LazyLoad for iframes and videos](https://docs.wp-rocket.me/article/1674-lazyload-for-iframes-and-videos) och [Replace YouTube iframe with preview image](https://docs.wp-rocket.me/article/1488-replace-youtube-iframe-with-preview-image)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-optimized-images": {"message": "Aktivera Imagify på fliken Image Optimization i WP Rocket och kör massoptimering för att komprimera dina bilder."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preconnect": {"message": "Använd [Prefetch DNS Requests](https://docs.wp-rocket.me/article/1302-prefetch-dns-requests) i WP Rocket för att lägga till dns-prefetch och förbättra hastigheten för anslutningen till externa domäner. WP Rocket lägger dessutom automatiskt till preconnect i [Google Fonts-domänen](https://docs.wp-rocket.me/article/1312-optimize-google-fonts) och eventuella CNAME lägg till via funktionen [Enable CDN](https://docs.wp-rocket.me/article/42-using-wp-rocket-with-a-cdn)."}, "node_modules/lighthouse-stack-packs/packs/wp-rocket.js | uses-rel-preload": {"message": "Åtgärda problemet med teckensnitt genom att aktivera [Remove Unused CSS](https://docs.wp-rocket.me/article/1529-remove-unused-css) i WP Rocket. Inläsning i förväg prioriteras för viktiga teckensnitt på webbplatsen."}, "report/renderer/report-utils.js | calculatorLink": {"message": "Visa kalkylator."}, "report/renderer/report-utils.js | collapseView": {"message": "Komprimera vy"}, "report/renderer/report-utils.js | crcInitialNavigation": {"message": "<PERSON><PERSON>rsta navigering"}, "report/renderer/report-utils.js | crcLongestDurationLabel": {"message": "Högsta latens för kritisk kedja:"}, "report/renderer/report-utils.js | dropdownCopyJSON": {"message": "Kopiera JSON"}, "report/renderer/report-utils.js | dropdownDarkTheme": {"message": "Aktivera och inaktivera mörkt tema"}, "report/renderer/report-utils.js | dropdownPrintExpanded": {"message": "Skriv ut utökad"}, "report/renderer/report-utils.js | dropdownPrintSummary": {"message": "Skriv ut sammanfattning"}, "report/renderer/report-utils.js | dropdownSaveGist": {"message": "Spara som Gist"}, "report/renderer/report-utils.js | dropdownSaveHTML": {"message": "Spara som HTML"}, "report/renderer/report-utils.js | dropdownSaveJSON": {"message": "Spara som JSON"}, "report/renderer/report-utils.js | dropdownViewer": {"message": "Öppna i visningsprogram"}, "report/renderer/report-utils.js | errorLabel": {"message": "<PERSON><PERSON>."}, "report/renderer/report-utils.js | errorMissingAuditInfo": {"message": "Rapportfel: ingen granskningsinformation"}, "report/renderer/report-utils.js | expandView": {"message": "Utöka vy"}, "report/renderer/report-utils.js | footerIssue": {"message": "Rapportera ett problem"}, "report/renderer/report-utils.js | hide": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | labDataTitle": {"message": "Labbdata"}, "report/renderer/report-utils.js | lsPerformanceCategoryDescription": {"message": "<PERSON><PERSON><PERSON> me<PERSON> [Lighthouse](https://developers.google.com/web/tools/lighthouse/) av den aktuella sidan i ett emulerat mobilnätverk. Värdena är uppskattningar och kan variera."}, "report/renderer/report-utils.js | manualAuditsGroupTitle": {"message": "Fler saker att kolla manuellt"}, "report/renderer/report-utils.js | notApplicableAuditsGroupTitle": {"message": "<PERSON><PERSON>"}, "report/renderer/report-utils.js | opportunityResourceColumnLabel": {"message": "Möjlighet"}, "report/renderer/report-utils.js | opportunitySavingsColumnLabel": {"message": "Uppskattad tidsbesparing"}, "report/renderer/report-utils.js | passedAuditsGroupTitle": {"message": "Godkända granskningar"}, "report/renderer/report-utils.js | runtimeAnalysisWindow": {"message": "<PERSON><PERSON><PERSON>a si<PERSON>mtningen"}, "report/renderer/report-utils.js | runtimeCustom": {"message": "Anpassad begränsning"}, "report/renderer/report-utils.js | runtimeDesktopEmulation": {"message": "Emulerat skrivbord"}, "report/renderer/report-utils.js | runtimeMobileEmulation": {"message": "Emulated Moto G Power"}, "report/renderer/report-utils.js | runtimeNoEmulation": {"message": "Ingen emulering"}, "report/renderer/report-utils.js | runtimeSettingsAxeVersion": {"message": "Axe-version"}, "report/renderer/report-utils.js | runtimeSettingsBenchmark": {"message": "Obegränsad CPU/minneskraft"}, "report/renderer/report-utils.js | runtimeSettingsCPUThrottling": {"message": "CPU-beg<PERSON><PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsDevice": {"message": "<PERSON><PERSON><PERSON>"}, "report/renderer/report-utils.js | runtimeSettingsNetworkThrottling": {"message": "Nätverksbegränsning"}, "report/renderer/report-utils.js | runtimeSettingsScreenEmulation": {"message": "Skärmemulering"}, "report/renderer/report-utils.js | runtimeSettingsUANetwork": {"message": "Användaragent (nätverk)"}, "report/renderer/report-utils.js | runtimeSingleLoad": {"message": "Enskild sidhämtning"}, "report/renderer/report-utils.js | runtimeSingleLoadTooltip": {"message": "Denna data kommer från en enskild sidhämtning, till skillnad från fältdata som sammanfattar flera sessioner."}, "report/renderer/report-utils.js | runtimeSlow4g": {"message": "Långsam 4G-begränsning"}, "report/renderer/report-utils.js | runtimeUnknown": {"message": "Okä<PERSON>"}, "report/renderer/report-utils.js | show": {"message": "Visa"}, "report/renderer/report-utils.js | showRelevantAudits": {"message": "Visa granskningar som är relevanta för:"}, "report/renderer/report-utils.js | snippetCollapseButtonLabel": {"message": "Komprimera utdrag"}, "report/renderer/report-utils.js | snippetExpandButtonLabel": {"message": "Utöka utdrag"}, "report/renderer/report-utils.js | thirdPartyResourcesLabel": {"message": "Visa resurser från tredje part"}, "report/renderer/report-utils.js | throttlingProvided": {"message": "Tillhandahålls av miljön"}, "report/renderer/report-utils.js | toplevelWarningsMessage": {"message": "Problem uppstod med den här körningen av Lighthouse."}, "report/renderer/report-utils.js | varianceDisclaimer": {"message": "Värdena är uppskattningar och kan variera. [Prestandapoängen beräknas](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) direkt utifrån dessa mätvärden."}, "report/renderer/report-utils.js | viewOriginalTraceLabel": {"message": "Visa ursprungligt spår"}, "report/renderer/report-utils.js | viewTraceLabel": {"message": "Visa spår"}, "report/renderer/report-utils.js | viewTreemapLabel": {"message": "Visa Treemap"}, "report/renderer/report-utils.js | warningAuditsGroupTitle": {"message": "Godkänd i granskningarna men med varningar"}, "report/renderer/report-utils.js | warningHeader": {"message": "Varningar: "}, "treemap/app/src/util.js | allLabel": {"message": "<PERSON>a"}, "treemap/app/src/util.js | allScriptsDropdownLabel": {"message": "<PERSON>a sk<PERSON>t"}, "treemap/app/src/util.js | coverageColumnName": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "treemap/app/src/util.js | duplicateModulesLabel": {"message": "Moduld<PERSON><PERSON>tter"}, "treemap/app/src/util.js | resourceBytesLabel": {"message": "Resursstorlek i byte"}, "treemap/app/src/util.js | tableColumnName": {"message": "<PERSON><PERSON>"}, "treemap/app/src/util.js | toggleTableButtonLabel": {"message": "Aktivera och inaktivera tabell"}, "treemap/app/src/util.js | unusedBytesLabel": {"message": "Byte som inte används"}}