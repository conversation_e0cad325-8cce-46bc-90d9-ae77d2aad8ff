/**
 * @license Copyright 2018 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */

declare module 'pretty-json-stringify' {
  interface Params {
    tab?: string;
    spaceBeforeColon?: string;
    spaceAfterColon?: string;
    spaceAfterComma?: string;
    spaceInsideObject?: string;
    spaceInsideArray?: string;
    shouldExpand?(obj: Object, level: number, index: string): Boolean;
  }

  function stringify(object: Object, params: Params): string;
  export = stringify;
}
