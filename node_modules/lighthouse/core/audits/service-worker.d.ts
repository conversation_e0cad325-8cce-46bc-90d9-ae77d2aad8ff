export default ServiceWorker;
declare class ServiceWorker extends Audit {
    /**
     * Find active service workers for this origin.
     * @param {Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>} versions
     * @param {URL} pageUrl
     * @return {Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>}
     */
    static getVersionsForOrigin(versions: Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>, pageUrl: URL): Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>;
    /**
     * From the set of active service workers for this origin, find the controlling SW (if any)
     * and return its scope URL.
     * @param {Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>} matchingSWVersions
     * @param {Array<LH.Crdp.ServiceWorker.ServiceWorkerRegistration>} registrations
     * @param {URL} pageUrl
     * @return {{scopeUrl: string; scriptUrl: string} | undefined}
     */
    static getControllingServiceWorker(matchingSWVersions: Array<LH.Crdp.ServiceWorker.ServiceWorkerVersion>, registrations: Array<LH.Crdp.ServiceWorker.ServiceWorkerRegistration>, pageUrl: URL): {
        scopeUrl: string;
        scriptUrl: string;
    } | undefined;
    /**
     * Returns a failure message if there is no start_url or if the start_url isn't
     * contolled by the scopeUrl.
     * @param {LH.Artifacts['WebAppManifest']} WebAppManifest
     * @param {string} scopeUrl
     * @return {LH.IcuMessage|undefined}
     */
    static checkStartUrl(WebAppManifest: LH.Artifacts['WebAppManifest'], scopeUrl: string): LH.IcuMessage | undefined;
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const explanationOutOfScope: string;
    const explanationNoManifest: string;
    const explanationBadManifest: string;
    const explanationBadStartUrl: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=service-worker.d.ts.map