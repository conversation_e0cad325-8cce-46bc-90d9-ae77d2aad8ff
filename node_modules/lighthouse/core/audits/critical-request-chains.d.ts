export default CriticalRequestChains;
declare class CriticalRequestChains extends Audit {
    /** @typedef {{depth: number, id: string, chainDuration: number, chainTransferSize: number, node: LH.Artifacts.CriticalRequestNode[string]}} CrcNodeInfo */
    /**
     * @param {LH.Artifacts.CriticalRequestNode} tree
     * @param {function(CrcNodeInfo): void} cb
     */
    static _traverse(tree: LH.Artifacts.CriticalRequestNode, cb: (arg0: {
        depth: number;
        id: string;
        chainDuration: number;
        chainTransferSize: number;
        node: LH.Artifacts.CriticalRequestNode[string];
    }) => void): void;
    /**
     * Get stats about the longest initiator chain (as determined by time duration)
     * @param {LH.Artifacts.CriticalRequestNode} tree
     * @return {{duration: number, length: number, transferSize: number}}
     */
    static _getLongestChain(tree: LH.Artifacts.CriticalRequestNode): {
        duration: number;
        length: number;
        transferSize: number;
    };
    /**
     * @param {LH.Artifacts.CriticalRequestNode} tree
     * @return {LH.Audit.Details.SimpleCriticalRequestNode}
     */
    static flattenRequests(tree: LH.Artifacts.CriticalRequestNode): LH.Audit.Details.SimpleCriticalRequestNode;
    /**
     * Audits the page to give a score for First Meaningful Paint.
     * @param {LH.Artifacts} artifacts The artifacts from the gather phase.
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const displayValue: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=critical-request-chains.d.ts.map