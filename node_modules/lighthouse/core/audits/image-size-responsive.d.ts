export default ImageSizeResponsive;
export type Result = {
    url: string;
    node: LH.Audit.Details.NodeValue;
    displayedSize: string;
    actualSize: string;
    actualPixels: number;
    expectedSize: string;
    expectedPixels: number;
};
export type ImageWithNaturalDimensions = LH.Artifacts.ImageElement & Required<Pick<LH.Artifacts.ImageElement, 'naturalDimensions'>>;
declare class ImageSizeResponsive extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const columnDisplayed: string;
    const columnActual: string;
    const columnExpected: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=image-size-responsive.d.ts.map