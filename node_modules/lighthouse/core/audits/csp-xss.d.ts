export default CspXss;
export type Finding = import('../lib/csp-evaluator.js').Finding;
declare class CspXss extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<{cspHeaders: string[], cspMetaTags: string[]}>}
     */
    static getRawCsps(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<{
        cspHeaders: string[];
        cspMetaTags: string[];
    }>;
    /**
     * @param {Finding} finding
     * @param {LH.IcuMessage=} severity
     * @return {LH.Audit.Details.TableItem}
     */
    static findingToTableItem(finding: Finding, severity?: LH.IcuMessage | undefined): LH.Audit.Details.TableItem;
    /**
     * @param {Finding[][]} syntaxFindings
     * @param {string[]} rawCsps
     * @return {LH.Audit.Details.TableItem[]}
     */
    static constructSyntaxResults(syntaxFindings: Finding[][], rawCsps: string[]): LH.Audit.Details.TableItem[];
    /**
     * @param {string[]} cspHeaders
     * @param {string[]} cspMetaTags
     * @return {{score: number, results: LH.Audit.Details.TableItem[]}}
     */
    static constructResults(cspHeaders: string[], cspMetaTags: string[]): {
        score: number;
        results: LH.Audit.Details.TableItem[];
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const noCsp: string;
    const metaTagMessage: string;
    const columnDirective: string;
    const columnSeverity: string;
    const itemSeveritySyntax: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=csp-xss.d.ts.map