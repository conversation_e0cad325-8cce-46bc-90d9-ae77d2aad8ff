export default AutocompleteAudit;
/**
 * The autocomplete attribute can have multiple tokens in it. All tokens should be valid and in the correct order.
 * For example, cc-namez is an invalid token. tel mobile shipping section-foo are valid tokens, but out of order. The spec defines correct ordering, but in short, correct order is:
 *
 * [section-*] [shipping|billing] [home|work|mobile|fax|pager] <autofill field name>
 *
 * If either of these invalid situations, the autocomplete property will be an empty string. */
declare class AutocompleteAudit extends Audit {
    /**
     * @param {LH.Artifacts.InputElement} input
     * @return {{hasValidTokens: boolean, isValidOrder?: boolean}}
     */
    static checkAttributeValidity(input: LH.Artifacts.InputElement): {
        hasValidTokens: boolean;
        isValidOrder?: boolean;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const columnSuggestions: string;
    const columnCurrent: string;
    const warningInvalid: string;
    const warningOrder: string;
    const reviewOrder: string;
    const manualReview: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=autocomplete.d.ts.map