export default FirstMeaningfulPaint;
declare class FirstMeaningfulPaint extends Audit {
    /**
     * @return {{mobile: {scoring: LH.Audit.ScoreOptions}, desktop: {scoring: LH.Audit.ScoreOptions}}}
     */
    static get defaultOptions(): {
        mobile: {
            scoring: LH.Audit.ScoreOptions;
        };
        desktop: {
            scoring: LH.Audit.ScoreOptions;
        };
    };
    /**
     * Audits the page to give a score for First Meaningful Paint.
     * @see https://github.com/GoogleChrome/lighthouse/issues/26
     * @see https://docs.google.com/document/d/1BR94tJdZLsin5poeet0XoTW60M0SjvOJQttKT-JK8HI/view
     * @param {LH.Artifacts} artifacts The artifacts from the gather phase.
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const description: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=first-meaningful-paint.d.ts.map