export default ContentWidth;
declare class ContentWidth extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const explanation: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=content-width.d.ts.map