export default ScreenshotThumbnails;
export type SpeedlineFrame = LH.Artifacts.Speedline['frames'][0];
/** @typedef {LH.Artifacts.Speedline['frames'][0]} SpeedlineFrame */
declare class ScreenshotThumbnails extends Audit {
    /**
     * Scales down an image to THUMBNAIL_WIDTH using nearest neighbor for speed, maintains aspect
     * ratio of the original thumbnail.
     *
     * @param {ReturnType<SpeedlineFrame['getParsedImage']>} imageData
     * @param {number} scaledWidth
     * @return {{width: number, height: number, data: Uint8Array}}
     */
    static scaleImageToThumbnail(imageData: ReturnType<SpeedlineFrame['getParsedImage']>, scaledWidth: number): {
        width: number;
        height: number;
        data: Uint8Array;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static _audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
import { Speedline } from "../computed/speedline.js";
import { Audit } from "./audit.js";
//# sourceMappingURL=screenshot-thumbnails.d.ts.map