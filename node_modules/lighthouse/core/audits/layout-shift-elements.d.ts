export default LayoutShiftElements;
declare class LayoutShiftElements extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const columnContribution: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=layout-shift-elements.d.ts.map