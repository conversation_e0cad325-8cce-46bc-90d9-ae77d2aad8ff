export type Simulator = import('../../lib/dependency-graph/simulator/simulator').Simulator;
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
export type ByteEfficiencyProduct = {
    items: Array<LH.Audit.ByteEfficiencyItem>;
    wastedBytesByUrl?: Map<string, number> | undefined;
    headings: LH.Audit.Details.Opportunity['headings'];
    displayValue?: import("../../index.js").IcuMessage | undefined;
    explanation?: import("../../index.js").IcuMessage | undefined;
    warnings?: (string | import("../../index.js").IcuMessage)[] | undefined;
    sortedBy?: string[] | undefined;
};
/**
 * @typedef {object} ByteEfficiencyProduct
 * @property {Array<LH.Audit.ByteEfficiencyItem>} items
 * @property {Map<string, number>=} wastedBytesByUrl
 * @property {LH.Audit.Details.Opportunity['headings']} headings
 * @property {LH.IcuMessage} [displayValue]
 * @property {LH.IcuMessage} [explanation]
 * @property {Array<string | LH.IcuMessage>} [warnings]
 * @property {Array<string>} [sortedBy]
 */
/**
 * @overview Used as the base for all byte efficiency audits. Computes total bytes
 *    and estimated time saved. Subclass and override `audit_` to return results.
 */
export class ByteEfficiencyAudit extends Audit {
    /**
     * Creates a score based on the wastedMs value using linear interpolation between control points.
     * A negative wastedMs is scored as 1, assuming time is not being wasted with respect to the
     * opportunity being measured.
     *
     * @param {number} wastedMs
     * @return {number}
     */
    static scoreForWastedMs(wastedMs: number): number;
    /**
     * Estimates the number of bytes this network record would have consumed on the network based on the
     * uncompressed size (totalBytes). Uses the actual transfer size from the network record if applicable.
     *
     * @param {LH.Artifacts.NetworkRequest|undefined} networkRecord
     * @param {number} totalBytes Uncompressed size of the resource
     * @param {LH.Crdp.Network.ResourceType=} resourceType
     * @return {number}
     */
    static estimateTransferSize(networkRecord: LH.Artifacts.NetworkRequest | undefined, totalBytes: number, resourceType?: LH.Crdp.Network.ResourceType | undefined): number;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
    /**
     * Computes the estimated effect of all the byte savings on the maximum of the following:
     *
     * - end time of the last long task in the provided graph
     * - (if includeLoad is true or not provided) end time of the last node in the graph
     *
     * @param {Array<LH.Audit.ByteEfficiencyItem>} results The array of byte savings results per resource
     * @param {Node} graph
     * @param {Simulator} simulator
     * @param {{includeLoad?: boolean, label?: string, providedWastedBytesByUrl?: Map<string, number>}=} options
     * @return {number}
     */
    static computeWasteWithTTIGraph(results: Array<LH.Audit.ByteEfficiencyItem>, graph: Node, simulator: Simulator, options?: {
        includeLoad?: boolean;
        label?: string;
        providedWastedBytesByUrl?: Map<string, number>;
    } | undefined): number;
    /**
     * @param {ByteEfficiencyProduct} result
     * @param {Node|null} graph
     * @param {Simulator} simulator
     * @param {LH.Artifacts['GatherContext']} gatherContext
     * @return {LH.Audit.Product}
     */
    static createAuditProduct(result: ByteEfficiencyProduct, graph: Node | null, simulator: Simulator, gatherContext: LH.Artifacts['GatherContext']): LH.Audit.Product;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {LH.Audit.Context} context
     * @return {ByteEfficiencyProduct|Promise<ByteEfficiencyProduct>}
     */
    static audit_(artifacts: LH.Artifacts, networkRecords: Array<LH.Artifacts.NetworkRequest>, context: LH.Audit.Context): ByteEfficiencyProduct | Promise<ByteEfficiencyProduct>;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=byte-efficiency-audit.d.ts.map