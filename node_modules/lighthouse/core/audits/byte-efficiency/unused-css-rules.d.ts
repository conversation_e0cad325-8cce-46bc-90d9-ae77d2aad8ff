export default UnusedCSSRules;
declare class UnusedCSSRules extends ByteEfficiencyAudit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Artifacts.NetworkRequest[]} _
     * @param {LH.Audit.Context} context
     * @return {Promise<import('./byte-efficiency-audit.js').ByteEfficiencyProduct>}
     */
    static audit_(artifacts: LH.Artifacts, _: LH.Artifacts.NetworkRequest[], context: LH.Audit.Context): Promise<import('./byte-efficiency-audit.js').ByteEfficiencyProduct>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
}
import { ByteEfficiencyAudit } from "./byte-efficiency-audit.js";
//# sourceMappingURL=unused-css-rules.d.ts.map