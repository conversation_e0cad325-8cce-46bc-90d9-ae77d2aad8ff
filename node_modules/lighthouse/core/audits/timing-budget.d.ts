export default TimingBudget;
export type BudgetItem = {
    metric: LH.Budget.TimingMetric;
    label: LH.IcuMessage;
    measurement?: LH.Audit.Details.NumericValue | number;
    overBudget?: LH.Audit.Details.NumericValue | number;
};
/** @typedef {{metric: LH.Budget.TimingMetric, label: LH.IcuMessage, measurement?: LH.Audit.Details.NumericValue|number, overBudget?: LH.Audit.Details.NumericValue|number}} BudgetItem */
declare class TimingBudget extends Audit {
    /**
     * @param {LH.Budget.TimingMetric} timingMetric
     * @return {LH.IcuMessage}
     */
    static getRowLabel(timingMetric: LH.Budget.TimingMetric): LH.IcuMessage;
    /**
     * @param {LH.Budget.TimingMetric} timingMetric
     * @param {LH.Artifacts.TimingSummary} summary
     * @return {number|undefined}
     */
    static getMeasurement(timingMetric: LH.Budget.TimingMetric, summary: LH.Artifacts.TimingSummary): number | undefined;
    /**
     * @param {LH.Util.Immutable<LH.Budget>} budget
     * @param {LH.Artifacts.TimingSummary} summary
     * @return {Array<BudgetItem>}
     */
    static tableItems(budget: LH.Util.Immutable<LH.Budget>, summary: LH.Artifacts.TimingSummary): Array<BudgetItem>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const columnTimingMetric: string;
    const columnMeasurement: string;
}
import { Budget } from "../config/budget.js";
import { Audit } from "./audit.js";
import { TimingSummary } from "../computed/metrics/timing-summary.js";
//# sourceMappingURL=timing-budget.d.ts.map