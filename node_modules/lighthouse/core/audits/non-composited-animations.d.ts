export default NonCompositedAnimations;
declare class NonCompositedAnimations extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const displayValue: string;
    const unsupportedCSSProperty: string;
    const transformDependsBoxSize: string;
    const filterMayMovePixels: string;
    const nonReplaceCompositeMode: string;
    const incompatibleAnimations: string;
    const unsupportedTimingParameters: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=non-composited-animations.d.ts.map