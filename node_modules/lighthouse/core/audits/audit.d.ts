export type TableOptions = {
    wastedMs?: number | undefined;
    wastedBytes?: number | undefined;
    sortedBy?: LH.Audit.Details.Table['sortedBy'] | undefined;
    skipSumming?: LH.Audit.Details.Table['skipSumming'] | undefined;
    isEntityGrouped?: LH.Audit.Details.Table['isEntityGrouped'] | undefined;
};
export type OpportunityOptions = {
    overallSavingsMs: number;
    overallSavingsBytes?: number | undefined;
    sortedBy?: LH.Audit.Details.Opportunity['sortedBy'] | undefined;
    skipSumming?: LH.Audit.Details.Opportunity['skipSumming'] | undefined;
    isEntityGrouped?: LH.Audit.Details.Opportunity['isEntityGrouped'] | undefined;
};
export class Audit {
    /**
     * @return {string}
     */
    static get DEFAULT_PASS(): string;
    /**
     * @return {LH.Audit.ScoreDisplayModes}
     */
    static get SCORING_MODES(): import("../../types/lhr/audit-result.js").ScoreDisplayModes;
    /**
     * @return {LH.Audit.Meta}
     */
    static get meta(): LH.Audit.Meta;
    /**
     * @return {Object}
     */
    static get defaultOptions(): Object;
    /**
     *
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {LH.Audit.Product|Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): LH.Audit.Product | Promise<LH.Audit.Product>;
    /**
     * Computes a score between 0 and 1 based on the measured `value`. Score is determined by
     * considering a log-normal distribution governed by two control points (the 10th
     * percentile value and the median value) and represents the percentage of sites that are
     * greater than `value`.
     *
     * Score characteristics:
     * - within [0, 1]
     * - rounded to two digits
     * - value must meet or beat a controlPoint value to meet or exceed its percentile score:
     *   - value > median will give a score < 0.5; value ≤ median will give a score ≥ 0.5.
     *   - value > p10 will give a score < 0.9; value ≤ p10 will give a score ≥ 0.9.
     * - values < p10 will get a slight boost so a score of 1 is achievable by a
     *   `value` other than those close to 0. Scores of > ~0.99524 end up rounded to 1.
     * @param {{median: number, p10: number}} controlPoints
     * @param {number} value
     * @return {number}
     */
    static computeLogNormalScore(controlPoints: {
        median: number;
        p10: number;
    }, value: number): number;
    /**
     * This catches typos in the `key` property of a heading definition of table/opportunity details.
     * Throws an error if any of keys referenced by headings don't exist in at least one of the items.
     *
     * @param {LH.Audit.Details.Table['headings']|LH.Audit.Details.Opportunity['headings']} headings
     * @param {LH.Audit.Details.Opportunity['items']|LH.Audit.Details.Table['items']} items
     */
    static assertHeadingKeysExist(headings: LH.Audit.Details.Table['headings'] | LH.Audit.Details.Opportunity['headings'], items: LH.Audit.Details.Opportunity['items'] | LH.Audit.Details.Table['items']): void;
    /**
     * @param {LH.Audit.Details.Table['headings']} headings
     * @param {LH.Audit.Details.Table['items']} results
     * @param {TableOptions=} options
     * @return {LH.Audit.Details.Table}
     */
    static makeTableDetails(headings: LH.Audit.Details.Table['headings'], results: LH.Audit.Details.Table['items'], options?: TableOptions | undefined): LH.Audit.Details.Table;
    /**
     * @param {LH.Audit.Details.List['items']} items
     * @return {LH.Audit.Details.List}
     */
    static makeListDetails(items: LH.Audit.Details.List['items']): LH.Audit.Details.List;
    /** @typedef {{
     * content: string;
     * title: string;
     * lineMessages: LH.Audit.Details.SnippetValue['lineMessages'];
     * generalMessages: LH.Audit.Details.SnippetValue['generalMessages'];
     * node?: LH.Audit.Details.NodeValue;
     * maxLineLength?: number;
     * maxLinesAroundMessage?: number;
     * }} SnippetInfo */
    /**
     * @param {SnippetInfo} snippetInfo
     * @return {LH.Audit.Details.SnippetValue}
     */
    static makeSnippetDetails({ content, title, lineMessages, generalMessages, node, maxLineLength, maxLinesAroundMessage, }: {
        content: string;
        title: string;
        lineMessages: LH.Audit.Details.SnippetValue['lineMessages'];
        generalMessages: LH.Audit.Details.SnippetValue['generalMessages'];
        node?: LH.Audit.Details.NodeValue | undefined;
        maxLineLength?: number | undefined;
        maxLinesAroundMessage?: number | undefined;
    }): LH.Audit.Details.SnippetValue;
    /**
     * @param {string} content
     * @param {number} maxLineLength
     * @return {LH.Audit.Details.SnippetValue['lines']}
     */
    static _makeSnippetLinesArray(content: string, maxLineLength: number): LH.Audit.Details.SnippetValue['lines'];
    /**
     * @param {LH.Audit.Details.Opportunity['headings']} headings
     * @param {LH.Audit.Details.Opportunity['items']} items
     * @param {OpportunityOptions} options
     * @return {LH.Audit.Details.Opportunity}
     */
    static makeOpportunityDetails(headings: LH.Audit.Details.Opportunity['headings'], items: LH.Audit.Details.Opportunity['items'], options: OpportunityOptions): LH.Audit.Details.Opportunity;
    /**
     * @param {LH.Artifacts.NodeDetails} node
     * @return {LH.Audit.Details.NodeValue}
     */
    static makeNodeItem(node: LH.Artifacts.NodeDetails): LH.Audit.Details.NodeValue;
    /**
     * @param {LH.Artifacts.Bundle} bundle
     * @param {number} generatedLine
     * @param {number} generatedColumn
     * @return {LH.Audit.Details.SourceLocationValue['original']}
     */
    static _findOriginalLocation(bundle: LH.Artifacts.Bundle, generatedLine: number, generatedColumn: number): LH.Audit.Details.SourceLocationValue['original'];
    /**
     * @param {string} url
     * @param {number} line 0-indexed
     * @param {number} column 0-indexed
     * @param {LH.Artifacts.Bundle=} bundle
     * @return {LH.Audit.Details.SourceLocationValue}
     */
    static makeSourceLocation(url: string, line: number, column: number, bundle?: LH.Artifacts.Bundle | undefined): LH.Audit.Details.SourceLocationValue;
    /**
     * @param {LH.Artifacts.ConsoleMessage} entry
     * @param {LH.Artifacts.Bundle=} bundle
     * @return {LH.Audit.Details.SourceLocationValue | undefined}
     */
    static makeSourceLocationFromConsoleMessage(entry: LH.Artifacts.ConsoleMessage, bundle?: LH.Artifacts.Bundle | undefined): LH.Audit.Details.SourceLocationValue | undefined;
    /**
     * @param {number|null} score
     * @param {LH.Audit.ScoreDisplayMode} scoreDisplayMode
     * @param {string} auditId
     * @return {number|null}
     */
    static _normalizeAuditScore(score: number | null, scoreDisplayMode: LH.Audit.ScoreDisplayMode, auditId: string): number | null;
    /**
     * @param {typeof Audit} audit
     * @param {string | LH.IcuMessage} errorMessage
     * @return {LH.RawIcu<LH.Audit.Result>}
     */
    static generateErrorAuditResult(audit: typeof Audit, errorMessage: string | LH.IcuMessage): LH.RawIcu<LH.Audit.Result>;
    /**
     * @param {typeof Audit} audit
     * @param {LH.Audit.Product} product
     * @return {LH.RawIcu<LH.Audit.Result>}
     */
    static generateAuditResult(audit: typeof Audit, product: LH.Audit.Product): LH.RawIcu<LH.Audit.Result>;
}
import * as LH from "../../types/lh.js";
//# sourceMappingURL=audit.d.ts.map