export default BootupTime;
declare class BootupTime extends Audit {
    /**
     * @return {LH.Audit.ScoreOptions & {thresholdInMs: number}}
     */
    static get defaultOptions(): import("../../types/audit.js").default.ScoreOptions & {
        thresholdInMs: number;
    };
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const columnTotal: string;
    const columnScriptEval: string;
    const columnScriptParse: string;
    const chromeExtensionsWarning: string;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=bootup-time.d.ts.map