export default CharsetDefined;
declare class CharsetDefined extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
}
export const CHARSET_HTML_REGEX: RegExp;
export const CHARSET_HTTP_REGEX: RegExp;
export const IANA_REGEX: RegExp;
import { Audit } from "../audit.js";
//# sourceMappingURL=charset.d.ts.map