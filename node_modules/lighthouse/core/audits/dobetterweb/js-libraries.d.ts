export default JsLibrariesAudit;
declare class JsLibrariesAudit extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const description: string;
    const columnVersion: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=js-libraries.d.ts.map