export default FontSize;
export type FailingNodeData = LH.Artifacts.FontSize['analyzedFailingNodesData'][0];
declare class FontSize extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const displayValue: string;
    const explanationViewport: string;
    const additionalIllegibleText: string;
    const legibleText: string;
    const columnSelector: string;
    const columnPercentPageText: string;
    const columnFontSize: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=font-size.d.ts.map