export default RobotsTxt;
declare class RobotsTxt extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit(artifacts: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const displayValueHttpBadCode: string;
    const displayValueValidationError: string;
    const explanation: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=robots-txt.d.ts.map