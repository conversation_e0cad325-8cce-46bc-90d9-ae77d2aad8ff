export default TapTargets;
export type ClientRectOverlapFailure = {
    overlapScoreRatio: number;
    tapTargetScore: number;
    overlappingTargetScore: number;
};
export type TapTargetOverlapFailure = {
    overlapScoreRatio: number;
    tapTargetScore: number;
    overlappingTargetScore: number;
    tapTarget: LH.Artifacts.TapTarget;
    overlappingTarget: LH.Artifacts.TapTarget;
};
export type BoundedTapTarget = {
    paddedBoundsRect: LH.Artifacts.Rect;
    tapTarget: LH.Artifacts.TapTarget;
};
export type TapTargetTableItem = {
    tapTarget: LH.Audit.Details.NodeValue;
    overlappingTarget: LH.Audit.Details.NodeValue;
    size: string;
    overlapScoreRatio: number;
    height: number;
    width: number;
    tapTargetScore: number;
    overlappingTargetScore: number;
};
declare class TapTargets extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
declare namespace TapTargets {
    export { FINGER_SIZE_PX };
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const tapTargetHeader: string;
    const overlappingTargetHeader: string;
    const explanationViewportMetaNotOptimized: string;
    const displayValue: string;
}
import { Audit } from "../audit.js";
declare const FINGER_SIZE_PX: 48;
//# sourceMappingURL=tap-targets.d.ts.map