export default Hreflang;
export type Source = string | LH.Audit.Details.NodeValue | undefined;
export type InvalidHreflang = {
    source: Source;
    subItems: {
        type: 'subitems';
        items: SubItem[];
    };
};
export type SubItem = {
    reason: LH.IcuMessage;
};
declare class Hreflang extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @return {LH.Audit.Product}
     */
    static audit({ LinkElements }: LH.Artifacts): LH.Audit.Product;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const unexpectedLanguage: string;
    const notFullyQualified: string;
}
import { Audit } from "../audit.js";
//# sourceMappingURL=hreflang.d.ts.map