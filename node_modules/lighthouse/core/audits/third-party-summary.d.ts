export default ThirdPartySummary;
export type Summary = {
    mainThreadTime: number;
    transferSize: number;
    blockingTime: number;
};
export type URLSummary = {
    transferSize: number;
    blockingTime: number;
    url: string | LH.IcuMessage;
};
export type SummaryMaps = {
    /**
     * Map of impact summaries for each entity.
     */
    byEntity: Map<LH.Artifacts.Entity, Summary>;
    /**
     * Map of impact summaries for each URL.
     */
    byURL: Map<string, Summary>;
    /**
     * Map of URLs under each entity.
     */
    urls: Map<LH.Artifacts.Entity, string[]>;
};
declare class ThirdPartySummary extends Audit {
    /**
     *
     * @param {Array<LH.Artifacts.NetworkRequest>} networkRecords
     * @param {Array<LH.Artifacts.TaskNode>} mainThreadTasks
     * @param {number} cpuMultiplier
     * @param {LH.Artifacts.EntityClassification} entityClassification
     * @return {SummaryMaps}
     */
    static getSummaries(networkRecords: Array<LH.Artifacts.NetworkRequest>, mainThreadTasks: Array<LH.Artifacts.TaskNode>, cpuMultiplier: number, entityClassification: LH.Artifacts.EntityClassification): SummaryMaps;
    /**
     * @param {LH.Artifacts.Entity} entity
     * @param {SummaryMaps} summaries
     * @param {Summary} stats
     * @return {Array<URLSummary>}
     */
    static makeSubItems(entity: LH.Artifacts.Entity, summaries: SummaryMaps, stats: Summary): Array<URLSummary>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
export namespace UIStrings {
    const title: string;
    const failureTitle: string;
    const description: string;
    const columnThirdParty: string;
    const displayValue: string;
}
import { Audit } from "./audit.js";
import { EntityClassification } from "../computed/entity-classification.js";
//# sourceMappingURL=third-party-summary.d.ts.map