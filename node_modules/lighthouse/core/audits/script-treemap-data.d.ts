export default ScriptTreemapDataAudit;
export type SourceData = Omit<LH.Treemap.Node, 'name' | 'children'>;
declare class ScriptTreemapDataAudit extends Audit {
    /**
     * Returns a tree data structure where leaf nodes are sources (ie. real files from source tree)
     * from a source map, and non-leaf nodes are directories. Leaf nodes have data
     * for bytes, coverage, etc., when available, and non-leaf nodes have the
     * same data as the sum of all descendant leaf nodes.
     * @param {string} src
     * @param {string} sourceRoot
     * @param {Record<string, SourceData>} sourcesData
     * @return {LH.Treemap.Node}
     */
    static makeScriptNode(src: string, sourceRoot: string, sourcesData: Record<string, SourceData>): LH.Treemap.Node;
    /**
     * Returns nodes where the first level of nodes are URLs.
     * Every external script has a node.
     * All inline scripts are combined into a single node.
     * If a script has a source map, that node will be created by makeScriptNode.
     *
     * Example return result:
       - index.html (inline scripts)
       - main.js
       - - webpack://
       - - - react.js
       - - - app.js
       - i-have-no-map.js
     *
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Treemap.Node[]>}
     */
    static makeNodes(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Treemap.Node[]>;
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @return {Promise<LH.Audit.Product>}
     */
    static audit(artifacts: LH.Artifacts, context: LH.Audit.Context): Promise<LH.Audit.Product>;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=script-treemap-data.d.ts.map