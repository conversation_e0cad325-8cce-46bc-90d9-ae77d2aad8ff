export default ViolationAudit;
declare class ViolationAudit extends Audit {
    /**
     * @param {LH.Artifacts} artifacts
     * @param {LH.Audit.Context} context
     * @param {RegExp} pattern
     * @return {Promise<Array<{source: LH.Audit.Details.SourceLocationValue}>>}
     */
    static getViolationResults(artifacts: LH.Artifacts, context: LH.Audit.Context, pattern: RegExp): Promise<Array<{
        source: LH.Audit.Details.SourceLocationValue;
    }>>;
}
import { Audit } from "./audit.js";
//# sourceMappingURL=violation-audit.d.ts.map