export { JSBundlesComputed as <PERSON><PERSON><PERSON><PERSON> };
declare const JSBundlesComputed: typeof JSBundles & {
    request: (dependencies: Pick<import("../index.js").Artifacts, "Scripts" | "SourceMaps">, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../index.js").Artifacts.Bundle[]>;
};
declare class JSBundles {
    /**
     * @param {Pick<LH.Artifacts, 'SourceMaps'|'Scripts'>} artifacts
     */
    static compute_(artifacts: Pick<LH.Artifacts, 'SourceMaps' | 'Scripts'>): Promise<import("../index.js").Artifacts.Bundle[]>;
}
//# sourceMappingURL=js-bundles.d.ts.map