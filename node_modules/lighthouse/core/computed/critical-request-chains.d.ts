export { CriticalRequestChainsComputed as CriticalRequest<PERSON>hai<PERSON> };
declare const CriticalRequestChainsComputed: typeof CriticalRequestChains & {
    request: (dependencies: {
        URL: LH.Artifacts['URL'];
        devtoolsLog: import("../index.js").DevtoolsLog;
        trace: LH.Trace;
    }, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../index.js").Artifacts.CriticalRequestNode>;
};
declare class CriticalRequestChains {
    /**
     * For now, we use network priorities as a proxy for "render-blocking"/critical-ness.
     * It's imperfect, but there is not a higher-fidelity signal available yet.
     * @see https://docs.google.com/document/d/1bCDuq9H1ih9iNjgzyAL0gpwNFiEP4TZS-YLRp_RuMlc
     * @param {LH.Artifacts.NetworkRequest} request
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @return {boolean}
     */
    static isCritical(request: LH.Artifacts.NetworkRequest, mainResource: LH.Artifacts.NetworkRequest): boolean;
    /**
     * Create a tree of critical requests.
     * @param {LH.Artifacts.NetworkRequest} mainResource
     * @param {LH.Gatherer.Simulation.GraphNode} graph
     * @return {LH.Artifacts.CriticalRequestNode}
     */
    static extractChainsFromGraph(mainResource: LH.Artifacts.NetworkRequest, graph: LH.Gatherer.Simulation.GraphNode): LH.Artifacts.CriticalRequestNode;
    /**
     * @param {{URL: LH.Artifacts['URL'], devtoolsLog: LH.DevtoolsLog, trace: LH.Trace}} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.CriticalRequestNode>}
     */
    static compute_(data: {
        URL: LH.Artifacts['URL'];
        devtoolsLog: import("../index.js").DevtoolsLog;
        trace: LH.Trace;
    }, context: LH.Artifacts.ComputedContext): Promise<LH.Artifacts.CriticalRequestNode>;
}
import { NetworkRequest } from "../lib/network-request.js";
//# sourceMappingURL=critical-request-chains.d.ts.map