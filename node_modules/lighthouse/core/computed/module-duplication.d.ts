export { ModuleDuplicationComputed as ModuleDuplication };
declare const ModuleDuplicationComputed: typeof ModuleDuplication & {
    request: (dependencies: Pick<import("../index.js").Artifacts, "Scripts" | "SourceMaps">, context: import("../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<Map<string, {
        scriptId: string;
        scriptUrl: string;
        resourceSize: number;
    }[]>>;
};
declare class ModuleDuplication {
    /**
     * @param {string} source
     */
    static normalizeSource(source: string): string;
    /**
     * @param {string} source
     */
    static _shouldIgnoreSource(source: string): boolean;
    /**
     * @param {Map<string, Array<{scriptId: string, resourceSize: number}>>} moduleNameToSourceData
     */
    static _normalizeAggregatedData(moduleNameToSourceData: Map<string, Array<{
        scriptId: string;
        resourceSize: number;
    }>>): void;
    /**
     * @param {Pick<LH.Artifacts, 'Scripts'|'SourceMaps'>} artifacts
     * @param {LH.Artifacts.ComputedContext} context
     */
    static compute_(artifacts: Pick<LH.Artifacts, 'Scripts' | 'SourceMaps'>, context: LH.Artifacts.ComputedContext): Promise<Map<string, {
        scriptId: string;
        scriptUrl: string;
        resourceSize: number;
    }[]>>;
}
//# sourceMappingURL=module-duplication.d.ts.map