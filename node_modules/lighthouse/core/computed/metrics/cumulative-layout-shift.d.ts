export { CumulativeLayoutShiftComputed as CumulativeLayoutShift };
export type LayoutShiftEvent = {
    ts: number;
    isMainFrame: boolean;
    weightedScore: number;
};
declare const CumulativeLayoutShiftComputed: typeof CumulativeLayoutShift & {
    request: (dependencies: import("../../index.js").Trace, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<{
        cumulativeLayoutShift: number;
        cumulativeLayoutShiftMainFrame: number;
        totalCumulativeLayoutShift: number;
    }>;
};
declare class CumulativeLayoutShift {
    /**
     * Returns all LayoutShift events that had no recent input.
     * Only a `weightedScore` per event is returned. For non-main-frame events, this is
     * the only score that matters. For main-frame events, `weighted_score_delta === score`.
     * @see https://source.chromium.org/chromium/chromium/src/+/main:third_party/blink/renderer/core/layout/layout_shift_tracker.cc;l=492-495;drc=de3b3a8a8839269c6b44403fa38a13a1ed12fed5
     * @param {LH.Artifacts.ProcessedTrace} processedTrace
     * @return {Array<LayoutShiftEvent>}
     */
    static getLayoutShiftEvents(processedTrace: LH.Artifacts.ProcessedTrace): Array<LayoutShiftEvent>;
    /**
     * Calculates cumulative layout shifts per cluster (session) of LayoutShift
     * events -- where a new cluster is created when there's a gap of more than
     * 1000ms since the last LayoutShift event or the cluster is greater than
     * 5000ms long -- and returns the max LayoutShift score found.
     * @param {Array<LayoutShiftEvent>} layoutShiftEvents
     * @return {number}
     */
    static calculate(layoutShiftEvents: Array<LayoutShiftEvent>): number;
    /**
     * Sum all layout shift events from the entire trace.
     * @param {Array<LayoutShiftEvent>} layoutShiftEvents
     * @return {number}
     */
    static calculateTotalCumulativeLayoutShift(layoutShiftEvents: Array<LayoutShiftEvent>): number;
    /**
     * @param {LH.Trace} trace
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<{cumulativeLayoutShift: number, cumulativeLayoutShiftMainFrame: number, totalCumulativeLayoutShift: number}>}
     */
    static compute_(trace: LH.Trace, context: LH.Artifacts.ComputedContext): Promise<{
        cumulativeLayoutShift: number;
        cumulativeLayoutShiftMainFrame: number;
        totalCumulativeLayoutShift: number;
    }>;
}
import { ProcessedTrace } from "../processed-trace.js";
//# sourceMappingURL=cumulative-layout-shift.d.ts.map