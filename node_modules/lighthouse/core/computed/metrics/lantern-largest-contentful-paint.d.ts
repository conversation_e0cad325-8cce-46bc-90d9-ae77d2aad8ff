export { LanternLargestContentfulPaintComputed as LanternLargestContentfulPaint };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternLargestContentfulPaintComputed: typeof LanternLargestContentfulPaint & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
declare class LanternLargestContentfulPaint extends LanternMetric {
    /**
     * Low priority image nodes are usually offscreen and very unlikely to be the
     * resource that is required for LCP. Our LCP graphs include everything except for these images.
     *
     * @param {Node} node
     * @return {boolean}
     */
    static isNotLowPriorityImageNode(node: Node): boolean;
    /**
     * @param {LH.Gatherer.Simulation.Result} simulationResult
     * @return {LH.Gatherer.Simulation.Result}
     */
    static getEstimateFromSimulation(simulationResult: LH.Gatherer.Simulation.Result): LH.Gatherer.Simulation.Result;
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-largest-contentful-paint.d.ts.map