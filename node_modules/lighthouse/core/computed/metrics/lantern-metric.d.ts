export type Node = import('../../lib/dependency-graph/base-node.js').Node;
export type NetworkNode = import('../../lib/dependency-graph/network-node').NetworkNode;
export type Simulator = import('../../lib/dependency-graph/simulator/simulator').Simulator;
export type Extras = {
    optimistic: boolean;
    fcpResult?: LH.Artifacts.LanternMetric | undefined;
    fmpResult?: LH.Artifacts.LanternMetric | undefined;
    interactiveResult?: LH.Artifacts.LanternMetric | undefined;
    speedline?: {
        speedIndex: number;
    } | undefined;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
/** @typedef {import('../../lib/dependency-graph/network-node').NetworkNode} NetworkNode */
/** @typedef {import('../../lib/dependency-graph/simulator/simulator').Simulator} Simulator */
/**
 * @typedef Extras
 * @property {boolean} optimistic
 * @property {LH.Artifacts.LanternMetric=} fcpResult
 * @property {LH.Artifacts.LanternMetric=} fmpResult
 * @property {LH.Artifacts.LanternMetric=} interactiveResult
 * @property {{speedIndex: number}=} speedline
 */
export class LanternMetric {
    /**
     * @param {Node} dependencyGraph
     * @param {function(NetworkNode):boolean=} condition
     * @return {Set<string>}
     */
    static getScriptUrls(dependencyGraph: Node, condition?: ((arg0: NetworkNode) => boolean) | undefined): Set<string>;
    /**
     * @return {LH.Gatherer.Simulation.MetricCoefficients}
     */
    static get COEFFICIENTS(): import("../../../types/gatherer.js").default.Simulation.MetricCoefficients;
    /**
     * Returns the coefficients, scaled by the throttling settings if needed by the metric.
     * Some lantern metrics (speed-index) use components in their estimate that are not
     * from the simulator. In this case, we need to adjust the coefficients as the target throttling
     * settings change.
     *
     * @param {number} rttMs
     * @return {LH.Gatherer.Simulation.MetricCoefficients}
     */
    static getScaledCoefficients(rttMs: number): LH.Gatherer.Simulation.MetricCoefficients;
    /**
     * @param {Node} dependencyGraph
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node, processedNavigation: LH.Artifacts.ProcessedNavigation): Node;
    /**
     * @param {Node} dependencyGraph
     * @param {LH.Artifacts.ProcessedNavigation} processedNavigation
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node, processedNavigation: LH.Artifacts.ProcessedNavigation): Node;
    /**
     * @param {LH.Gatherer.Simulation.Result} simulationResult
     * @param {Extras} extras
     * @return {LH.Gatherer.Simulation.Result}
     */
    static getEstimateFromSimulation(simulationResult: LH.Gatherer.Simulation.Result, extras: Extras): LH.Gatherer.Simulation.Result;
    /**
     * @param {LH.Artifacts.MetricComputationDataInput} data
     * @param {LH.Artifacts.ComputedContext} context
     * @param {Omit<Extras, 'optimistic'>=} extras
     * @return {Promise<LH.Artifacts.LanternMetric>}
     */
    static computeMetricWithGraphs(data: LH.Artifacts.MetricComputationDataInput, context: LH.Artifacts.ComputedContext, extras?: Omit<Extras, 'optimistic'> | undefined): Promise<LH.Artifacts.LanternMetric>;
    /**
     * @param {LH.Artifacts.MetricComputationDataInput} data
     * @param {LH.Artifacts.ComputedContext} context
     * @return {Promise<LH.Artifacts.LanternMetric>}
     */
    static compute_(data: LH.Artifacts.MetricComputationDataInput, context: LH.Artifacts.ComputedContext): Promise<LH.Artifacts.LanternMetric>;
}
import { ProcessedNavigation } from "../processed-navigation.js";
//# sourceMappingURL=lantern-metric.d.ts.map