export { LanternTotalBlockingTimeComputed as LanternTotalBlockingTime };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternTotalBlockingTimeComputed: typeof LanternTotalBlockingTime & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
declare class LanternTotalBlockingTime extends LanternMetric {
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {LH.Gatherer.Simulation.Result['nodeTimings']} nodeTimings
     * @param {number} minDurationMs
     */
    static getTopLevelEvents(nodeTimings: LH.Gatherer.Simulation.Result['nodeTimings'], minDurationMs: number): {
        start: number;
        end: number;
        duration: number;
    }[];
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-total-blocking-time.d.ts.map