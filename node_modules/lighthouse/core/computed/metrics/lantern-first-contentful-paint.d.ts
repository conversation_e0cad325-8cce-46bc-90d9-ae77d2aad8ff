export { LanternFirstContentfulPaintComputed as LanternFirstContentfulPaint };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
export type CPUNode = import('../../lib/dependency-graph/cpu-node').CPUNode;
export type NetworkNode = import('../../lib/dependency-graph/network-node').NetworkNode;
declare const LanternFirstContentfulPaintComputed: typeof LanternFirstContentfulPaint & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
/** @typedef {import('../../lib/dependency-graph/cpu-node').CPUNode} CPUNode */
/** @typedef {import('../../lib/dependency-graph/network-node').NetworkNode} NetworkNode */
declare class LanternFirstContentfulPaint extends LanternMetric {
    /**
     * This function computes the set of URLs that *appeared* to be render-blocking based on our filter,
     * *but definitely were not* render-blocking based on the timing of their EvaluateScript task.
     * It also computes the set of corresponding CPU node ids that were needed for the paint at the
     * given timestamp.
     *
     * @param {Node} graph
     * @param {number} filterTimestamp The timestamp used to filter out tasks that occured after our
     *    paint of interest. Typically this is First Contentful Paint or First Meaningful Paint.
     * @param {function(NetworkNode):boolean} blockingScriptFilter The function that determines which scripts
     *    should be considered *possibly* render-blocking.
     * @param {(function(CPUNode):boolean)=} extraBlockingCpuNodesToIncludeFilter The function that determines which CPU nodes
     *    should also be included in our blocking node IDs set.
     * @return {{definitelyNotRenderBlockingScriptUrls: Set<string>, blockingCpuNodeIds: Set<string>}}
     */
    static getBlockingNodeData(graph: Node, filterTimestamp: number, blockingScriptFilter: (arg0: NetworkNode) => boolean, extraBlockingCpuNodesToIncludeFilter?: ((arg0: CPUNode) => boolean) | undefined): {
        definitelyNotRenderBlockingScriptUrls: Set<string>;
        blockingCpuNodeIds: Set<string>;
    };
    /**
     * This function computes the graph required for the first paint of interest.
     *
     * @param {Node} dependencyGraph
     * @param {number} paintTs The timestamp used to filter out tasks that occured after our
     *    paint of interest. Typically this is First Contentful Paint or First Meaningful Paint.
     * @param {function(NetworkNode):boolean} blockingResourcesFilter The function that determines which resources
     *    should be considered *possibly* render-blocking.
     * @param {(function(CPUNode):boolean)=} extraBlockingCpuNodesToIncludeFilter The function that determines which CPU nodes
     *    should also be included in our blocking node IDs set.
     * @return {Node}
     */
    static getFirstPaintBasedGraph(dependencyGraph: Node, paintTs: number, blockingResourcesFilter: (arg0: NetworkNode) => boolean, extraBlockingCpuNodesToIncludeFilter?: ((arg0: CPUNode) => boolean) | undefined): Node;
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-first-contentful-paint.d.ts.map