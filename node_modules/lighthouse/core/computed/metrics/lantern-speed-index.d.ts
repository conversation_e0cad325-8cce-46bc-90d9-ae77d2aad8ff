export { LanternSpeedIndexComputed as LanternSpeedIndex };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternSpeedIndexComputed: typeof LanternSpeedIndex & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
declare class LanternSpeedIndex extends LanternMetric {
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node): Node;
    /**
     * Approximate speed index using layout events from the simulated node timings.
     * The layout-based speed index is the weighted average of the endTime of CPU nodes that contained
     * a 'Layout' task. log(duration) is used as the weight to stand for "significance" to the page.
     *
     * If no layout events can be found or the endTime of a CPU task is too early, FCP is used instead.
     *
     * This approach was determined after evaluating the accuracy/complexity tradeoff of many
     * different methods. Read more in the evaluation doc.
     *
     * @see https://docs.google.com/document/d/1qJWXwxoyVLVadezIp_Tgdk867G3tDNkkVRvUJSH3K1E/edit#
     * @param {LH.Gatherer.Simulation.Result['nodeTimings']} nodeTimings
     * @param {number} fcpTimeInMs
     * @return {number}
     */
    static computeLayoutBasedSpeedIndex(nodeTimings: LH.Gatherer.Simulation.Result['nodeTimings'], fcpTimeInMs: number): number;
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-speed-index.d.ts.map