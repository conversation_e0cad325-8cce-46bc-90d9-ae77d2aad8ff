export { LanternMaxPotentialFIDComputed as LanternMaxPotentialFID };
export type Node = import('../../lib/dependency-graph/base-node.js').Node;
declare const LanternMaxPotentialFIDComputed: typeof LanternMaxPotentialFID & {
    request: (dependencies: import("../../index.js").Artifacts.MetricComputationDataInput, context: import("../../../types/utility-types.js").default.ImmutableObject<{
        computedCache: Map<string, import("../../lib/arbitrary-equality-map.js").ArbitraryEqualityMap>;
    }>) => Promise<import("../../index.js").Artifacts.LanternMetric>;
};
/** @typedef {import('../../lib/dependency-graph/base-node.js').Node} Node */
declare class LanternMaxPotentialFID extends LanternMetric {
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getOptimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {Node} dependencyGraph
     * @return {Node}
     */
    static getPessimisticGraph(dependencyGraph: Node): Node;
    /**
     * @param {LH.Gatherer.Simulation.Result['nodeTimings']} nodeTimings
     * @param {number} fcpTimeInMs
     * @return {Array<{duration: number}>}
     */
    static getTimingsAfterFCP(nodeTimings: LH.Gatherer.Simulation.Result['nodeTimings'], fcpTimeInMs: number): Array<{
        duration: number;
    }>;
}
import { LanternMetric } from "./lantern-metric.js";
//# sourceMappingURL=lantern-max-potential-fid.d.ts.map