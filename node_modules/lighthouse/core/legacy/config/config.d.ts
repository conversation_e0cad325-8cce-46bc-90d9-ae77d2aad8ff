export type GathererConstructor = typeof import('../../gather/gatherers/gatherer.js').Gatherer;
export type Gatherer = InstanceType<GathererConstructor>;
/**
 * @implements {LH.Config.LegacyResolvedConfig}
 */
export class LegacyResolvedConfig implements LH.Config.LegacyResolvedConfig {
    /**
     * Resolves the provided config (inherits from extended config, if set), resolves
     * all referenced modules, and validates.
     * @param {LH.Config=} config If not provided, uses the default config.
     * @param {LH.Flags=} flags
     * @return {Promise<LegacyResolvedConfig>}
     */
    static fromJson(config?: LH.Config | undefined, flags?: LH.Flags | undefined): Promise<LegacyResolvedConfig>;
    /**
     * @param {LH.Config} baseJSON The JSON of the configuration to extend
     * @param {LH.Config} extendJSON The JSON of the extensions
     * @return {LH.Config}
     */
    static extendConfigJSON(baseJSON: LH.Config, extendJSON: LH.Config): LH.Config;
    /**
     * @param {LH.Config['passes']} passes
     * @return {?Array<Required<LH.Config.PassJson>>}
     */
    static augmentPassesWithDefaults(passes: LH.Config['passes']): Array<Required<LH.Config.PassJson>> | null;
    /**
     * Observed throttling methods (devtools/provided) require at least 5s of quiet for the metrics to
     * be computed. This method adjusts the quiet thresholds to the required minimums if necessary.
     * @param {LH.Config.Settings} settings
     * @param {?Array<Required<LH.Config.PassJson>>} passes
     */
    static adjustDefaultPassForThrottling(settings: LH.Config.Settings, passes: Array<Required<LH.Config.PassJson>> | null): void;
    /**
     * Filter out any unrequested items from the config, based on requested categories or audits.
     * @param {LegacyResolvedConfig} config
     */
    static filterConfigIfNeeded(config: LegacyResolvedConfig): void;
    /**
     * Filter out any unrequested categories or audits from the categories object.
     * @param {LegacyResolvedConfig['categories']} oldCategories
     * @param {LH.Config.Settings} settings
     * @return {{categories: LegacyResolvedConfig['categories'], requestedAuditNames: Set<string>}}
     */
    static filterCategoriesAndAudits(oldCategories: LegacyResolvedConfig['categories'], settings: LH.Config.Settings): {
        categories: LegacyResolvedConfig['categories'];
        requestedAuditNames: Set<string>;
    };
    /**
     * From some requested audits, return names of all required and optional artifacts
     * @param {LegacyResolvedConfig['audits']} audits
     * @return {Set<string>}
     */
    static getGatherersRequestedByAudits(audits: LegacyResolvedConfig['audits']): Set<string>;
    /**
     * Filters to only requested passes and gatherers, returning a new passes array.
     * @param {LegacyResolvedConfig['passes']} passes
     * @param {Set<string>} requestedGatherers
     * @return {LegacyResolvedConfig['passes']}
     */
    static generatePassesNeededByGatherers(passes: LegacyResolvedConfig['passes'], requestedGatherers: Set<string>): LegacyResolvedConfig['passes'];
    /**
     * Take an array of audits and audit paths and require any paths (possibly
     * relative to the optional `configDir`) using `resolveModulePath`,
     * leaving only an array of AuditDefns.
     * @param {LH.Config['audits']} audits
     * @param {string=} configDir
     * @return {Promise<LegacyResolvedConfig['audits']>}
     */
    static requireAudits(audits: LH.Config['audits'], configDir?: string | undefined): Promise<LegacyResolvedConfig['audits']>;
    /**
     * Takes an array of passes with every property now initialized except the
     * gatherers and requires them, (relative to the optional `configDir` if
     * provided) using `resolveModulePath`, returning an array of full Passes.
     * @param {?Array<Required<LH.Config.PassJson>>} passes
     * @param {string=} configDir
     * @return {Promise<LegacyResolvedConfig['passes']>}
     */
    static requireGatherers(passes: Array<Required<LH.Config.PassJson>> | null, configDir?: string | undefined): Promise<LegacyResolvedConfig['passes']>;
    /**
     * @deprecated `Config.fromJson` should be used instead.
     * @constructor
     * @param {LH.Config} config
     * @param {{settings: LH.Config.Settings, passes: ?LH.Config.Pass[], audits: ?LH.Config.AuditDefn[]}} opts
     */
    constructor(config: LH.Config, opts: {
        settings: LH.Config.Settings;
        passes: LH.Config.Pass[] | null;
        audits: LH.Config.AuditDefn[] | null;
    });
    /** @type {LH.Config.Settings} */
    settings: LH.Config.Settings;
    /** @type {?Array<LH.Config.Pass>} */
    passes: Array<LH.Config.Pass> | null;
    /** @type {?Array<LH.Config.AuditDefn>} */
    audits: Array<LH.Config.AuditDefn> | null;
    /** @type {?Record<string, LH.Config.Category>} */
    categories: Record<string, LH.Config.Category> | null;
    /** @type {?Record<string, LH.Config.Group>} */
    groups: Record<string, LH.Config.Group> | null;
    /**
     * Provides a cleaned-up, stringified version of this config. Gatherer and
     * Audit `implementation` and `instance` do not survive this process.
     * @return {string}
     */
    getPrintString(): string;
}
//# sourceMappingURL=config.d.ts.map