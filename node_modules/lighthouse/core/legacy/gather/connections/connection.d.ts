export type ProtocolEventRecord = {
    'protocolevent': [LH.Protocol.RawEventMessage];
};
export type CrdpEventMessageEmitter = LH.Protocol.StrictEventEmitter<ProtocolEventRecord>;
export type CommandInfo = LH.CrdpCommands[keyof LH.CrdpCommands];
export type CommandCallback = {
    resolve: (arg0: Promise<CommandInfo['returnType']>) => void;
    method: keyof LH.CrdpCommands;
};
/**
 * @typedef {{'protocolevent': [LH.Protocol.RawEventMessage]}} ProtocolEventRecord
 * @typedef {LH.Protocol.StrictEventEmitter<ProtocolEventRecord>} CrdpEventMessageEmitter
 * @typedef {LH.CrdpCommands[keyof LH.CrdpCommands]} CommandInfo
 * @typedef {{resolve: function(Promise<CommandInfo['returnType']>): void, method: keyof LH.CrdpCommands}} CommandCallback
 */
export class Connection {
    _lastCommandId: number;
    /** @type {Map<number, CommandCallback>} */
    _callbacks: Map<number, CommandCallback>;
    _eventEmitter: CrdpEventMessageEmitter | null;
    /**
     * @return {Promise<void>}
     */
    connect(): Promise<void>;
    /**
     * @return {Promise<void>}
     */
    disconnect(): Promise<void>;
    /**
     * @return {Promise<string>}
     */
    wsEndpoint(): Promise<string>;
    /**
     * Call protocol methods
     * @template {keyof LH.CrdpCommands} C
     * @param {C} method
     * @param {string|undefined} sessionId
     * @param {LH.CrdpCommands[C]['paramsType']} paramArgs,
     * @return {Promise<LH.CrdpCommands[C]['returnType']>}
     */
    sendCommand<C extends keyof LH.CrdpCommands>(method: C, sessionId: string | undefined, ...paramArgs: LH.CrdpCommands[C]["paramsType"]): Promise<LH.CrdpCommands[C]["returnType"]>;
    /**
     * Bind listeners for connection events.
     * @param {'protocolevent'} eventName
     * @param {function(LH.Protocol.RawEventMessage): void} cb
     */
    on(eventName: 'protocolevent', cb: (arg0: LH.Protocol.RawEventMessage) => void): void;
    /**
     * Unbind listeners for connection events.
     * @param {'protocolevent'} eventName
     * @param {function(LH.Protocol.RawEventMessage): void} cb
     */
    off(eventName: 'protocolevent', cb: (arg0: LH.Protocol.RawEventMessage) => void): void;
    /**
     * @param {string} message
     * @protected
     */
    protected sendRawMessage(message: string): void;
    /**
     * @param {string} message
     * @return {void}
     * @protected
     */
    protected handleRawMessage(message: string): void;
    /**
     * @param {LH.Protocol.RawEventMessage} eventMessage
     */
    emitProtocolEvent(eventMessage: LH.Protocol.RawEventMessage): void;
    /**
     * @protected
     */
    protected dispose(): void;
}
import * as LH from "../../../../types/lh.js";
//# sourceMappingURL=connection.d.ts.map