export class CriConnection extends Connection {
    /**
     * @param {number=} port Optional port number. Defaults to 9222;
     * @param {string=} hostname Optional hostname. Defaults to localhost.
     * @constructor
     */
    constructor(port?: number | undefined, hostname?: string | undefined);
    port: number;
    hostname: string;
    _ws: WebSocket | null;
    _pageId: string | null;
    /**
     * @param {LH.DevToolsJsonTarget} response
     * @return {Promise<void>}
     * @private
     */
    private _connectToSocket;
    /**
     * @param {string} command
     * @return {Promise<LH.DevToolsJsonTarget | Array<LH.DevToolsJsonTarget> | {message: string}>}
     * @private
     */
    private _runJsonCommand;
}
import { Connection } from "./connection.js";
import WebSocket from "ws";
//# sourceMappingURL=cri.d.ts.map