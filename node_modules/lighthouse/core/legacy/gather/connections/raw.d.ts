export type Port = {
    on: (eventName: 'message' | 'close', cb: ((arg: string) => void) | (() => void)) => void;
    send: (message: string) => void;
    close: () => void;
};
/**
 * @typedef {object} Port
 * @property {(eventName: 'message'|'close', cb: ((arg: string) => void) | (() => void)) => void} on
 * @property {(message: string) => void} send
 * @property {() => void} close
 */
export class RawConnection extends Connection {
    /**
     * @param {Port} port
     */
    constructor(port: Port);
    _port: Port;
}
import { Connection } from "./connection.js";
//# sourceMappingURL=raw.d.ts.map