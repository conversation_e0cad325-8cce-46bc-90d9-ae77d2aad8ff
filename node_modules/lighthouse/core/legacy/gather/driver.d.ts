export type CrdpEventEmitter = LH.Protocol.StrictEventEmitter<LH.CrdpEvents>;
/**
 * @typedef {LH.Protocol.StrictEventEmitter<LH.CrdpEvents>} CrdpEventEmitter
 */
/**
 * @implements {LH.Gatherer.FRTransitionalDriver}
 */
export class Driver implements LH.Gatherer.FRTransitionalDriver {
    /** @deprecated - Not available on Fraggle Rock driver. */
    static get traceCategories(): string[];
    /**
     * @param {import('./connections/connection.js').Connection} connection
     */
    constructor(connection: import('./connections/connection.js').Connection);
    /**
     * @pri_vate (This should be private, but that makes our tests harder).
     * An event emitter that enforces mapping between Crdp event names and payload types.
     */
    _eventEmitter: CrdpEventEmitter;
    /**
     * @private
     * Used to save network and lifecycle protocol traffic. Just Page and Network are needed.
     */
    private _devtoolsLog;
    /**
     * @private
     * @type {Map<string, number>}
     */
    private _domainEnabledCounts;
    /**
     * @type {number}
     * @private
     */
    private _nextProtocolTimeout;
    online: boolean;
    executionContext: ExecutionContext;
    defaultSession: Driver;
    fetcher: Fetcher;
    _connection: import("./connections/connection.js").Connection;
    /** @private @deprecated Only available for plugin backcompat. */
    private evaluate;
    /** @private @deprecated Only available for plugin backcompat. */
    private evaluateAsync;
    targetManager: {
        rootSession: () => Driver;
        /**
         * Bind to *any* protocol event.
         * @param {'protocolevent'} event
         * @param {(payload: LH.Protocol.RawEventMessage) => void} callback
         */
        on: (event: 'protocolevent', callback: (payload: LH.Protocol.RawEventMessage) => void) => void;
        /**
         * Unbind to *any* protocol event.
         * @param {'protocolevent'} event
         * @param {(payload: LH.Protocol.RawEventMessage) => void} callback
         */
        off: (event: 'protocolevent', callback: (payload: LH.Protocol.RawEventMessage) => void) => void;
    };
    /**
     * @return {Promise<LH.Crdp.Browser.GetVersionResponse & {milestone: number}>}
     */
    getBrowserVersion(): Promise<LH.Crdp.Browser.GetVersionResponse & {
        milestone: number;
    }>;
    /**
     * @return {Promise<void>}
     */
    connect(): Promise<void>;
    /**
     * @return {Promise<void>}
     */
    disconnect(): Promise<void>;
    /** @return {Promise<void>} */
    dispose(): Promise<void>;
    /**
     * Get the browser WebSocket endpoint for devtools protocol clients like Puppeteer.
     * Only works with WebSocket connection, not extension or devtools.
     * @return {Promise<string>}
     */
    wsEndpoint(): Promise<string>;
    /**
     * Bind listeners for protocol events.
     * @template {keyof LH.CrdpEvents} E
     * @param {E} eventName
     * @param {(...args: LH.CrdpEvents[E]) => void} cb
     */
    on<E extends keyof LH.CrdpEvents>(eventName: E, cb: (...args: LH.CrdpEvents[E]) => void): void;
    /**
     * Bind a one-time listener for protocol events. Listener is removed once it
     * has been called.
     * @template {keyof LH.CrdpEvents} E
     * @param {E} eventName
     * @param {(...args: LH.CrdpEvents[E]) => void} cb
     */
    once<E_1 extends keyof LH.CrdpEvents>(eventName: E_1, cb: (...args: LH.CrdpEvents[E_1]) => void): void;
    /**
     * Unbind event listener.
     * @template {keyof LH.CrdpEvents} E
     * @param {E} eventName
     * @param {Function} cb
     */
    off<E_2 extends keyof LH.CrdpEvents>(eventName: E_2, cb: Function): void;
    /** @param {LH.Crdp.Target.TargetInfo} targetInfo */
    setTargetInfo(targetInfo: LH.Crdp.Target.TargetInfo): void;
    /**
     * Debounce enabling or disabling domains to prevent driver users from
     * stomping on each other. Maintains an internal count of the times a domain
     * has been enabled. Returns false if the command would have no effect (domain
     * is already enabled or disabled), or if command would interfere with another
     * user of that domain (e.g. two gatherers have enabled a domain, both need to
     * disable it for it to be disabled). Returns true otherwise.
     * @param {string} domain
     * @param {string|undefined} sessionId
     * @param {boolean} enable
     * @return {boolean}
     * @private
     */
    private _shouldToggleDomain;
    /**
     * @return {boolean}
     */
    hasNextProtocolTimeout(): boolean;
    /**
     * @return {number}
     */
    getNextProtocolTimeout(): number;
    /**
     * timeout is used for the next call to 'sendCommand'.
     * NOTE: This can eventually be replaced when TypeScript
     * resolves https://github.com/Microsoft/TypeScript/issues/5453.
     * @param {number} timeout
     */
    setNextProtocolTimeout(timeout: number): void;
    /**
     * @param {LH.Protocol.RawEventMessage} event
     */
    _handleProtocolEvent(event: LH.Protocol.RawEventMessage): void;
    /**
     * @param {Error} error
     */
    _handleEventError(error: Error): void;
    /**
     * @param {LH.Crdp.Target.AttachedToTargetEvent} event
     */
    _handleTargetAttached(event: LH.Crdp.Target.AttachedToTargetEvent): Promise<void>;
    /**
     * Call protocol methods, with a timeout.
     * To configure the timeout for the next call, use 'setNextProtocolTimeout'.
     * If 'sessionId' is undefined, the message is sent to the main session.
     * @template {keyof LH.CrdpCommands} C
     * @param {C} method
     * @param {string|undefined} sessionId
     * @param {LH.CrdpCommands[C]['paramsType']} params
     * @return {Promise<LH.CrdpCommands[C]['returnType']>}
     */
    sendCommandToSession<C extends keyof LH.CrdpCommands>(method: C, sessionId: string | undefined, ...params: LH.CrdpCommands[C]["paramsType"]): Promise<LH.CrdpCommands[C]["returnType"]>;
    /**
     * Alias for 'sendCommandToSession(method, undefined, ...params)'
     * @template {keyof LH.CrdpCommands} C
     * @param {C} method
     * @param {LH.CrdpCommands[C]['paramsType']} params
     * @return {Promise<LH.CrdpCommands[C]['returnType']>}
     */
    sendCommand<C_1 extends keyof LH.CrdpCommands>(method: C_1, ...params: LH.CrdpCommands[C_1]["paramsType"]): Promise<LH.CrdpCommands[C_1]["returnType"]>;
    /**
     * Call protocol methods.
     * @private
     * @template {keyof LH.CrdpCommands} C
     * @param {C} method
     * @param {string|undefined} sessionId
     * @param {LH.CrdpCommands[C]['paramsType']} params
     * @return {Promise<LH.CrdpCommands[C]['returnType']>}
     */
    private _innerSendCommand;
    /**
     * Returns whether a domain is currently enabled.
     * @param {string} domain
     * @return {boolean}
     */
    isDomainEnabled(domain: string): boolean;
    /**
     * Return the body of the response with the given ID. Rejects if getting the
     * body times out.
     * @param {string} requestId
     * @param {number} [timeout]
     * @return {Promise<string>}
     */
    getRequestContent(requestId: string, timeout?: number | undefined): Promise<string>;
    /**
     * @param {{additionalTraceCategories?: string|null}=} settings
     * @return {Promise<void>}
     */
    beginTrace(settings?: {
        additionalTraceCategories?: string | null;
    } | undefined): Promise<void>;
    /**
     * @return {Promise<LH.Trace>}
     */
    endTrace(): Promise<LH.Trace>;
    /**
     * Begin recording devtools protocol messages.
     */
    beginDevtoolsLog(): Promise<void>;
    _disableAsyncStacks: (() => Promise<void>) | undefined;
    /**
     * Stop recording to devtoolsLog and return log contents.
     * @return {Promise<LH.DevtoolsLog>}
     */
    endDevtoolsLog(): Promise<LH.DevtoolsLog>;
    url(): Promise<string>;
}
import * as LH from "../../../types/lh.js";
import { ExecutionContext } from "../../gather/driver/execution-context.js";
import { Fetcher } from "../../gather/fetcher.js";
//# sourceMappingURL=driver.d.ts.map