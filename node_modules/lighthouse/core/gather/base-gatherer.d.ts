export default FRGatherer;
/**
 * Base class for all gatherers supporting both Fraggle Rock and the legacy flow.
 * Most extending classes should implement the Fraggle Rock API and let this class handle translation.
 * See core/gather/gatherers/gatherer.js for legacy method explanations.
 *
 * @implements {LH.Gatherer.GathererInstance}
 * @implements {LH.Gatherer.FRGathererInstance}
 */
declare class FRGatherer implements LH.Gatherer.GathererInstance, LH.Gatherer.FRGathererInstance {
    /** @type {LH.Gatherer.GathererMeta} */
    meta: LH.Gatherer.GathererMeta;
    /**
     * Method to start observing a page for an arbitrary period of time.
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<void>|void}
     */
    startInstrumentation(passContext: LH.Gatherer.FRTransitionalContext): Promise<void> | void;
    /**
     * Method to start observing a page when the measurements are very sensitive and
     * should observe as little Lighthouse-induced work as possible.
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<void>|void}
     */
    startSensitiveInstrumentation(passContext: LH.Gatherer.FRTransitionalContext): Promise<void> | void;
    /**
     * Method to stop observing a page when the measurements are very sensitive and
     * should observe as little Lighthouse-induced work as possible.
     *
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<void>|void}
     */
    stopSensitiveInstrumentation(passContext: LH.Gatherer.FRTransitionalContext): Promise<void> | void;
    /**
     * Method to end observing a page after an arbitrary period of time.
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<void>|void}
     */
    stopInstrumentation(passContext: LH.Gatherer.FRTransitionalContext): Promise<void> | void;
    /**
     * Method to gather results about a page.
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {LH.Gatherer.PhaseResult}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): LH.Gatherer.PhaseResult;
    /**
     * Legacy property used to define the artifact ID. In Fraggle Rock, the artifact ID lives on the config.
     * @return {keyof LH.GathererArtifacts}
     */
    get name(): keyof LH.GathererArtifacts;
    /**
     * Legacy method. Called before navigation to target url, roughly corresponds to `startInstrumentation`.
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<LH.Gatherer.PhaseResultNonPromise>}
     */
    beforePass(passContext: LH.Gatherer.PassContext): Promise<LH.Gatherer.PhaseResultNonPromise>;
    /**
     * Legacy method. Should never be used by a Fraggle Rock gatherer, here for compat only.
     * @param {LH.Gatherer.PassContext} passContext
     * @return {LH.Gatherer.PhaseResult}
     */
    pass(passContext: LH.Gatherer.PassContext): LH.Gatherer.PhaseResult;
    /**
     * Legacy method. Roughly corresponds to `stopInstrumentation` or `getArtifact` depending on type of gatherer.
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Gatherer.PhaseResultNonPromise>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Gatherer.PhaseResultNonPromise>;
}
import * as LH from "../../types/lh.js";
//# sourceMappingURL=base-gatherer.d.ts.map