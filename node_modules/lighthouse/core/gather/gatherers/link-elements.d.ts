export default LinkElements;
declare class LinkElements extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['LinkElements']>}
     */
    static getLinkElementsInDOM(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['LinkElements']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts['DevtoolsLog']} devtoolsLog
     * @return {Promise<LH.Artifacts['LinkElements']>}
     */
    static getLinkElementsInHeaders(context: LH.Gatherer.FRTransitionalContext, devtoolsLog: LH.Artifacts['DevtoolsLog']): Promise<LH.Artifacts['LinkElements']>;
    /**
     * This needs to be in the constructor.
     * https://github.com/GoogleChrome/lighthouse/issues/12134
     * @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>}
     */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts['DevtoolsLog']} devtoolsLog
     * @return {Promise<LH.Artifacts['LinkElements']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, devtoolsLog: LH.Artifacts['DevtoolsLog']): Promise<LH.Artifacts['LinkElements']>;
    /**
     * @param {LH.Gatherer.PassContext} context
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts['LinkElements']>}
     */
    afterPass(context: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts['LinkElements']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['LinkElements']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['LinkElements']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=link-elements.d.ts.map