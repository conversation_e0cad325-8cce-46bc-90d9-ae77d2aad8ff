export default ConsoleMessages;
declare class ConsoleMessages extends FRGatherer {
    /** @type {LH.Artifacts.ConsoleMessage[]} */
    _logEntries: LH.Artifacts.ConsoleMessage[];
    _onConsoleAPICalled: (event: LH.Crdp.Runtime.ConsoleAPICalledEvent) => void;
    _onExceptionThrown: (event: LH.Crdp.Runtime.ExceptionThrownEvent) => void;
    _onLogEntryAdded: (event: LH.Crdp.Log.EntryAddedEvent) => void;
    /**
     * Handles events for when a script invokes a console API.
     * @param {LH.Crdp.Runtime.ConsoleAPICalledEvent} event
     */
    onConsoleAPICalled(event: LH.Crdp.Runtime.ConsoleAPICalledEvent): void;
    /**
     * Handles exception thrown events.
     * @param {LH.Crdp.Runtime.ExceptionThrownEvent} event
     */
    onExceptionThrown(event: LH.Crdp.Runtime.ExceptionThrownEvent): void;
    /**
     * <PERSON>les browser reports logged to the console, including interventions,
     * deprecations, violations, and more.
     * @param {LH.Crdp.Log.EntryAddedEvent} event
     */
    onLogEntry(event: LH.Crdp.Log.EntryAddedEvent): void;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     */
    startInstrumentation(passContext: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<void>}
     */
    stopInstrumentation({ driver }: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @return {Promise<LH.Artifacts['ConsoleMessages']>}
     */
    getArtifact(): Promise<LH.Artifacts['ConsoleMessages']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=console-messages.d.ts.map