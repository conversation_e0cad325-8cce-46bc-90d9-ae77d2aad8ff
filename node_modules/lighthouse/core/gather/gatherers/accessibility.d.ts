export default Accessibility;
declare class Accessibility extends FRGatherer {
    static pageFns: {
        runA11yChecks: typeof runA11yChecks;
        createAxeRuleResultArtifact: typeof createAxeRuleResultArtifact;
    };
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts.Accessibility>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts.Accessibility>;
}
import FRGatherer from "../base-gatherer.js";
/**
 * @return {Promise<LH.Artifacts.Accessibility>}
 */
declare function runA11yChecks(): Promise<LH.Artifacts.Accessibility>;
/**
 * @param {import('axe-core/axe').Result} result
 * @return {LH.Artifacts.AxeRuleResult}
 */
declare function createAxeRuleResultArtifact(result: import('axe-core/axe').Result): LH.Artifacts.AxeRuleResult;
//# sourceMappingURL=accessibility.d.ts.map