export default AnchorElements;
declare class AnchorElements extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['AnchorElements']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['AnchorElements']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=anchor-elements.d.ts.map