export default SourceMaps;
/**
 * @fileoverview Gets JavaScript source maps.
 */
declare class SourceMaps extends FRGatherer {
    /** @type {LH.Crdp.Debugger.ScriptParsedEvent[]} */
    _scriptParsedEvents: LH.Crdp.Debugger.ScriptParsedEvent[];
    /**
     * @param {LH.Crdp.Debugger.ScriptParsedEvent} event
     */
    onScriptParsed(event: LH.Crdp.Debugger.ScriptParsedEvent): void;
    /**
     * @param {LH.Gatherer.FRTransitionalDriver} driver
     * @param {string} sourceMapUrl
     * @return {Promise<LH.Artifacts.RawSourceMap>}
     */
    fetchSourceMap(driver: LH.Gatherer.FRTransitionalDriver, sourceMapUrl: string): Promise<LH.Artifacts.RawSourceMap>;
    /**
     * @param {string} sourceMapURL
     * @return {LH.Artifacts.RawSourceMap}
     */
    parseSourceMapFromDataUrl(sourceMapURL: string): LH.Artifacts.RawSourceMap;
    /**
     * @param {string} url
     * @param {string} base
     * @return {string|undefined}
     */
    _resolveUrl(url: string, base: string): string | undefined;
    /**
     * @param {LH.Gatherer.FRTransitionalDriver} driver
     * @param {LH.Crdp.Debugger.ScriptParsedEvent} event
     * @return {Promise<LH.Artifacts.SourceMap>}
     */
    _retrieveMapFromScriptParsedEvent(driver: LH.Gatherer.FRTransitionalDriver, event: LH.Crdp.Debugger.ScriptParsedEvent): Promise<LH.Artifacts.SourceMap>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startSensitiveInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopSensitiveInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['SourceMaps']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['SourceMaps']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=source-maps.d.ts.map