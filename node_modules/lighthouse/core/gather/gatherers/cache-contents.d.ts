export default CacheContents;
declare class CacheContents extends FRGatherer {
    /**
     * Creates an array of cached URLs.
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['CacheContents']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['CacheContents']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=cache-contents.d.ts.map