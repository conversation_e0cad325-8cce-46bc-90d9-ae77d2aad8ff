export default Scripts;
/**
 * @fileoverview Gets JavaScript file contents.
 */
declare class Scripts extends FRGatherer {
    /** @type {LH.Crdp.Debugger.ScriptParsedEvent[]} */
    _scriptParsedEvents: LH.Crdp.Debugger.ScriptParsedEvent[];
    /** @type {Array<string | undefined>} */
    _scriptContents: Array<string | undefined>;
    /** @type {string|null|undefined} */
    _mainSessionId: string | null | undefined;
    /**
     * @param {LH.Crdp.Debugger.ScriptParsedEvent} params
     */
    onScriptParsed(params: LH.Crdp.Debugger.ScriptParsedEvent): void;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    getArtifact(): Promise<import("../../index.js").Artifacts.Script[]>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=scripts.d.ts.map