export default InstallabilityErrors;
declare class InstallabilityErrors extends FR<PERSON>atherer {
    /**
     * Creates an Artifacts.InstallabilityErrors, tranforming data from the protocol
     * for old versions of Chrome.
     * @param {LH.Gatherer.FRProtocolSession} session
     * @return {Promise<LH.Artifacts['InstallabilityErrors']>}
     */
    static getInstallabilityErrors(session: LH.Gatherer.FRProtocolSession): Promise<LH.Artifacts['InstallabilityErrors']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['InstallabilityErrors']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['InstallabilityErrors']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=installability-errors.d.ts.map