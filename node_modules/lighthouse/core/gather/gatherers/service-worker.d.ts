export default ServiceWorker;
declare class ServiceWorker extends FRGatherer {
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @return {Promise<LH.Artifacts['ServiceWorker']>}
     */
    beforePass(passContext: LH.Gatherer.PassContext): Promise<LH.Artifacts['ServiceWorker']>;
    afterPass(): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['ServiceWorker']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['ServiceWorker']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=service-worker.d.ts.map