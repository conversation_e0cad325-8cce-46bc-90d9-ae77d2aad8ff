export default GlobalListeners;
declare class GlobalListeners extends FRGatherer {
    /**
     * @param {LH.Crdp.DOMDebugger.EventListener} listener
     * @return {listener is {type: 'pagehide'|'unload'|'visibilitychange'} & LH.Crdp.DOMDebugger.EventListener}
     */
    static _filterForAllowlistedTypes(listener: LH.Crdp.DOMDebugger.EventListener): listener is {
        type: 'pagehide' | 'unload' | 'visibilitychange';
    } & import("devtools-protocol").Protocol.DOMDebugger.EventListener;
    /**
     * @param { LH.Artifacts.GlobalListener } listener
     * @return { string }
     */
    getListenerIndentifier(listener: LH.Artifacts.GlobalListener): string;
    /**
     * @param { LH.Artifacts['GlobalListeners'] } listeners
     * @return { LH.Artifacts['GlobalListeners'] }
     */
    dedupeListeners(listeners: LH.Artifacts['GlobalListeners']): LH.Artifacts['GlobalListeners'];
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['GlobalListeners']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['GlobalListeners']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=global-listeners.d.ts.map