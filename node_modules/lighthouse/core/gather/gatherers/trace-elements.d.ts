export default TraceElements;
export type TraceElementData = {
    nodeId: number;
    score?: number;
    animations?: {
        name?: string;
        failureReasonsMask?: number;
        unsupportedProperties?: string[];
    }[];
    type?: string;
};
declare class TraceElements extends FRGatherer {
    /**
     * @param {Array<number>} rect
     * @return {LH.Artifacts.Rect}
     */
    static traceRectToLHRect(rect: Array<number>): LH.Artifacts.Rect;
    /**
     * This function finds the top (up to 5) elements that contribute to the CLS score of the page.
     * Each layout shift event has a 'score' which is the amount added to the CLS as a result of the given shift(s).
     * We calculate the score per element by taking the 'score' of each layout shift event and
     * distributing it between all the nodes that were shifted, proportianal to the impact region of
     * each shifted element.
     * @param {Array<LH.TraceEvent>} mainThreadEvents
     * @return {Array<TraceElementData>}
     */
    static getTopLayoutShiftElements(mainThreadEvents: Array<LH.TraceEvent>): Array<TraceElementData>;
    /**
     * @param {LH.Trace} trace
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<TraceElementData|undefined>}
     */
    static getResponsivenessElement(trace: LH.Trace, context: LH.Gatherer.FRTransitionalContext): Promise<TraceElementData | undefined>;
    /**
     * @param {LH.Trace} trace
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<{nodeId: number, type: string} | undefined>}
     */
    static getLcpElement(trace: LH.Trace, context: LH.Gatherer.FRTransitionalContext): Promise<{
        nodeId: number;
        type: string;
    } | undefined>;
    /** @type {LH.Gatherer.GathererMeta<'Trace'>} */
    meta: LH.Gatherer.GathererMeta<'Trace'>;
    /** @type {Map<string, string>} */
    animationIdToName: Map<string, string>;
    /** @param {LH.Crdp.Animation.AnimationStartedEvent} args */
    _onAnimationStarted({ animation: { id, name } }: LH.Crdp.Animation.AnimationStartedEvent): void;
    /**
     * Find the node ids of elements which are animated using the Animation trace events.
     * @param {Array<LH.TraceEvent>} mainThreadEvents
     * @return {Promise<Array<TraceElementData>>}
     */
    getAnimatedElements(mainThreadEvents: Array<LH.TraceEvent>): Promise<Array<TraceElementData>>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Trace|undefined} trace
     * @return {Promise<LH.Artifacts['TraceElements']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, trace: LH.Trace | undefined): Promise<LH.Artifacts['TraceElements']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'Trace'>} context
     * @return {Promise<LH.Artifacts.TraceElement[]>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'Trace'>): Promise<LH.Artifacts.TraceElement[]>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts.TraceElement[]>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts.TraceElement[]>;
}
import FRGatherer from "../base-gatherer.js";
import Trace from "./trace.js";
//# sourceMappingURL=trace-elements.d.ts.map