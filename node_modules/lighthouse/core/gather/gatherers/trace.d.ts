export default Trace;
declare class Trace extends FRGatherer {
    static getDefaultTraceCategories(): string[];
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @return {Promise<LH.Trace>}
     */
    static endTraceAndCollectEvents(session: LH.Gatherer.FRProtocolSession): Promise<LH.Trace>;
    static symbol: symbol;
    /** @type {LH.Trace} */
    _trace: LH.Trace;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     */
    startSensitiveInstrumentation({ driver, gatherMode, settings }: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     */
    stopSensitiveInstrumentation({ driver }: LH.Gatherer.FRTransitionalContext): Promise<void>;
    getArtifact(): import("../../index.js").Trace;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=trace.d.ts.map