export default ImageElements;
declare class ImageElements extends FRGatherer {
    /** @type {Map<string, {naturalWidth: number, naturalHeight: number}>} */
    _naturalSizeCache: Map<string, {
        naturalWidth: number;
        naturalHeight: number;
    }>;
    /**
     * @param {LH.Gatherer.FRTransitionalDriver} driver
     * @param {LH.Artifacts.ImageElement} element
     */
    fetchElementWithSizeInformation(driver: LH.Gatherer.FRTransitionalDriver, element: LH.Artifacts.ImageElement): Promise<void>;
    /**
     * Images might be sized via CSS. In order to compute unsized-images failures, we need to collect
     * matched CSS rules to see if this is the case.
     * @url http://go/dwoqq (googlers only)
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} devtoolsNodePath
     * @param {LH.Artifacts.ImageElement} element
     */
    fetchSourceRules(session: LH.Gatherer.FRProtocolSession, devtoolsNodePath: string, element: LH.Artifacts.ImageElement): Promise<void>;
    /**
     *
     * @param {LH.Gatherer.FRTransitionalDriver} driver
     * @param {LH.Artifacts.ImageElement[]} elements
     */
    collectExtraDetails(driver: LH.Gatherer.FRTransitionalDriver, elements: LH.Artifacts.ImageElement[]): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['ImageElements']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['ImageElements']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=image-elements.d.ts.map