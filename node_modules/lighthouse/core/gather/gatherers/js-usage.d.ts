export default JsUsage;
/**
 * @fileoverview Tracks unused JavaScript
 */
declare class JsUsage extends FRGatherer {
    /** @type {LH.Crdp.Profiler.ScriptCoverage[]} */
    _scriptUsages: LH.Crdp.Profiler.ScriptCoverage[];
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @return {Promise<LH.Artifacts['JsUsage']>}
     */
    getArtifact(): Promise<LH.Artifacts['JsUsage']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=js-usage.d.ts.map