export default NetworkUserAgent;
/** @implements {LH.Gatherer.FRGathererInstance<'DevtoolsLog'>} */
declare class NetworkUserAgent extends FRGatherer implements LH.Gatherer.FRGathererInstance<'DevtoolsLog'> {
    /**
     * @param {LH.Artifacts['DevtoolsLog']} devtoolsLog
     * @return {string}
     */
    static getNetworkUserAgent(devtoolsLog: LH.Artifacts['DevtoolsLog']): string;
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['NetworkUserAgent']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['NetworkUserAgent']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=network-user-agent.d.ts.map