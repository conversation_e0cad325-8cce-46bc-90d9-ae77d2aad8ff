export default EmbeddedContent;
declare class EmbeddedContent extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['EmbeddedContent']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['EmbeddedContent']>;
}
import FRGatherer from "../../base-gatherer.js";
//# sourceMappingURL=embedded-content.d.ts.map