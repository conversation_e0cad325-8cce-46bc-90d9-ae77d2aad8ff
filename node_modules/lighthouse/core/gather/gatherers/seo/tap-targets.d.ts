export default TapTargets;
declare class TapTargets extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} className
     * @return {Promise<string>}
     */
    addStyleRule(session: LH.Gatherer.FRProtocolSession, className: string): Promise<string>;
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     * @param {string} styleSheetId
     */
    removeStyleRule(session: LH.Gatherer.FRProtocolSession, styleSheetId: string): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts.TapTarget[]>} All visible tap targets with their positions and sizes
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts.TapTarget[]>;
}
import FRGatherer from "../../base-gatherer.js";
//# sourceMappingURL=tap-targets.d.ts.map