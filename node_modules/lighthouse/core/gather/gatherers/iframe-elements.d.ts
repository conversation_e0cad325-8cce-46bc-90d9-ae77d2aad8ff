export default IFrameElements;
declare class IFrameElements extends FRGatherer {
    /**
     * @param {LH.Gatherer.FRTransitionalContext} passContext
     * @return {Promise<LH.Artifacts['IFrameElements']>}
     * @override
     */
    override getArtifact(passContext: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['IFrameElements']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=iframe-elements.d.ts.map