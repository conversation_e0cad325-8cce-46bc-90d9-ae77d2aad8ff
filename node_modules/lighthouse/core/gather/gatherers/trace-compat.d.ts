export default TraceCompat;
/** @implements {LH.Gatherer.FRGathererInstance<'Trace'>} */
declare class TraceCompat extends FRGatherer implements LH.Gatherer.FRGathererInstance<'Trace'> {
    /** @type {LH.Gatherer.GathererMeta<'Trace'>} */
    meta: LH.Gatherer.GathererMeta<'Trace'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'Trace'>} passContext
     * @return {Promise<LH.Artifacts['traces']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext<'Trace'>): Promise<LH.Artifacts['traces']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=trace-compat.d.ts.map