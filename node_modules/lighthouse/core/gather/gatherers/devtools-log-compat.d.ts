export default DevtoolsLogCompat;
/** @implements {LH.Gatherer.FRGathererInstance<'DevtoolsLog'>} */
declare class DevtoolsLogCompat extends FRGatherer implements LH.Gatherer.FRGathererInstance<'DevtoolsLog'> {
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} passContext
     * @return {Promise<LH.Artifacts['devtoolsLogs']>}
     */
    getArtifact(passContext: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['devtoolsLogs']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=devtools-log-compat.d.ts.map