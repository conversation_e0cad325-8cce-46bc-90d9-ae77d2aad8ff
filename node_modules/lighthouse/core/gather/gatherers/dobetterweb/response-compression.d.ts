export default ResponseCompression;
declare class ResponseCompression extends FRGatherer {
    /**
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @return {LH.Artifacts['ResponseCompression']}
     */
    static filterUnoptimizedResponses(networkRecords: LH.Artifacts.NetworkRequest[]): LH.Artifacts['ResponseCompression'];
    /** @type {LH.Gatherer.GathererMeta<'DevtoolsLog'>} */
    meta: LH.Gatherer.GathererMeta<'DevtoolsLog'>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @param {LH.Artifacts.NetworkRequest[]} networkRecords
     * @return {Promise<LH.Artifacts['ResponseCompression']>}
     */
    _getArtifact(context: LH.Gatherer.FRTransitionalContext, networkRecords: LH.Artifacts.NetworkRequest[]): Promise<LH.Artifacts['ResponseCompression']>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>} context
     * @return {Promise<LH.Artifacts['ResponseCompression']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext<'DevtoolsLog'>): Promise<LH.Artifacts['ResponseCompression']>;
    /**
     * @param {LH.Gatherer.PassContext} passContext
     * @param {LH.Gatherer.LoadData} loadData
     * @return {Promise<LH.Artifacts['ResponseCompression']>}
     */
    afterPass(passContext: LH.Gatherer.PassContext, loadData: LH.Gatherer.LoadData): Promise<LH.Artifacts['ResponseCompression']>;
}
import FRGatherer from "../../base-gatherer.js";
import { NetworkRequest } from "../../../lib/network-request.js";
//# sourceMappingURL=response-compression.d.ts.map