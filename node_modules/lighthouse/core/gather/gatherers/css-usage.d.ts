export default CSSUsage;
declare class CSSUsage extends FRGatherer {
    /** @type {LH.Gatherer.FRProtocolSession|undefined} */
    _session: LH.Gatherer.FRProtocolSession | undefined;
    /** @type {Map<string, Promise<LH.Artifacts.CSSStyleSheetInfo|Error>>} */
    _sheetPromises: Map<string, Promise<LH.Artifacts.CSSStyleSheetInfo | Error>>;
    /**
     * Initialize as undefined so we can assert results are fetched.
     * @type {LH.Crdp.CSS.RuleUsage[]|undefined}
     */
    _ruleUsage: LH.Crdp.CSS.RuleUsage[] | undefined;
    /**
     * @param {LH.Crdp.CSS.StyleSheetAddedEvent} event
     */
    _onStylesheetAdded(event: LH.Crdp.CSS.StyleSheetAddedEvent): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startCSSUsageTracking(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopCSSUsageTracking(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    startInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     */
    stopInstrumentation(context: LH.Gatherer.FRTransitionalContext): Promise<void>;
    /**
     * @param {LH.Gatherer.FRTransitionalContext} context
     * @return {Promise<LH.Artifacts['CSSUsage']>}
     */
    getArtifact(context: LH.Gatherer.FRTransitionalContext): Promise<LH.Artifacts['CSSUsage']>;
}
import FRGatherer from "../base-gatherer.js";
//# sourceMappingURL=css-usage.d.ts.map