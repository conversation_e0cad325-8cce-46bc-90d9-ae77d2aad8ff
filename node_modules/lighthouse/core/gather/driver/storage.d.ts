/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {string} url
 * @return {Promise<LH.IcuMessage[]>}
 */
export function clearDataForOrigin(session: LH.Gatherer.FRProtocolSession, url: string): Promise<LH.IcuMessage[]>;
/**
 * Clear the network cache on disk and in memory.
 * @param {LH.Gatherer.FRProtocolSession} session
 * @return {Promise<LH.IcuMessage[]>}
 */
export function clearBrowserCaches(session: LH.Gatherer.FRProtocolSession): Promise<LH.IcuMessage[]>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {string} url
 * @return {Promise<LH.IcuMessage | undefined>}
 */
export function getImportantStorageWarning(session: LH.Gatherer.FRProtocolSession, url: string): Promise<LH.IcuMessage | undefined>;
export namespace UIStrings {
    const warningData: string;
    const warningCacheTimeout: string;
    const warningOriginDataTimeout: string;
}
//# sourceMappingURL=storage.d.ts.map