export type TargetWithSession = {
    target: LH.Crdp.Target.TargetInfo;
    cdpSession: LH.Puppeteer.CDPSession;
    session: LH.Gatherer.FRProtocolSession;
    protocolListener: (event: unknown) => void;
};
export type ProtocolEventMap = {
    'protocolevent': [LH.Protocol.RawEventMessage];
};
export type ProtocolEventMessageEmitter = LH.Protocol.StrictEventEmitterClass<ProtocolEventMap>;
declare const TargetManager_base: ProtocolEventMessageEmitter;
/**
 * Tracks targets (the page itself, its iframes, their iframes, etc) as they
 * appear and allows listeners to the flattened protocol events from all targets.
 */
export class TargetManager extends TargetManager_base {
    /** @param {LH.Puppeteer.CDPSession} cdpSession */
    constructor(cdpSession: LH.Puppeteer.CDPSession);
    _enabled: boolean;
    _rootCdpSession: import("../../../types/puppeteer.js").default.CDPSession;
    /**
     * A map of target id to target/session information. Used to ensure unique
     * attached targets.
     * @type {Map<string, TargetWithSession>}
     */
    _targetIdToTargets: Map<string, TargetWithSession>;
    /**
     * @param {LH.Puppeteer.CDPSession} cdpSession
     */
    _onSessionAttached(cdpSession: LH.Puppeteer.CDPSession): Promise<void>;
    /**
     * @param {LH.Crdp.Page.FrameNavigatedEvent} frameNavigatedEvent
     */
    _onFrameNavigated(frameNavigatedEvent: LH.Crdp.Page.FrameNavigatedEvent): Promise<void>;
    /**
     * @param {string} sessionId
     * @return {LH.Gatherer.FRProtocolSession}
     */
    _findSession(sessionId: string): LH.Gatherer.FRProtocolSession;
    /**
     * Returns the root session.
     * @return {LH.Gatherer.FRProtocolSession}
     */
    rootSession(): LH.Gatherer.FRProtocolSession;
    /**
     * Returns a listener for all protocol events from session, and augments the
     * event with the sessionId.
     * @param {string} sessionId
     */
    _getProtocolEventListener(sessionId: string): <EventName extends keyof import("puppeteer-core").ProtocolMapping.Events>(method: EventName, params: import("../../../types/protocol.js").default.RawEventMessageRecord[EventName]["params"]) => void;
    /**
     * @return {Promise<void>}
     */
    enable(): Promise<void>;
    /**
     * @return {Promise<void>}
     */
    disable(): Promise<void>;
}
export {};
//# sourceMappingURL=target-manager.d.ts.map