/**
 * Resolves a backend node ID (from a trace event, protocol, etc) to the object ID for use with
 * `Runtime.callFunctionOn`. `undefined` means the node could not be found.
 *
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {number} backendNodeId
 * @return {Promise<string|undefined>}
 */
export function resolveNodeIdToObjectId(session: LH.Gatherer.FRProtocolSession, backendNodeId: number): Promise<string | undefined>;
/**
 * Resolves a proprietary devtools node path (created from page-function.js) to the object ID for use
 * with `Runtime.callFunctionOn`. `undefined` means the node could not be found.
 * Requires `DOM.getDocument` to have been called since the object's creation or it will always be `undefined`.
 *
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {string} path
 * @return {Promise<string|undefined>}
 */
export function resolveDevtoolsNodePathToObjectId(session: LH.Gatherer.FRProtocolSession, path: string): Promise<string | undefined>;
//# sourceMappingURL=dom.d.ts.map