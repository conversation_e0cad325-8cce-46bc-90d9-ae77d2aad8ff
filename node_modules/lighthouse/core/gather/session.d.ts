export type CrdpEventMessageEmitter = LH.Protocol.StrictEventEmitterClass<LH.CrdpEvents>;
declare const ProtocolSession_base: CrdpEventMessageEmitter;
/** @implements {LH.Gatherer.FRProtocolSession} */
export class ProtocolSession extends ProtocolSession_base implements LH.Gatherer.FRProtocolSession {
    /**
     * @param {LH.Puppeteer.CDPSession} cdpSession
     */
    constructor(cdpSession: LH.Puppeteer.CDPSession);
    _cdpSession: import("../../types/puppeteer.js").default.CDPSession;
    /** @type {LH.Crdp.Target.TargetInfo|undefined} */
    _targetInfo: LH.Crdp.Target.TargetInfo | undefined;
    /** @type {number|undefined} */
    _nextProtocolTimeout: number | undefined;
    /**
     * Re-emit protocol events from the underlying CDPSession.
     * @template {keyof LH.CrdpEvents} E
     * @param {E} method
     * @param {LH.CrdpEvents[E]} params
     */
    _handleProtocolEvent<E extends keyof import("puppeteer-core").ProtocolMapping.Events>(method: E, ...params: import("puppeteer-core").ProtocolMapping.Events[E]): void;
    id(): string;
    /** @param {LH.Crdp.Target.TargetInfo} targetInfo */
    setTargetInfo(targetInfo: LH.Crdp.Target.TargetInfo): void;
    /**
     * @return {boolean}
     */
    hasNextProtocolTimeout(): boolean;
    /**
     * @return {number}
     */
    getNextProtocolTimeout(): number;
    /**
     * @param {number} ms
     */
    setNextProtocolTimeout(ms: number): void;
    /**
     * @template {keyof LH.CrdpCommands} C
     * @param {C} method
     * @param {LH.CrdpCommands[C]['paramsType']} params
     * @return {Promise<LH.CrdpCommands[C]['returnType']>}
     */
    sendCommand<C extends keyof import("puppeteer-core").ProtocolMapping.Commands>(method: C, ...params: import("puppeteer-core").ProtocolMapping.Commands[C]["paramsType"]): Promise<import("puppeteer-core").ProtocolMapping.Commands[C]["returnType"]>;
    /**
     * Disposes of a session so that it can no longer talk to Chrome.
     * @return {Promise<void>}
     */
    dispose(): Promise<void>;
}
export {};
//# sourceMappingURL=session.d.ts.map