export type CollectPhaseArtifactOptions = {
    driver: import('./driver.js').Driver;
    page: LH.Puppeteer.Page;
    artifactDefinitions: Array<LH.Config.AnyArtifactDefn>;
    artifactState: ArtifactState;
    baseArtifacts: LH.FRBaseArtifacts;
    phase: LH.Gatherer.FRGatherPhase;
    gatherMode: LH.Gatherer.GatherMode;
    computedCache: Map<string, LH.ArbitraryEqualityMap>;
    settings: LH.Config.Settings;
};
export type IntermediateArtifacts = Record<string, Promise<any>>;
export type ArtifactState = Record<CollectPhaseArtifactOptions['phase'], IntermediateArtifacts>;
export type Dependencies = LH.Gatherer.FRTransitionalContext<LH.Gatherer.DependencyKey>['dependencies'];
/** @return {ArtifactState} */
export function getEmptyArtifactState(): ArtifactState;
/**
 * Awaits the result of artifact, catching errors to set the artifact to an error instead.
 *
 * @param {ArtifactState} artifactState
 * @return {Promise<Partial<LH.GathererArtifacts>>}
 */
export function awaitArtifacts(artifactState: ArtifactState): Promise<Partial<LH.GathererArtifacts>>;
/**
 * Runs the gatherer methods for a particular navigation phase (startInstrumentation/getArtifact/etc).
 * All gatherer method return values are stored on the artifact state object, organized by phase.
 * This method collects required dependencies, runs the applicable gatherer methods, and saves the
 * result on the artifact state object that was passed as part of `options`.
 *
 * @param {CollectPhaseArtifactOptions} options
 */
export function collectPhaseArtifacts(options: CollectPhaseArtifactOptions): Promise<void>;
/**
 * @param {LH.Config.AnyArtifactDefn} artifact
 * @param {Record<string, LH.Gatherer.PhaseResult>} artifactsById
 * @return {Promise<Dependencies>}
 */
export function collectArtifactDependencies(artifact: LH.Config.AnyArtifactDefn, artifactsById: Record<string, LH.Gatherer.PhaseResult>): Promise<Dependencies>;
//# sourceMappingURL=runner-helpers.d.ts.map