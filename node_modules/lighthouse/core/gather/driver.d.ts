/** @implements {LH.Gatherer.FRTransitionalDriver} */
export class Driver implements LH.Gatherer.FRTransitionalDriver {
    /**
     * @param {LH.Puppeteer.Page} page
     */
    constructor(page: LH.Puppeteer.Page);
    _page: import("../../types/puppeteer.js").default.Page;
    /** @type {TargetManager|undefined} */
    _targetManager: TargetManager | undefined;
    /** @type {ExecutionContext|undefined} */
    _executionContext: ExecutionContext | undefined;
    /** @type {Fetcher|undefined} */
    _fetcher: Fetcher | undefined;
    defaultSession: import("../../types/gatherer.js").default.FRProtocolSession;
    /** @return {LH.Gatherer.FRTransitionalDriver['executionContext']} */
    get executionContext(): ExecutionContext;
    /** @return {Fetcher} */
    get fetcher(): Fetcher;
    get targetManager(): any;
    /** @return {Promise<string>} */
    url(): Promise<string>;
    /** @return {Promise<void>} */
    connect(): Promise<void>;
    /** @return {Promise<void>} */
    disconnect(): Promise<void>;
}
import { TargetManager } from "./driver/target-manager.js";
import { ExecutionContext } from "./driver/execution-context.js";
import { Fetcher } from "./fetcher.js";
//# sourceMappingURL=driver.d.ts.map