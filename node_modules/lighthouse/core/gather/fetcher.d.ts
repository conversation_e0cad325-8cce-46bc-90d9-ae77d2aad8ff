export type FetchResponse = {
    content: string | null;
    status: number | null;
};
/**
 * @fileoverview Fetcher is a utility for making requests to any arbitrary resource,
 * ignoring normal browser constraints such as CORS.
 */
/** @typedef {{content: string|null, status: number|null}} FetchResponse */
export class Fetcher {
    /**
     * @param {LH.Gatherer.FRProtocolSession} session
     */
    constructor(session: LH.Gatherer.FRProtocolSession);
    session: LH.Gatherer.FRProtocolSession;
    /**
     * Fetches any resource using the network directly.
     *
     * @param {string} url
     * @param {{timeout: number}=} options timeout is in ms
     * @return {Promise<FetchResponse>}
     */
    fetchResource(url: string, options?: {
        timeout: number;
    } | undefined): Promise<FetchResponse>;
    /**
     * @param {string} url
     * @return {Promise<FetchResponse>}
     */
    _fetchWithFetchApi(url: string): Promise<FetchResponse>;
    /**
     * @param {string} handle
     * @param {{timeout: number}=} options,
     * @return {Promise<string>}
     */
    _readIOStream(handle: string, options?: {
        timeout: number;
    } | undefined): Promise<string>;
    /**
     * @param {string} url
     * @return {Promise<{stream: LH.Crdp.IO.StreamHandle|null, status: number|null}>}
     */
    _loadNetworkResource(url: string): Promise<{
        stream: LH.Crdp.IO.StreamHandle | null;
        status: number | null;
    }>;
    /**
     * @param {string} url
     * @param {{timeout: number}} options timeout is in ms
     * @return {Promise<FetchResponse>}
     */
    _fetchResourceOverProtocol(url: string, options: {
        timeout: number;
    }): Promise<FetchResponse>;
    /**
     * @template T
     * @param {Promise<T>} promise
     * @param {number} ms
     */
    _wrapWithTimeout<T>(promise: Promise<T>, ms: number): Promise<T>;
}
import * as LH from "../../types/lh.js";
//# sourceMappingURL=fetcher.d.ts.map