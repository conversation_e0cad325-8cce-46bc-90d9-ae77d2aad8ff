/**
 * @param {LH.Config.ResolvedConfig} resolvedConfig
 * @param {LH.Gatherer.FRTransitionalDriver} driver
 * @param {{gatherMode: LH.Gatherer.GatherMode}} context
 * @return {Promise<LH.BaseArtifacts>}
 */
export function getBaseArtifacts(resolvedConfig: LH.Config.ResolvedConfig, driver: L<PERSON><PERSON>Gatherer.FRTransitionalDriver, context: {
    gatherMode: LH.Gatherer.GatherMode;
}): Promise<LH.BaseArtifacts>;
/**
 * @param {LH.FRBaseArtifacts} baseArtifacts
 * @param {Partial<LH.Artifacts>} gathererArtifacts
 * @return {LH.Artifacts}
 */
export function finalizeArtifacts(baseArtifacts: LH.FRBaseArtifacts, gathererArtifacts: Partial<LH.Artifacts>): LH.Artifacts;
//# sourceMappingURL=base-artifacts.d.ts.map