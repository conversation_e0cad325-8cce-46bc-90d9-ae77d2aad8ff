export type NavigationContext = {
    driver: Driver;
    page: LH.Puppeteer.Page;
    resolvedConfig: LH.Config.ResolvedConfig;
    navigation: LH.Config.NavigationDefn;
    requestor: LH.NavigationRequestor;
    baseArtifacts: LH.FRBaseArtifacts;
    computedCache: Map<string, LH.ArbitraryEqualityMap>;
};
export type PhaseState = Omit<Parameters<typeof collectPhaseArtifacts>[0], 'phase'>;
/**
 * @param {LH.Puppeteer.Page|undefined} page
 * @param {LH.NavigationRequestor|undefined} requestor
 * @param {{config?: LH.Config, flags?: LH.Flags}} [options]
 * @return {Promise<LH.Gatherer.FRGatherResult>}
 */
export function navigationGather(page: LH.Puppeteer.Page | undefined, requestor: LH.NavigationRequestor | undefined, options?: {
    config?: import("../../types/config.js").default | undefined;
    flags?: import("../index.js").Flags | undefined;
} | undefined): Promise<LH.Gatherer.FRGatherResult>;
/**
 * @param {{driver: Driver, resolvedConfig: LH.Config.ResolvedConfig, requestor: LH.NavigationRequestor}} args
 * @return {Promise<{baseArtifacts: LH.FRBaseArtifacts}>}
 */
export function _setup({ driver, resolvedConfig, requestor }: {
    driver: Driver;
    resolvedConfig: LH.Config.ResolvedConfig;
    requestor: LH.NavigationRequestor;
}): Promise<{
    baseArtifacts: LH.FRBaseArtifacts;
}>;
/**
 * @param {NavigationContext} navigationContext
 * @return {Promise<{warnings: Array<LH.IcuMessage>}>}
 */
export function _setupNavigation({ requestor, driver, navigation, resolvedConfig }: NavigationContext): Promise<{
    warnings: Array<LH.IcuMessage>;
}>;
/**
 * @param {NavigationContext} navigationContext
 * @return {Promise<{requestedUrl: string, mainDocumentUrl: string, navigationError: LH.LighthouseError | undefined, warnings: Array<LH.IcuMessage>}>}
 */
export function _navigate(navigationContext: NavigationContext): Promise<{
    requestedUrl: string;
    mainDocumentUrl: string;
    navigationError: LH.LighthouseError | undefined;
    warnings: Array<LH.IcuMessage>;
}>;
/**
 * @param {NavigationContext} navigationContext
 * @return {ReturnType<typeof _computeNavigationResult>}
 */
export function _navigation(navigationContext: NavigationContext): ReturnType<typeof _computeNavigationResult>;
/**
 * @param {{driver: Driver, page: LH.Puppeteer.Page, resolvedConfig: LH.Config.ResolvedConfig, requestor: LH.NavigationRequestor; baseArtifacts: LH.FRBaseArtifacts, computedCache: NavigationContext['computedCache']}} args
 * @return {Promise<{artifacts: Partial<LH.FRArtifacts & LH.FRBaseArtifacts>}>}
 */
export function _navigations(args: {
    driver: Driver;
    page: LH.Puppeteer.Page;
    resolvedConfig: LH.Config.ResolvedConfig;
    requestor: LH.NavigationRequestor;
    baseArtifacts: LH.FRBaseArtifacts;
    computedCache: NavigationContext['computedCache'];
}): Promise<{
    artifacts: Partial<LH.FRArtifacts & LH.FRBaseArtifacts>;
}>;
/**
 * @param {{requestedUrl?: string, driver: Driver, resolvedConfig: LH.Config.ResolvedConfig, lhBrowser?: LH.Puppeteer.Browser, lhPage?: LH.Puppeteer.Page}} args
 */
export function _cleanup({ requestedUrl, driver, resolvedConfig, lhBrowser, lhPage }: {
    requestedUrl?: string;
    driver: Driver;
    resolvedConfig: LH.Config.ResolvedConfig;
    lhBrowser?: LH.Puppeteer.Browser;
    lhPage?: LH.Puppeteer.Page;
}): Promise<void>;
import { Driver } from "./driver.js";
import { collectPhaseArtifacts } from "./runner-helpers.js";
import { LighthouseError } from "../lib/lh-error.js";
/**
 * @param {NavigationContext} navigationContext
 * @param {PhaseState} phaseState
 * @param {Awaited<ReturnType<typeof _setupNavigation>>} setupResult
 * @param {Awaited<ReturnType<typeof _navigate>>} navigateResult
 * @return {Promise<{artifacts: Partial<LH.GathererArtifacts>, warnings: Array<LH.IcuMessage>, pageLoadError: LH.LighthouseError | undefined}>}
 */
declare function _computeNavigationResult(navigationContext: NavigationContext, phaseState: PhaseState, setupResult: Awaited<ReturnType<typeof _setupNavigation>>, navigateResult: Awaited<ReturnType<typeof _navigate>>): Promise<{
    artifacts: Partial<LH.GathererArtifacts>;
    warnings: Array<LH.IcuMessage>;
    pageLoadError: LH.LighthouseError | undefined;
}>;
export {};
//# sourceMappingURL=navigation-runner.d.ts.map