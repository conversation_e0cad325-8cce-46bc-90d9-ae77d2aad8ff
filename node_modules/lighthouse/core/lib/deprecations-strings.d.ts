/**
 * @param {LH.Crdp.Audits.DeprecationIssueDetails} issueDetails
 */
declare function getDescription(issueDetails: LH.Crdp.Audits.DeprecationIssueDetails): {
    file: string;
    substitutions: Map<string, import("../index.js").IcuMessage>;
    links: {
        link: string;
        linkTitle: import("../index.js").IcuMessage;
    }[];
    message: import("../index.js").IcuMessage;
};
export namespace UIStrings {
    const feature: string;
    const milestone: string;
    const title: string;
    const authorizationCoveredByWildcard: string;
    const canRequestURLHTTPContainingNewline: string;
    const chromeLoadTimesConnectionInfo: string;
    const chromeLoadTimesFirstPaintAfterLoadTime: string;
    const chromeLoadTimesWasAlternateProtocolAvailable: string;
    const cookieWithTruncatingChar: string;
    const crossOriginAccessBasedOnDocumentDomain: string;
    const crossOriginWindowApi: string;
    const cssSelectorInternalMediaControlsOverlayCastButton: string;
    const deprecationExample: string;
    const documentDomainSettingWithoutOriginAgentClusterHeader: string;
    const eventPath: string;
    const expectCTHeader: string;
    const geolocationInsecureOrigin: string;
    const geolocationInsecureOriginDeprecatedNotRemoved: string;
    const getUserMediaInsecureOrigin: string;
    const hostCandidateAttributeGetter: string;
    const identityInCanMakePaymentEvent: string;
    const insecurePrivateNetworkSubresourceRequest: string;
    const localCSSFileExtensionRejected: string;
    const mediaSourceAbortRemove: string;
    const mediaSourceDurationTruncatingBuffered: string;
    const noSysexWebMIDIWithoutPermission: string;
    const notificationInsecureOrigin: string;
    const notificationPermissionRequestedIframe: string;
    const obsoleteWebRtcCipherSuite: string;
    const openWebDatabaseInsecureContext: string;
    const paymentInstruments: string;
    const paymentRequestCSPViolation: string;
    const persistentQuotaType: string;
    const pictureSourceSrc: string;
    const vendorSpecificApi: string;
    const prefixedStorageInfo: string;
    const deprecatedWithReplacement: string;
    const requestedSubresourceWithEmbeddedCredentials: string;
    const rtcConstraintEnableDtlsSrtpFalse: string;
    const rtcConstraintEnableDtlsSrtpTrue: string;
    const rtcPeerConnectionComplexPlanBSdpUsingDefaultSdpSemantics: string;
    const rtcPeerConnectionSdpSemanticsPlanB: string;
    const rtcpMuxPolicyNegotiate: string;
    const sharedArrayBufferConstructedWithoutIsolation: string;
    const textToSpeech_DisallowedByAutoplay: string;
    const v8SharedArrayBufferConstructedInExtensionWithoutIsolation: string;
    const xhrJSONEncodingDetection: string;
    const xmlHttpRequestSynchronousInNonWorkerOutsideBeforeUnload: string;
    const xrSupportsSession: string;
    const overflowVisibleOnReplacedElement: string;
}
export { getDescription as getIssueDetailDescription };
//# sourceMappingURL=deprecations-strings.d.ts.map