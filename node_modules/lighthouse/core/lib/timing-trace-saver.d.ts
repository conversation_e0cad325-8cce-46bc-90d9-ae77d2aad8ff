/**
 * @license Copyright 2018 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * Generates a chromium trace file from user timing measures
 * `threadId` can be provided to separate a series of trace events into another thread, useful
 * if timings do not share the same timeOrigin, but should both be "left-aligned".
 * Adapted from https://github.com/tdresser/performance-observer-tracing
 * @param {LH.Artifacts.MeasureEntry[]} entries user timing entries
 * @param {number=} threadId
 */
export function generateTraceEvents(entries: LH.Artifacts.MeasureEntry[], threadId?: number | undefined): import("..").TraceEvent[];
/**
 * Writes a trace file to disk
 * @param {LH.Result} lhr
 * @return {string}
 */
export function createTraceString(lhr: LH.Result): string;
//# sourceMappingURL=timing-trace-saver.d.ts.map