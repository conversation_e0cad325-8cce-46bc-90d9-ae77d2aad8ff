export default UrlUtils;
export type NetworkRequest = import('./network-request.js').NetworkRequest;
declare class UrlUtils {
    /**
     * @param {string} url
     * @return {boolean}
     */
    static isValid(url: string): boolean;
    /**
     * @param {string} urlA
     * @param {string} urlB
     * @return {boolean}
     */
    static hostsMatch(urlA: string, urlB: string): boolean;
    /**
     * @param {string} urlA
     * @param {string} urlB
     * @return {boolean}
     */
    static originsMatch(urlA: string, urlB: string): boolean;
    /**
     * @param {string} url
     * @return {?string}
     */
    static getOrigin(url: string): string | null;
    /**
     * Check if rootDomains matches
     *
     * @param {string|URL} urlA
     * @param {string|URL} urlB
     */
    static rootDomainsMatch(urlA: string | URL, urlB: string | URL): boolean;
    /**
     * @param {string} url
     * @param {{numPathParts: number, preserveQuery: boolean, preserveHost: boolean}=} options
     * @return {string}
     */
    static getURLDisplayName(url: string, options?: {
        numPathParts: number;
        preserveQuery: boolean;
        preserveHost: boolean;
    } | undefined): string;
    /**
     * Limits data URIs to 100 characters, returns all other strings untouched.
     * @param {string} url
     * @return {string}
     */
    static elideDataURI(url: string): string;
    /**
     * Determine if url1 equals url2, ignoring URL fragments.
     * @param {string} url1
     * @param {string} url2
     * @return {boolean}
     */
    static equalWithExcludedFragments(url1: string, url2: string): boolean;
    /**
     * Determine if the url has a protocol that we're able to test
     * @param {string} url
     * @return {boolean}
     */
    static isProtocolAllowed(url: string): boolean;
    /**
     * Is the host localhost-enough to satisfy the "secure context" definition
     * https://github.com/GoogleChrome/lighthouse/pull/11766#discussion_r582340683
     * @param {string} hostname Either a `new URL(url).hostname` or a `networkRequest.parsedUrl.host`
     * @return {boolean}
     */
    static isLikeLocalhost(hostname: string): boolean;
    /**
     * @param {NetworkRequest['parsedURL']['scheme']} scheme
     * @return {boolean}
     */
    static isSecureScheme(scheme: NetworkRequest['parsedURL']['scheme']): boolean;
    /**
     * Use `NetworkRequest.isNonNetworkRequest(req)` if working with a request.
     * Note: the `protocol` field from CDP can be 'h2', 'http', (not 'https'!) or it'll be url's scheme.
     *   https://source.chromium.org/chromium/chromium/src/+/main:content/browser/devtools/protocol/network_handler.cc;l=598-611;drc=56d4a9a9deb30be73adcee8737c73bcb2a5ab64f
     * However, a `new URL(href).protocol` has a colon suffix.
     *   https://url.spec.whatwg.org/#dom-url-protocol
     * A URL's `scheme` is specced as the `protocol` sans-colon, but isn't exposed on a URL object.
     * This method can take all 3 of these string types as a parameter.
     * @param {NetworkRequest['protocol'] | URL['protocol']} protocol Either a networkRequest's `protocol` per CDP or a `new URL(href).protocol`
     * @return {boolean}
     */
    static isNonNetworkProtocol(protocol: NetworkRequest['protocol'] | URL['protocol']): boolean;
    /**
     * @param {string} src
     * @return {string|undefined}
     */
    static guessMimeType(src: string): string | undefined;
    /**
     * @param {string|undefined} url
     * @return {string}
     */
    static normalizeUrl(url: string | undefined): string;
}
declare namespace UrlUtils {
    const INVALID_URL_DEBUG_STRING: string;
}
//# sourceMappingURL=url-utils.d.ts.map