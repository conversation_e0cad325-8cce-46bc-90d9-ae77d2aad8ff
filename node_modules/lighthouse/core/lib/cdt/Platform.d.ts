/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * @param {any[]} array
 * @param {any} needle
 * @param {any} comparator
 */
declare function lowerBound(array: any[], needle: any, comparator: any, left: any, right: any): any;
/**
 * @param {any[]} array
 * @param {any} needle
 * @param {any} comparator
 */
declare function upperBound(array: any[], needle: any, comparator: any, left: any, right: any): any;
export namespace ArrayUtilities {
    export { lowerBound };
    export { upperBound };
}
export namespace DevToolsPath {
    const EmptyUrlString: string;
}
export {};
//# sourceMappingURL=Platform.d.ts.map