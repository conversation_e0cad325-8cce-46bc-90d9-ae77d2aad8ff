/**
 * @license Copyright 2016 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * @param {NonNullable<LH.Artifacts.Manifest['value']>} manifest
 * @return {boolean} Does the manifest have any icons?
 */
export function doExist(manifest: NonNullable<LH.Artifacts.Manifest['value']>): boolean;
/**
 * @param {number} sizeRequirement
 * @param {NonNullable<LH.Artifacts.Manifest['value']>} manifest
 * @return {Array<string>} Value of satisfactory sizes (eg. ['192x192', '256x256'])
 */
export function pngSizedAtLeast(sizeRequirement: number, manifest: NonNullable<LH.Artifacts.Manifest['value']>): Array<string>;
/**
 * @param {NonNullable<LH.Artifacts.Manifest['value']>} manifest
 * @return {boolean} Does the manifest icons value contain at least one icon with purpose including "maskable"
 */
export function containsMaskableIcon(manifest: NonNullable<LH.Artifacts.Manifest['value']>): boolean;
//# sourceMappingURL=icons.d.ts.map