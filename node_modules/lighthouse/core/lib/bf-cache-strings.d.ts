/** @type {Record<string, {name: LH.IcuMessage} | undefined>} */
export const NotRestoredReasonDescription: Record<string, {
    name: LH.IcuMessage;
} | undefined>;
export namespace UIStrings {
    const notMainFrame: string;
    const backForwardCacheDisabled: string;
    const relatedActiveContentsExist: string;
    const HTTPStatusNotOK: string;
    const schemeNotHTTPOrHTTPS: string;
    const loading: string;
    const wasGrantedMediaAccess: string;
    const HTTPMethodNotGET: string;
    const subframeIsNavigating: string;
    const timeout: string;
    const cacheLimit: string;
    const JavaScriptExecution: string;
    const rendererProcessKilled: string;
    const rendererProcessCrashed: string;
    const grantedMediaStreamAccess: string;
    const cacheFlushed: string;
    const serviceWorkerVersionActivation: string;
    const sessionRestored: string;
    const serviceWorkerPostMessage: string;
    const enteredBackForwardCacheBeforeServiceWorkerHostAdded: string;
    const serviceWorkerClaim: string;
    const haveInnerContents: string;
    const timeoutPuttingInCache: string;
    const backForwardCacheDisabledByLowMemory: string;
    const backForwardCacheDisabledByCommandLine: string;
    const networkRequestDatapipeDrainedAsBytesConsumer: string;
    const networkRequestRedirected: string;
    const networkRequestTimeout: string;
    const networkExceedsBufferLimit: string;
    const navigationCancelledWhileRestoring: string;
    const backForwardCacheDisabledForPrerender: string;
    const userAgentOverrideDiffers: string;
    const foregroundCacheLimit: string;
    const backForwardCacheDisabledForDelegate: string;
    const unloadHandlerExistsInMainFrame: string;
    const unloadHandlerExistsInSubFrame: string;
    const serviceWorkerUnregistration: string;
    const noResponseHead: string;
    const cacheControlNoStore: string;
    const ineligibleAPI: string;
    const internalError: string;
    const webSocket: string;
    const webTransport: string;
    const webRTC: string;
    const mainResourceHasCacheControlNoStore: string;
    const mainResourceHasCacheControlNoCache: string;
    const subresourceHasCacheControlNoStore: string;
    const subresourceHasCacheControlNoCache: string;
    const containsPlugins: string;
    const documentLoaded: string;
    const dedicatedWorkerOrWorklet: string;
    const outstandingNetworkRequestOthers: string;
    const outstandingIndexedDBTransaction: string;
    const requestedNotificationsPermission: string;
    const requestedMIDIPermission: string;
    const requestedAudioCapturePermission: string;
    const requestedVideoCapturePermission: string;
    const requestedBackForwardCacheBlockedSensors: string;
    const requestedBackgroundWorkPermission: string;
    const broadcastChannel: string;
    const indexedDBConnection: string;
    const webXR: string;
    const sharedWorker: string;
    const webLocks: string;
    const webHID: string;
    const webShare: string;
    const requestedStorageAccessGrant: string;
    const webNfc: string;
    const outstandingNetworkRequestFetch: string;
    const outstandingNetworkRequestXHR: string;
    const appBanner: string;
    const printing: string;
    const webDatabase: string;
    const pictureInPicture: string;
    const portal: string;
    const speechRecognizer: string;
    const idleManager: string;
    const paymentManager: string;
    const speechSynthesis: string;
    const keyboardLock: string;
    const webOTPService: string;
    const outstandingNetworkRequestDirectSocket: string;
    const injectedJavascript: string;
    const injectedStyleSheet: string;
    const contentSecurityHandler: string;
    const contentWebAuthenticationAPI: string;
    const contentFileChooser: string;
    const contentSerial: string;
    const contentFileSystemAccess: string;
    const contentMediaDevicesDispatcherHost: string;
    const contentWebBluetooth: string;
    const contentWebUSB: string;
    const contentMediaSession: string;
    const contentMediaSessionService: string;
    const contentMediaPlay: string;
    const contentScreenReader: string;
    const embedderPopupBlockerTabHelper: string;
    const embedderSafeBrowsingTriggeredPopupBlocker: string;
    const embedderSafeBrowsingThreatDetails: string;
    const embedderAppBannerManager: string;
    const embedderDomDistillerViewerSource: string;
    const embedderDomDistillerSelfDeletingRequestDelegate: string;
    const embedderOomInterventionTabHelper: string;
    const embedderOfflinePage: string;
    const embedderChromePasswordManagerClientBindCredentialManager: string;
    const embedderPermissionRequestManager: string;
    const embedderModalDialog: string;
    const embedderExtensions: string;
    const embedderExtensionMessaging: string;
    const embedderExtensionMessagingForOpenPort: string;
    const embedderExtensionSentMessageToCachedFrame: string;
    const errorDocument: string;
    const fencedFramesEmbedder: string;
    const keepaliveRequest: string;
}
//# sourceMappingURL=bf-cache-strings.d.ts.map