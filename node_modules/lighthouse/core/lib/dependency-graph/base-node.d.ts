/**
 * A union of all types derived from BaseNode, allowing type check discrimination
 * based on `node.type`. If a new node type is created, it should be added here.
 */
export type Node = import('./cpu-node.js').CPUNode | import('./network-node.js').NetworkNode;
/**
 * @license Copyright 2017 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * A union of all types derived from BaseNode, allowing type check discrimination
 * based on `node.type`. If a new node type is created, it should be added here.
 * @typedef {import('./cpu-node.js').CPUNode | import('./network-node.js').NetworkNode} Node
 */
/**
 * @fileoverview This class encapsulates logic for handling resources and tasks used to model the
 * execution dependency graph of the page. A node has a unique identifier and can depend on other
 * nodes/be depended on. The construction of the graph maintains some important invariants that are
 * inherent to the model:
 *
 *    1. The graph is a DAG, there are no cycles.
 *    2. There is always a root node upon which all other nodes eventually depend.
 *
 * This allows particular optimizations in this class so that we do no need to check for cycles as
 * these methods are called and we can always start traversal at the root node.
 */
export class BaseNode {
    /**
     * Returns whether the given node has a cycle in its dependent graph by performing a DFS.
     * @param {Node} node
     * @param {'dependents'|'dependencies'|'both'} [direction]
     * @return {boolean}
     */
    static hasCycle(node: Node, direction?: "dependents" | "dependencies" | "both" | undefined): boolean;
    /**
     * @param {string} id
     */
    constructor(id: string);
    _id: string;
    _isMainDocument: boolean;
    /** @type {Node[]} */
    _dependents: Node[];
    /** @type {Node[]} */
    _dependencies: Node[];
    /**
     * @return {string}
     */
    get id(): string;
    /**
     * @return {typeof BaseNode.TYPES[keyof typeof BaseNode.TYPES]}
     */
    get type(): "network" | "cpu";
    /**
     * In microseconds
     * @return {number}
     */
    get startTime(): number;
    /**
     * In microseconds
     * @return {number}
     */
    get endTime(): number;
    /**
     * @param {boolean} value
     */
    setIsMainDocument(value: boolean): void;
    /**
     * @return {boolean}
     */
    isMainDocument(): boolean;
    /**
     * @return {Node[]}
     */
    getDependents(): Node[];
    /**
     * @return {number}
     */
    getNumberOfDependents(): number;
    /**
     * @return {Node[]}
     */
    getDependencies(): Node[];
    /**
     * @return {number}
     */
    getNumberOfDependencies(): number;
    /**
     * @return {Node}
     */
    getRootNode(): Node;
    /**
     * @param {Node} node
     */
    addDependent(node: Node): void;
    /**
     * @param {Node} node
     */
    addDependency(node: Node): void;
    /**
     * @param {Node} node
     */
    removeDependent(node: Node): void;
    /**
     * @param {Node} node
     */
    removeDependency(node: Node): void;
    removeAllDependencies(): void;
    /**
     * Computes whether the given node is anywhere in the dependency graph of this node.
     * While this method can prevent cycles, it walks the graph and should be used sparingly.
     * Nodes are always considered dependent on themselves for the purposes of cycle detection.
     * @param {BaseNode} node
     * @return {boolean}
     */
    isDependentOn(node: BaseNode): boolean;
    /**
     * Clones the node's information without adding any dependencies/dependents.
     * @return {Node}
     */
    cloneWithoutRelationships(): Node;
    /**
     * Clones the entire graph connected to this node filtered by the optional predicate. If a node is
     * included by the predicate, all nodes along the paths between the node and the root will be included. If the
     * node this was called on is not included in the resulting filtered graph, the method will throw.
     * @param {function(Node):boolean} [predicate]
     * @return {Node}
     */
    cloneWithRelationships(predicate?: ((arg0: Node) => boolean) | undefined): Node;
    /**
     * Traverses all connected nodes in BFS order, calling `callback` exactly once
     * on each. `traversalPath` is the shortest (though not necessarily unique)
     * path from `node` to the root of the iteration.
     *
     * The `getNextNodes` function takes a visited node and returns which nodes to
     * visit next. It defaults to returning the node's dependents.
     * @param {(node: Node, traversalPath: Node[]) => void} callback
     * @param {function(Node): Node[]} [getNextNodes]
     */
    traverse(callback: (node: Node, traversalPath: Node[]) => void, getNextNodes?: ((arg0: Node) => Node[]) | undefined): void;
    /**
     * @see BaseNode.traverse
     * @param {function(Node): Node[]} [getNextNodes]
     */
    traverseGenerator(getNextNodes?: ((arg0: Node) => Node[]) | undefined): Generator<{
        node: Node;
        traversalPath: Node[];
    }, void, unknown>;
    /**
     * @param {Node} node
     * @return {boolean}
     */
    canDependOn(node: Node): boolean;
}
export namespace BaseNode {
    namespace TYPES {
        const NETWORK: 'network';
        const CPU: 'cpu';
    }
}
//# sourceMappingURL=base-node.d.ts.map