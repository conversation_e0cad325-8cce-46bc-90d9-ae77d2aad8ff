export class CPUNode extends BaseNode {
    /**
     * @param {LH.TraceEvent} parentEvent
     * @param {LH.TraceEvent[]=} childEvents
     */
    constructor(parentEvent: LH.TraceEvent, childEvents?: LH.TraceEvent[] | undefined);
    _event: LH.TraceEvent;
    _childEvents: LH.TraceEvent[];
    get type(): "cpu";
    /**
     * @return {LH.TraceEvent}
     */
    get event(): LH.TraceEvent;
    /**
     * @return {LH.TraceEvent[]}
     */
    get childEvents(): LH.TraceEvent[];
    /**
     * Returns true if this node contains a Layout task.
     * @return {boolean}
     */
    didPerformLayout(): boolean;
    /**
     * Returns the script URLs that had their EvaluateScript events occur in this task.
     */
    getEvaluateScriptURLs(): Set<string>;
    /**
     * @return {CPUNode}
     */
    cloneWithoutRelationships(): CPUNode;
}
import { BaseNode } from "./base-node.js";
import * as LH from "../../../types/lh.js";
//# sourceMappingURL=cpu-node.d.ts.map