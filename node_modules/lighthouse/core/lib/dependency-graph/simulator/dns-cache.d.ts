export class DNSCache {
    /**
     * @param {{rtt: number}} options
     */
    constructor({ rtt }: {
        rtt: number;
    });
    _rtt: number;
    /** @type {Map<string, {resolvedAt: number}>} */
    _resolvedDomainNames: Map<string, {
        resolvedAt: number;
    }>;
    /**
     * @param {LH.Artifacts.NetworkRequest} request
     * @param {{requestedAt: number, shouldUpdateCache: boolean}=} options
     * @return {number}
     */
    getTimeUntilResolution(request: LH.Artifacts.NetworkRequest, options?: {
        requestedAt: number;
        shouldUpdateCache: boolean;
    } | undefined): number;
    /**
     * @param {LH.Artifacts.NetworkRequest} request
     * @param {number} resolvedAt
     */
    _updateCacheResolvedAtIfNeeded(request: LH.Artifacts.NetworkRequest, resolvedAt: number): void;
    /**
     * Forcefully sets the DNS resolution time for a record.
     * Useful for testing and alternate execution simulations.
     *
     * @param {string} domain
     * @param {number} resolvedAt
     */
    setResolvedAt(domain: string, resolvedAt: number): void;
}
export namespace DNSCache {
    export { DNS_RESOLUTION_RTT_MULTIPLIER as RTT_MULTIPLIER };
}
import * as LH from "../../../../types/lh.js";
declare const DNS_RESOLUTION_RTT_MULTIPLIER: 2;
export {};
//# sourceMappingURL=dns-cache.d.ts.map