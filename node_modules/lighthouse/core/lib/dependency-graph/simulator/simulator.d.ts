export type Node = import('../base-node.js').Node;
export type NetworkNode = import('../network-node.js').NetworkNode;
export type CpuNode = import('../cpu-node.js').CPUNode;
export type CompleteNodeTiming = import('./simulator-timing-map.js').CpuNodeTimingComplete | import('./simulator-timing-map.js').NetworkNodeTimingComplete;
export type ConnectionTiming = import('./simulator-timing-map.js').ConnectionTiming;
export class Simulator {
    /** @return {Map<string, Map<Node, CompleteNodeTiming>>} */
    static get ALL_NODE_TIMINGS(): Map<string, Map<import("../base-node.js").Node, CompleteNodeTiming>>;
    /**
     * We attempt to start nodes by their observed start time using the record priority as a tie breaker.
     * When simulating, just because a low priority image started 5ms before a high priority image doesn't mean
     * it would have happened like that when the network was slower.
     * @param {Node} node
     */
    static _computeNodeStartPosition(node: Node): number;
    /**
     * @param {LH.Gatherer.Simulation.Options} [options]
     */
    constructor(options?: LH.Gatherer.Simulation.Options | undefined);
    /** @type {Required<LH.Gatherer.Simulation.Options>} */
    _options: Required<LH.Gatherer.Simulation.Options>;
    _rtt: number;
    _throughput: number;
    _maximumConcurrentRequests: number;
    _cpuSlowdownMultiplier: number;
    _layoutTaskMultiplier: number;
    /** @type {Array<Node>} */
    _cachedNodeListByStartPosition: Array<Node>;
    _flexibleOrdering: boolean;
    _nodeTimings: SimulatorTimingMap;
    /** @type {Map<string, number>} */
    _numberInProgressByType: Map<string, number>;
    /** @type {Record<number, Set<Node>>} */
    _nodes: Record<number, Set<Node>>;
    _dns: DNSCache;
    /** @type {ConnectionPool} */
    _connectionPool: ConnectionPool;
    /** @return {number} */
    get rtt(): number;
    /**
     * @param {Node} graph
     */
    _initializeConnectionPool(graph: Node): void;
    /**
     * Initializes the various state data structures such _nodeTimings and the _node Sets by state.
     */
    _initializeAuxiliaryData(): void;
    /**
     * @param {string} type
     * @return {number}
     */
    _numberInProgress(type: string): number;
    /**
     * @param {Node} node
     * @param {number} queuedTime
     */
    _markNodeAsReadyToStart(node: Node, queuedTime: number): void;
    /**
     * @param {Node} node
     * @param {number} startTime
     */
    _markNodeAsInProgress(node: Node, startTime: number): void;
    /**
     * @param {Node} node
     * @param {number} endTime
     * @param {ConnectionTiming} [connectionTiming] Optional network connection information.
     */
    _markNodeAsComplete(node: Node, endTime: number, connectionTiming?: import("./simulator-timing-map.js").ConnectionTiming | undefined): void;
    /**
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {?TcpConnection}
     */
    _acquireConnection(record: LH.Artifacts.NetworkRequest): TcpConnection | null;
    /**
     * @return {Node[]}
     */
    _getNodesSortedByStartPosition(): Node[];
    /**
     * @param {Node} node
     * @param {number} totalElapsedTime
     */
    _startNodeIfPossible(node: Node, totalElapsedTime: number): void;
    /**
     * Updates each connection in use with the available throughput based on the number of network requests
     * currently in flight.
     */
    _updateNetworkCapacity(): void;
    /**
     * Estimates the number of milliseconds remaining given current condidtions before the node is complete.
     * @param {Node} node
     * @return {number}
     */
    _estimateTimeRemaining(node: Node): number;
    /**
     * @param {CpuNode} cpuNode
     * @return {number}
     */
    _estimateCPUTimeRemaining(cpuNode: CpuNode): number;
    /**
     * @param {NetworkNode} networkNode
     * @return {number}
     */
    _estimateNetworkTimeRemaining(networkNode: NetworkNode): number;
    /**
     * Computes and returns the minimum estimated completion time of the nodes currently in progress.
     * @return {number}
     */
    _findNextNodeCompletionTime(): number;
    /**
     * Given a time period, computes the progress toward completion that the node made durin that time.
     * @param {Node} node
     * @param {number} timePeriodLength
     * @param {number} totalElapsedTime
     */
    _updateProgressMadeInTimePeriod(node: Node, timePeriodLength: number, totalElapsedTime: number): number | void;
    /**
     * @return {{nodeTimings: Map<Node, LH.Gatherer.Simulation.NodeTiming>, completeNodeTimings: Map<Node, CompleteNodeTiming>}}
     */
    _computeFinalNodeTimings(): {
        nodeTimings: Map<Node, LH.Gatherer.Simulation.NodeTiming>;
        completeNodeTimings: Map<Node, CompleteNodeTiming>;
    };
    /**
     * @return {Required<LH.Gatherer.Simulation.Options>}
     */
    getOptions(): Required<LH.Gatherer.Simulation.Options>;
    /**
     * Estimates the time taken to process all of the graph's nodes, returns the overall time along with
     * each node annotated by start/end times.
     *
     * If flexibleOrdering is set, simulator/connection pool are allowed to deviate from what was
     * observed in the trace/devtoolsLog and start requests as soon as they are queued (i.e. do not
     * wait around for a warm connection to be available if the original record was fetched on a warm
     * connection).
     *
     * @param {Node} graph
     * @param {{flexibleOrdering?: boolean, label?: string}=} options
     * @return {LH.Gatherer.Simulation.Result}
     */
    simulate(graph: Node, options?: {
        flexibleOrdering?: boolean;
        label?: string;
    } | undefined): LH.Gatherer.Simulation.Result;
    /**
     * @param {number} wastedBytes
     */
    computeWastedMsFromWastedBytes(wastedBytes: number): number;
}
import * as LH from "../../../../types/lh.js";
import { SimulatorTimingMap } from "./simulator-timing-map.js";
import { DNSCache } from "./dns-cache.js";
import { ConnectionPool } from "./connection-pool.js";
import { TcpConnection } from "./tcp-connection.js";
//# sourceMappingURL=simulator.d.ts.map