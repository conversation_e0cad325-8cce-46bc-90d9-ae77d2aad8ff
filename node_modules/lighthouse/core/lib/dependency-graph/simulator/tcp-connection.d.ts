export type ConnectionTiming = import('./simulator-timing-map.js').ConnectionTiming;
export type DownloadOptions = {
    dnsResolutionTime?: number | undefined;
    timeAlreadyElapsed?: number | undefined;
    maximumTimeToElapse?: number | undefined;
};
export type DownloadResults = {
    roundTrips: number;
    timeElapsed: number;
    bytesDownloaded: number;
    extraBytesDownloaded: number;
    congestionWindow: number;
    connectionTiming: ConnectionTiming;
};
export class TcpConnection {
    /**
     * @param {number} rtt
     * @param {number} availableThroughput
     * @return {number}
     */
    static maximumSaturatedConnections(rtt: number, availableThroughput: number): number;
    /**
     * @param {number} rtt
     * @param {number} throughput
     * @param {number=} serverLatency
     * @param {boolean=} ssl
     * @param {boolean=} h2
     */
    constructor(rtt: number, throughput: number, serverLatency?: number | undefined, ssl?: boolean | undefined, h2?: boolean | undefined);
    _warmed: boolean;
    _ssl: boolean;
    _h2: boolean;
    _rtt: number;
    _throughput: number;
    _serverLatency: number;
    _congestionWindow: number;
    _h2OverflowBytesDownloaded: number;
    /**
     * @return {number}
     */
    _computeMaximumCongestionWindowInSegments(): number;
    /**
     * @param {number} throughput
     */
    setThroughput(throughput: number): void;
    /**
     * @param {number} congestion
     */
    setCongestionWindow(congestion: number): void;
    /**
     * @param {boolean} warmed
     */
    setWarmed(warmed: boolean): void;
    /**
     * @return {boolean}
     */
    isWarm(): boolean;
    /**
     * @return {boolean}
     */
    isH2(): boolean;
    /**
     * @return {number}
     */
    get congestionWindow(): number;
    /**
     * Sets the number of excess bytes that are available to this connection on future downloads, only
     * applies to H2 connections.
     * @param {number} bytes
     */
    setH2OverflowBytesDownloaded(bytes: number): void;
    /**
     * @return {TcpConnection}
     */
    clone(): TcpConnection;
    /**
     * Simulates a network download of a particular number of bytes over an optional maximum amount of time
     * and returns information about the ending state.
     *
     * See https://hpbn.co/building-blocks-of-tcp/#three-way-handshake and
     *  https://hpbn.co/transport-layer-security-tls/#tls-handshake for details.
     *
     * @param {number} bytesToDownload
     * @param {DownloadOptions} [options]
     * @return {DownloadResults}
     */
    simulateDownloadUntil(bytesToDownload: number, options?: DownloadOptions | undefined): DownloadResults;
}
//# sourceMappingURL=tcp-connection.d.ts.map