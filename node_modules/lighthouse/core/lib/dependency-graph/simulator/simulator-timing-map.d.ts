export type Node = import('../base-node.js').Node;
export type NetworkNode = import('../network-node.js').NetworkNode;
export type CpuNode = import('../cpu-node.js').CPUNode;
export type NodeTimingComplete = {
    startTime: number;
    endTime: number;
    /**
     * Helpful for debugging.
     */
    queuedTime: number;
    estimatedTimeElapsed: number;
    timeElapsed: number;
    timeElapsedOvershoot: number;
    bytesDownloaded: number;
};
export type NodeTimingQueued = Pick<NodeTimingComplete, 'queuedTime'>;
export type CpuNodeTimingStarted = NodeTimingQueued & Pick<NodeTimingComplete, 'startTime' | 'timeElapsed'>;
export type NetworkNodeTimingStarted = CpuNodeTimingStarted & Pick<NodeTimingComplete, 'timeElapsedOvershoot' | 'bytesDownloaded'>;
export type CpuNodeTimingInProgress = CpuNodeTimingStarted & Pick<NodeTimingComplete, 'estimatedTimeElapsed'>;
export type NetworkNodeTimingInProgress = NetworkNodeTimingStarted & Pick<NodeTimingComplete, 'estimatedTimeElapsed'>;
export type CpuNodeTimingComplete = CpuNodeTimingInProgress & Pick<NodeTimingComplete, 'endTime'>;
export type NetworkNodeTimingComplete = NetworkNodeTimingInProgress & Pick<NodeTimingComplete, 'endTime'> & {
    connectionTiming: ConnectionTiming;
};
export type NodeTimingData = NodeTimingQueued | CpuNodeTimingStarted | NetworkNodeTimingStarted | CpuNodeTimingInProgress | NetworkNodeTimingInProgress | CpuNodeTimingComplete | NetworkNodeTimingComplete;
/**
 * A breakdown of network connection timings.
 */
export type ConnectionTiming = {
    dnsResolutionTime?: number | undefined;
    connectionTime?: number | undefined;
    sslTime?: number | undefined;
    timeToFirstByte: number;
};
/**
 * @fileoverview
 *
 * This class encapsulates the type-related validation logic for moving timing information for nodes
 * through the different simulation phases. Methods here ensure that the invariants of simulation hold
 * as nodes are queued, partially simulated, and completed.
 */
/** @typedef {import('../base-node.js').Node} Node */
/** @typedef {import('../network-node.js').NetworkNode} NetworkNode */
/** @typedef {import('../cpu-node.js').CPUNode} CpuNode */
/**
 * @typedef NodeTimingComplete
 * @property {number} startTime
 * @property {number} endTime
 * @property {number} queuedTime Helpful for debugging.
 * @property {number} estimatedTimeElapsed
 * @property {number} timeElapsed
 * @property {number} timeElapsedOvershoot
 * @property {number} bytesDownloaded
 */
/** @typedef {Pick<NodeTimingComplete, 'queuedTime'>} NodeTimingQueued */
/** @typedef {NodeTimingQueued & Pick<NodeTimingComplete, 'startTime'|'timeElapsed'>} CpuNodeTimingStarted */
/** @typedef {CpuNodeTimingStarted & Pick<NodeTimingComplete, 'timeElapsedOvershoot'|'bytesDownloaded'>} NetworkNodeTimingStarted */
/** @typedef {CpuNodeTimingStarted & Pick<NodeTimingComplete, 'estimatedTimeElapsed'>} CpuNodeTimingInProgress */
/** @typedef {NetworkNodeTimingStarted & Pick<NodeTimingComplete, 'estimatedTimeElapsed'>} NetworkNodeTimingInProgress */
/** @typedef {CpuNodeTimingInProgress & Pick<NodeTimingComplete, 'endTime'>} CpuNodeTimingComplete */
/** @typedef {NetworkNodeTimingInProgress & Pick<NodeTimingComplete, 'endTime'> & {connectionTiming: ConnectionTiming}} NetworkNodeTimingComplete */
/** @typedef {NodeTimingQueued | CpuNodeTimingStarted | NetworkNodeTimingStarted | CpuNodeTimingInProgress | NetworkNodeTimingInProgress | CpuNodeTimingComplete | NetworkNodeTimingComplete} NodeTimingData */
/**
 * @typedef ConnectionTiming A breakdown of network connection timings.
 * @property {number} [dnsResolutionTime]
 * @property {number} [connectionTime]
 * @property {number} [sslTime]
 * @property {number} timeToFirstByte
 */
export class SimulatorTimingMap {
    /** @type {Map<Node, NodeTimingData>} */
    _nodeTimings: Map<Node, NodeTimingData>;
    /** @return {Array<Node>} */
    getNodes(): Array<Node>;
    /**
     * @param {Node} node
     * @param {{queuedTime: number}} values
     */
    setReadyToStart(node: Node, values: {
        queuedTime: number;
    }): void;
    /**
     * @param {Node} node
     * @param {{startTime: number}} values
     */
    setInProgress(node: Node, values: {
        startTime: number;
    }): void;
    /**
     * @param {Node} node
     * @param {{endTime: number, connectionTiming?: ConnectionTiming}} values
     */
    setCompleted(node: Node, values: {
        endTime: number;
        connectionTiming?: ConnectionTiming;
    }): void;
    /**
     * @param {CpuNode} node
     * @param {{timeElapsed: number}} values
     */
    setCpu(node: CpuNode, values: {
        timeElapsed: number;
    }): void;
    /**
     * @param {CpuNode} node
     * @param {{estimatedTimeElapsed: number}} values
     */
    setCpuEstimated(node: CpuNode, values: {
        estimatedTimeElapsed: number;
    }): void;
    /**
     * @param {NetworkNode} node
     * @param {{timeElapsed: number, timeElapsedOvershoot: number, bytesDownloaded: number}} values
     */
    setNetwork(node: NetworkNode, values: {
        timeElapsed: number;
        timeElapsedOvershoot: number;
        bytesDownloaded: number;
    }): void;
    /**
     * @param {NetworkNode} node
     * @param {{estimatedTimeElapsed: number}} values
     */
    setNetworkEstimated(node: NetworkNode, values: {
        estimatedTimeElapsed: number;
    }): void;
    /**
     * @param {Node} node
     * @return {NodeTimingQueued}
     */
    getQueued(node: Node): NodeTimingQueued;
    /**
     * @param {CpuNode} node
     * @return {CpuNodeTimingStarted}
     */
    getCpuStarted(node: CpuNode): CpuNodeTimingStarted;
    /**
     * @param {NetworkNode} node
     * @return {NetworkNodeTimingStarted}
     */
    getNetworkStarted(node: NetworkNode): NetworkNodeTimingStarted;
    /**
     * @param {Node} node
     * @return {CpuNodeTimingInProgress | NetworkNodeTimingInProgress}
     */
    getInProgress(node: Node): CpuNodeTimingInProgress | NetworkNodeTimingInProgress;
    /**
     * @param {Node} node
     * @return {CpuNodeTimingComplete | NetworkNodeTimingComplete}
     */
    getCompleted(node: Node): CpuNodeTimingComplete | NetworkNodeTimingComplete;
}
//# sourceMappingURL=simulator-timing-map.d.ts.map