export class ConnectionPool {
    /**
     * @param {LH.Artifacts.NetworkRequest[]} records
     * @param {Required<LH.Gatherer.Simulation.Options>} options
     */
    constructor(records: LH.Artifacts.NetworkRequest[], options: Required<LH.Gatherer.Simulation.Options>);
    _options: Required<LH.Gatherer.Simulation.Options>;
    _records: import("../../network-request.js").NetworkRequest[];
    /** @type {Map<string, TcpConnection[]>} */
    _connectionsByOrigin: Map<string, TcpConnection[]>;
    /** @type {Map<LH.Artifacts.NetworkRequest, TcpConnection>} */
    _connectionsByRecord: Map<LH.Artifacts.NetworkRequest, TcpConnection>;
    _connectionsInUse: Set<any>;
    _connectionReusedByRequestId: Map<string, boolean>;
    /**
     * @return {TcpConnection[]}
     */
    connectionsInUse(): TcpConnection[];
    _initializeConnections(): void;
    /**
     * @param {Array<TcpConnection>} connections
     * @param {{ignoreConnectionReused?: boolean, observedConnectionWasReused: boolean}} options
     */
    _findAvailableConnectionWithLargestCongestionWindow(connections: Array<TcpConnection>, options: {
        ignoreConnectionReused?: boolean | undefined;
        observedConnectionWasReused: boolean;
    }): TcpConnection | null;
    /**
     * This method finds an available connection to the origin specified by the network record or null
     * if no connection was available. If returned, connection will not be available for other network
     * records until release is called.
     *
     * If ignoreConnectionReused is true, acquire will consider all connections not in use as available.
     * Otherwise, only connections that have matching "warmth" are considered available.
     *
     * @param {LH.Artifacts.NetworkRequest} record
     * @param {{ignoreConnectionReused?: boolean}} options
     * @return {?TcpConnection}
     */
    acquire(record: LH.Artifacts.NetworkRequest, options?: {
        ignoreConnectionReused?: boolean;
    }): TcpConnection | null;
    /**
     * Return the connection currently being used to fetch a record. If no connection
     * currently being used for this record, an error will be thrown.
     *
     * @param {LH.Artifacts.NetworkRequest} record
     * @return {TcpConnection}
     */
    acquireActiveConnectionFromRecord(record: LH.Artifacts.NetworkRequest): TcpConnection;
    /**
     * @param {LH.Artifacts.NetworkRequest} record
     */
    release(record: LH.Artifacts.NetworkRequest): void;
}
import * as LH from "../../../../types/lh.js";
import { TcpConnection } from "./tcp-connection.js";
//# sourceMappingURL=connection-pool.d.ts.map