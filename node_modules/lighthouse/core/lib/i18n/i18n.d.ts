export type LhlMessages = import('../../../shared/localization/locales').LhlMessages;
export namespace UIStrings {
    const ms: string;
    const seconds: string;
    const displayValueByteSavings: string;
    const displayValueMsSavings: string;
    const displayValueElementsFound: string;
    const columnURL: string;
    const columnSize: string;
    const columnResourceSize: string;
    const columnTransferSize: string;
    const columnCacheTTL: string;
    const columnWastedBytes: string;
    const columnWastedMs: string;
    const columnBlockingTime: string;
    const columnTimeSpent: string;
    const columnLocation: string;
    const columnResourceType: string;
    const columnRequests: string;
    const columnName: string;
    const columnSource: string;
    const columnOverBudget: string;
    const columnElement: string;
    const columnStartTime: string;
    const columnDuration: string;
    const columnFailingElem: string;
    const columnDescription: string;
    const totalResourceType: string;
    const documentResourceType: string;
    const scriptResourceType: string;
    const stylesheetResourceType: string;
    const imageResourceType: string;
    const mediaResourceType: string;
    const fontResourceType: string;
    const otherResourceType: string;
    const thirdPartyResourceType: string;
    const otherResourcesLabel: string;
    const firstContentfulPaintMetric: string;
    const interactiveMetric: string;
    const firstMeaningfulPaintMetric: string;
    const totalBlockingTimeMetric: string;
    const maxPotentialFIDMetric: string;
    const speedIndexMetric: string;
    const largestContentfulPaintMetric: string;
    const cumulativeLayoutShiftMetric: string;
    const interactionToNextPaint: string;
    const itemSeverityLow: string;
    const itemSeverityMedium: string;
    const itemSeverityHigh: string;
}
/**
 * Look up the best available locale for the requested language through these fall backs:
 * - exact match
 * - progressively shorter prefixes (`de-CH-1996` -> `de-CH` -> `de`)
 * - supported locales in Intl formatters
 *
 * If `locale` isn't provided or one could not be found, DEFAULT_LOCALE is returned.
 *
 * By default any of the locales Lighthouse has strings for can be returned, but this
 * can be overridden with `possibleLocales`, useful e.g. when Lighthouse is bundled and
 * only DEFAULT_LOCALE is available, but `possibleLocales` can be used to select a
 * locale available to be downloaded on demand.
 * @param {string|string[]=} locales
 * @param {Array<string>=} possibleLocales
 * @return {LH.Locale}
 */
export function lookupLocale(locales?: (string | string[]) | undefined, possibleLocales?: Array<string> | undefined): LH.Locale;
/**
 * Returns a function that generates `LH.IcuMessage` objects to localize the
 * messages in `fileStrings` and the shared `i18n.UIStrings`.
 * @param {string} filename
 * @param {Record<string, string>} fileStrings
 */
export function createIcuMessageFn(filename: string, fileStrings: Record<string, string>): (message: string, values?: Record<string, string | number> | undefined) => LH.IcuMessage;
/**
 * Returns true if the given value is a string or an LH.IcuMessage.
 * @param {unknown} value
 * @return {value is string|LH.IcuMessage}
 */
export function isStringOrIcuMessage(value: unknown): value is string | import("../../index.js").IcuMessage;
//# sourceMappingURL=i18n.d.ts.map