/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {LH.Config.Settings} settings
 * @return {Promise<void>}
 */
export function emulate(session: LH.Gatherer.FRProtocolSession, settings: LH.Config.Settings): Promise<void>;
/**
 * Sets the throttling options specified in config settings, clearing existing network throttling if
 * throttlingMethod is not `devtools` (but not CPU throttling, suspected requirement of WPT-compat).
 *
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {LH.Config.Settings} settings
 * @return {Promise<void>}
 */
export function throttle(session: LH.Gatherer.FRProtocolSession, settings: LH.Config.Settings): Promise<void>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @return {Promise<void>}
 */
export function clearThrottling(session: LH.Gatherer.FRProtocolSession): Promise<void>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {Required<LH.ThrottlingSettings>} throttlingSettings
 * @return {Promise<void>}
 */
export function enableNetworkThrottling(session: LH.Gatherer.FRProtocolSession, throttlingSettings: Required<LH.ThrottlingSettings>): Promise<void>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @return {Promise<void>}
 */
export function clearNetworkThrottling(session: LH.Gatherer.FRProtocolSession): Promise<void>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @param {Required<LH.ThrottlingSettings>} throttlingSettings
 * @return {Promise<void>}
 */
export function enableCPUThrottling(session: LH.Gatherer.FRProtocolSession, throttlingSettings: Required<LH.ThrottlingSettings>): Promise<void>;
/**
 * @param {LH.Gatherer.FRProtocolSession} session
 * @return {Promise<void>}
 */
export function clearCPUThrottling(session: LH.Gatherer.FRProtocolSession): Promise<void>;
//# sourceMappingURL=emulation.d.ts.map