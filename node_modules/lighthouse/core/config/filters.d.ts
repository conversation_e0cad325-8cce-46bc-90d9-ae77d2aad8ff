/**
 * Filters a config's artifacts, audits, and categories down to the set that supports the specified gather mode.
 *
 * @param {LH.Config.ResolvedConfig} resolvedConfig
 * @param {LH.Gatherer.GatherMode} mode
 * @return {LH.Config.ResolvedConfig}
 */
export function filterConfigByGatherMode(resolvedConfig: LH.Config.ResolvedConfig, mode: LH.Gatherer.GatherMode): LH.Config.ResolvedConfig;
/**
 * Filters a config's artifacts, audits, and categories down to the requested set.
 * Skip audits overrides inclusion via `onlyAudits`/`onlyCategories`.
 *
 * @param {LH.Config.ResolvedConfig} resolvedConfig
 * @param {Pick<LH.Config.Settings, 'onlyAudits'|'onlyCategories'|'skipAudits'>} filters
 * @return {LH.Config.ResolvedConfig}
 */
export function filterConfigByExplicitFilters(resolvedConfig: LH.Config.ResolvedConfig, filters: Pick<LH.Config.Settings, 'onlyAudits' | 'onlyCategories' | 'skipAudits'>): LH.Config.ResolvedConfig;
/**
 * Filters an array of artifacts down to the set that supports the specified gather mode.
 *
 * @param {LH.Config.ResolvedConfig['artifacts']} artifacts
 * @param {LH.Gatherer.GatherMode} mode
 * @return {LH.Config.ResolvedConfig['artifacts']}
 */
export function filterArtifactsByGatherMode(artifacts: LH.Config.ResolvedConfig['artifacts'], mode: LH.Gatherer.GatherMode): LH.Config.ResolvedConfig['artifacts'];
/**
 * Filters an array of artifacts down to the set that's required by the specified audits.
 *
 * @param {LH.Config.ResolvedConfig['artifacts']} artifacts
 * @param {LH.Config.ResolvedConfig['audits']} audits
 * @return {LH.Config.ResolvedConfig['artifacts']}
 */
export function filterArtifactsByAvailableAudits(artifacts: LH.Config.ResolvedConfig['artifacts'], audits: LH.Config.ResolvedConfig['audits']): LH.Config.ResolvedConfig['artifacts'];
/**
 * Filters an array of navigations down to the set supported by the available artifacts.
 *
 * @param {LH.Config.ResolvedConfig['navigations']} navigations
 * @param {Array<LH.Config.AnyArtifactDefn>} availableArtifacts
 * @return {LH.Config.ResolvedConfig['navigations']}
 */
export function filterNavigationsByAvailableArtifacts(navigations: LH.Config.ResolvedConfig['navigations'], availableArtifacts: Array<LH.Config.AnyArtifactDefn>): LH.Config.ResolvedConfig['navigations'];
/**
 * Filters an array of audits down to the set that can be computed using only the specified artifacts.
 *
 * @param {LH.Config.ResolvedConfig['audits']} audits
 * @param {Array<LH.Config.AnyArtifactDefn>} availableArtifacts
 * @return {LH.Config.ResolvedConfig['audits']}
 */
export function filterAuditsByAvailableArtifacts(audits: LH.Config.ResolvedConfig['audits'], availableArtifacts: Array<LH.Config.AnyArtifactDefn>): LH.Config.ResolvedConfig['audits'];
/**
 * Optional `supportedModes` property can explicitly exclude an audit even if all required artifacts are available.
 *
 * @param {LH.Config.ResolvedConfig['audits']} audits
 * @param {LH.Gatherer.GatherMode} mode
 * @return {LH.Config.ResolvedConfig['audits']}
 */
export function filterAuditsByGatherMode(audits: LH.Config.ResolvedConfig['audits'], mode: LH.Gatherer.GatherMode): LH.Config.ResolvedConfig['audits'];
/**
 * Filters a categories object and their auditRefs down to the set that can be computed using
 * only the specified audits.
 *
 * @param {LH.Config.LegacyResolvedConfig['categories']} categories
 * @param {Array<LH.Config.AuditDefn>} availableAudits
 * @return {LH.Config.LegacyResolvedConfig['categories']}
 */
export function filterCategoriesByAvailableAudits(categories: LH.Config.LegacyResolvedConfig['categories'], availableAudits: Array<LH.Config.AuditDefn>): LH.Config.LegacyResolvedConfig['categories'];
/**
 * Filters a categories object and their auditRefs down to the specified category ids.
 *
 * @param {LH.Config.LegacyResolvedConfig['categories']} categories
 * @param {string[] | null | undefined} onlyCategories
 * @return {LH.Config.LegacyResolvedConfig['categories']}
 */
export function filterCategoriesByExplicitFilters(categories: LH.Config.LegacyResolvedConfig['categories'], onlyCategories: string[] | null | undefined): LH.Config.LegacyResolvedConfig['categories'];
/**
 * Optional `supportedModes` property can explicitly exclude a category even if some audits are available.
 *
 * @param {LH.Config.LegacyResolvedConfig['categories']} categories
 * @param {LH.Gatherer.GatherMode} mode
 * @return {LH.Config.LegacyResolvedConfig['categories']}
 */
export function filterCategoriesByGatherMode(categories: LH.Config.LegacyResolvedConfig['categories'], mode: LH.Gatherer.GatherMode): LH.Config.LegacyResolvedConfig['categories'];
//# sourceMappingURL=filters.d.ts.map