export class Budget {
    /**
     * Asserts that obj has no own properties, throwing a nice error message if it does.
     * `objectName` is included for nicer logging.
     * @param {Record<string, unknown>} obj
     * @param {string} objectName
     */
    static assertNoExcessProperties(obj: Record<string, unknown>, objectName: string): void;
    /**
     * Asserts that `strings` has no duplicate strings in it, throwing an error if
     * it does. `arrayName` is included for nicer logging.
     * @param {Array<string>} strings
     * @param {string} arrayName
     */
    static assertNoDuplicateStrings(strings: Array<string>, arrayName: string): void;
    /**
     * @param {Record<string, unknown>} resourceBudget
     * @return {LH.Budget.ResourceBudget}
     */
    static validateResourceBudget(resourceBudget: Record<string, unknown>): LH.Budget.ResourceBudget;
    /**
     * @param {unknown} path
     * @param {string} error
     */
    static throwInvalidPathError(path: unknown, error: string): void;
    /**
     * Validates that path is either: a) undefined or ) properly formed.
     * Verifies the quantity and location of the two robot.txt regex characters: $, *
     * @param {unknown} path
     * @return {undefined|string}
     */
    static validatePath(path: unknown): undefined | string;
    /**
     * Returns the budget that applies to a given URL.
     * If multiple budgets match based on thier 'path' property,
     * then the last-listed of those budgets is returned.
     * @param {LH.Util.Immutable<Array<LH.Budget>>|null} budgets
     * @param {string|undefined} url
     * @return {LH.Util.Immutable<LH.Budget> | undefined} budget
     */
    static getMatchingBudget(budgets: LH.Util.Immutable<Array<LH.Budget>> | null, url: string | undefined): LH.Util.Immutable<LH.Budget> | undefined;
    /**
     * Determines whether a URL matches against a robots.txt-style "path".
     * Pattern should use the robots.txt format. E.g. "/*-article.html" or "/". Reference:
     * https://developers.google.com/search/reference/robots_txt#url-matching-based-on-path-values
     * @param {string} url
     * @param {string=} pattern
     * @return {boolean}
     */
    static urlMatchesPattern(url: string, pattern?: string | undefined): boolean;
    /**
     * @param {Record<string, unknown>} timingBudget
     * @return {LH.Budget.TimingBudget}
     */
    static validateTimingBudget(timingBudget: Record<string, unknown>): LH.Budget.TimingBudget;
    /**
     * @param {string} hostname
     * @return {string}
     */
    static validateHostname(hostname: string): string;
    /**
     * @param {unknown} hostnames
     * @return {undefined|Array<string>}
     */
    static validateHostnames(hostnames: unknown): undefined | Array<string>;
    /**
     * More info on the Budget format:
     * https://github.com/GoogleChrome/lighthouse/issues/6053#issuecomment-428385930
     * @param {unknown} budgetJson
     * @return {Array<LH.Budget>}
     */
    static initializeBudget(budgetJson: unknown): Array<LH.Budget>;
}
//# sourceMappingURL=budget.d.ts.map