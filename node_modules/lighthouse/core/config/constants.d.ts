export namespace throttling {
    export { DEVTOOLS_RTT_ADJUSTMENT_FACTOR };
    export { DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR };
    export namespace mobileSlow4G {
        const rttMs: number;
        const throughputKbps: number;
        const requestLatencyMs: number;
        const downloadThroughputKbps: number;
        const uploadThroughputKbps: number;
        const cpuSlowdownMultiplier: number;
    }
    export namespace mobileRegular3G {
        const rttMs_1: number;
        export { rttMs_1 as rttMs };
        const throughputKbps_1: number;
        export { throughputKbps_1 as throughputKbps };
        const requestLatencyMs_1: number;
        export { requestLatencyMs_1 as requestLatencyMs };
        const downloadThroughputKbps_1: number;
        export { downloadThroughputKbps_1 as downloadThroughputKbps };
        const uploadThroughputKbps_1: number;
        export { uploadThroughputKbps_1 as uploadThroughputKbps };
        const cpuSlowdownMultiplier_1: number;
        export { cpuSlowdownMultiplier_1 as cpuSlowdownMultiplier };
    }
    export namespace desktopDense4G {
        const rttMs_2: number;
        export { rttMs_2 as rttMs };
        const throughputKbps_2: number;
        export { throughputKbps_2 as throughputKbps };
        const cpuSlowdownMultiplier_2: number;
        export { cpuSlowdownMultiplier_2 as cpuSlowdownMultiplier };
        const requestLatencyMs_2: number;
        export { requestLatencyMs_2 as requestLatencyMs };
        const downloadThroughputKbps_2: number;
        export { downloadThroughputKbps_2 as downloadThroughputKbps };
        const uploadThroughputKbps_2: number;
        export { uploadThroughputKbps_2 as uploadThroughputKbps };
    }
}
export namespace screenEmulationMetrics {
    export { MOTOGPOWER_EMULATION_METRICS as mobile };
    export { DESKTOP_EMULATION_METRICS as desktop };
}
export namespace userAgents {
    export { MOTOG4_USERAGENT as mobile };
    export { DESKTOP_USERAGENT as desktop };
}
/** @type {LH.Config.Settings} */
export const defaultSettings: LH.Config.Settings;
/** @type {LH.Config.Pass} */
export const defaultPassConfig: LH.Config.Pass;
/** @type {Required<LH.Config.NavigationJson>} */
export const defaultNavigationConfig: Required<LH.Config.NavigationJson>;
export namespace nonSimulatedPassConfigOverrides {
    const pauseAfterFcpMs: number;
    const pauseAfterLoadMs: number;
    const networkQuietThresholdMs: number;
    const cpuQuietThresholdMs: number;
}
/**
 * @license Copyright 2018 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * Adjustments needed for DevTools network throttling to simulate
 * more realistic network conditions.
 * @see https://crbug.com/721112
 * @see https://docs.google.com/document/d/10lfVdS1iDWCRKQXPfbxEn4Or99D64mvNlugP1AQuFlE/edit
 */
declare const DEVTOOLS_RTT_ADJUSTMENT_FACTOR: 3.75;
declare const DEVTOOLS_THROUGHPUT_ADJUSTMENT_FACTOR: 0.9;
/**
 * @type {Required<LH.SharedFlagsSettings['screenEmulation']>}
 */
declare const MOTOGPOWER_EMULATION_METRICS: Required<LH.SharedFlagsSettings['screenEmulation']>;
/**
 * Desktop metrics adapted from emulated_devices/module.json
 * @type {Required<LH.SharedFlagsSettings['screenEmulation']>}
 */
declare const DESKTOP_EMULATION_METRICS: Required<LH.SharedFlagsSettings['screenEmulation']>;
declare const MOTOG4_USERAGENT: "Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Mobile Safari/537.36";
declare const DESKTOP_USERAGENT: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36";
export {};
//# sourceMappingURL=constants.d.ts.map