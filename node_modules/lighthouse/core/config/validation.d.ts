/**
 * @param {LH.Config.GathererDefn | LH.Config.AnyFRGathererDefn} gathererDefn
 * @return {gathererDefn is LH.Config.AnyFRGathererDefn}
 */
export function isFRGathererDefn(gathererDefn: LH.Config.GathererDefn | LH.Config.AnyFRGathererDefn): gathererDefn is import("../../types/config.js").default.AnyFRGathererDefn;
/**
 * Determines if the artifact dependency direction is valid. The dependency's minimum supported mode
 * must be less than or equal to the dependent's.
 *
 * @param {LH.Config.AnyFRGathererDefn} dependent The artifact that depends on the other.
 * @param {LH.Config.AnyFRGathererDefn} dependency The artifact that is being depended on by the other.
 * @return {boolean}
 */
export function isValidArtifactDependency(dependent: LH.Config.AnyFRGathererDefn, dependency: LH.Config.AnyFRGathererDefn): boolean;
/**
 * Throws if pluginName is invalid or (somehow) collides with a category in the
 * config being added to.
 * @param {LH.Config} config
 * @param {string} pluginName
 */
export function assertValidPluginName(config: LH.Config, pluginName: string): void;
/**
 * Throws an error if the provided object does not implement the required Fraggle Rock gatherer interface.
 * @param {LH.Config.AnyFRGathererDefn} gathererDefn
 */
export function assertValidFRGatherer(gathererDefn: LH.Config.AnyFRGathererDefn): void;
/**
 * Throws an error if the provided object does not implement the required navigations interface.
 * @param {LH.Config.ResolvedConfig['navigations']} navigationsDefn
 * @return {{warnings: string[]}}
 */
export function assertValidFRNavigations(navigationsDefn: LH.Config.ResolvedConfig['navigations']): {
    warnings: string[];
};
/**
 * Throws an error if the provided object does not implement the required properties of an audit
 * definition.
 * @param {LH.Config.AuditDefn} auditDefinition
 */
export function assertValidAudit(auditDefinition: LH.Config.AuditDefn): void;
/**
 * @param {LH.Config.ResolvedConfig['categories']} categories
 * @param {LH.Config.ResolvedConfig['audits']} audits
 * @param {LH.Config.ResolvedConfig['groups']} groups
 */
export function assertValidCategories(categories: LH.Config.ResolvedConfig['categories'], audits: LH.Config.ResolvedConfig['audits'], groups: LH.Config.ResolvedConfig['groups']): void;
/**
 * Validate the settings after they've been built.
 * @param {LH.Config.Settings} settings
 */
export function assertValidSettings(settings: LH.Config.Settings): void;
/**
 * Asserts that artifacts are in a valid dependency order that can be computed.
 *
 * @param {Array<LH.Config.NavigationDefn>} navigations
 */
export function assertArtifactTopologicalOrder(navigations: Array<LH.Config.NavigationDefn>): void;
/**
 * @param {LH.Config.ResolvedConfig} resolvedConfig
 * @return {{warnings: string[]}}
 */
export function assertValidConfig(resolvedConfig: LH.Config.ResolvedConfig): {
    warnings: string[];
};
/**
 * @param {string} artifactId
 * @param {string} dependencyKey
 * @return {never}
 */
export function throwInvalidDependencyOrder(artifactId: string, dependencyKey: string): never;
/**
 * @param {string} artifactId
 * @param {string} dependencyKey
 * @return {never}
 */
export function throwInvalidArtifactDependency(artifactId: string, dependencyKey: string): never;
//# sourceMappingURL=validation.d.ts.map