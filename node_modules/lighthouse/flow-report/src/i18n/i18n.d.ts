/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
import { FunctionComponent } from 'preact';
import { I18nFormatter } from '../../../report/renderer/i18n-formatter';
declare function useI18n(): {
    formatter: I18nFormatter;
    strings: {
        navigationDescription: string;
        timespanDescription: string;
        snapshotDescription: string;
        navigationLongDescription: string;
        timespanLongDescription: string;
        snapshotLongDescription: string;
        navigationReport: string;
        timespanReport: string;
        snapshotReport: string;
        summary: string;
        allReports: string;
        title: string;
        categories: string;
        categoryPerformance: string;
        categoryAccessibility: string;
        categoryBestPractices: string;
        categorySeo: string;
        categoryProgressiveWebApp: string;
        desktop: string;
        mobile: string;
        ratingPass: string;
        ratingAverage: string;
        ratingFail: string;
        ratingError: string;
        navigationReportCount: string;
        timespanReportCount: string;
        snapshotReportCount: string;
        save: string;
        helpLabel: string;
        helpDialogTitle: string;
        helpUseCaseInstructionNavigation: string;
        helpUseCaseInstructionTimespan: string;
        helpUseCaseInstructionSnapshot: string;
        helpUseCaseNavigation1: string;
        helpUseCaseNavigation2: string;
        helpUseCaseNavigation3: string;
        helpUseCaseTimespan1: string;
        helpUseCaseTimespan2: string;
        helpUseCaseSnapshot1: string;
        helpUseCaseSnapshot2: string;
        passedAuditCount: string;
        passableAuditCount: string;
        informativeAuditCount: string;
        highestImpact: string;
        varianceDisclaimer: string;
        calculatorLink: string;
        showRelevantAudits: string;
        opportunityResourceColumnLabel: string;
        opportunitySavingsColumnLabel: string;
        errorMissingAuditInfo: string;
        errorLabel: string;
        warningHeader: string;
        warningAuditsGroupTitle: string;
        passedAuditsGroupTitle: string;
        notApplicableAuditsGroupTitle: string;
        manualAuditsGroupTitle: string;
        toplevelWarningsMessage: string;
        crcInitialNavigation: string;
        crcLongestDurationLabel: string;
        snippetExpandButtonLabel: string;
        snippetCollapseButtonLabel: string;
        lsPerformanceCategoryDescription: string;
        labDataTitle: string;
        thirdPartyResourcesLabel: string;
        viewTreemapLabel: string;
        viewTraceLabel: string;
        viewOriginalTraceLabel: string;
        dropdownPrintSummary: string;
        dropdownPrintExpanded: string;
        dropdownCopyJSON: string;
        dropdownSaveHTML: string;
        dropdownSaveJSON: string;
        dropdownViewer: string;
        dropdownSaveGist: string;
        dropdownDarkTheme: string;
        runtimeSettingsDevice: string;
        runtimeSettingsNetworkThrottling: string;
        runtimeSettingsCPUThrottling: string;
        runtimeSettingsUANetwork: string;
        runtimeSettingsBenchmark: string;
        runtimeSettingsAxeVersion: string;
        runtimeSettingsScreenEmulation: string;
        footerIssue: string;
        runtimeNoEmulation: string;
        runtimeMobileEmulation: string;
        runtimeDesktopEmulation: string;
        runtimeUnknown: string;
        runtimeSingleLoad: string;
        runtimeAnalysisWindow: string;
        runtimeSingleLoadTooltip: string;
        throttlingProvided: string;
        show: string;
        hide: string;
        expandView: string;
        collapseView: string;
        runtimeSlow4g: string;
        runtimeCustom: string;
        firstPartyChipLabel: string;
        openInANewTabTooltip: string;
        unattributable: string;
    };
};
declare function useLocalizedStrings(): {
    navigationDescription: string;
    timespanDescription: string;
    snapshotDescription: string;
    navigationLongDescription: string;
    timespanLongDescription: string;
    snapshotLongDescription: string;
    navigationReport: string;
    timespanReport: string;
    snapshotReport: string;
    summary: string;
    allReports: string;
    title: string;
    categories: string;
    categoryPerformance: string;
    categoryAccessibility: string;
    categoryBestPractices: string;
    categorySeo: string;
    categoryProgressiveWebApp: string;
    desktop: string;
    mobile: string;
    ratingPass: string;
    ratingAverage: string;
    ratingFail: string;
    ratingError: string;
    navigationReportCount: string;
    timespanReportCount: string;
    snapshotReportCount: string;
    save: string;
    helpLabel: string;
    helpDialogTitle: string;
    helpUseCaseInstructionNavigation: string;
    helpUseCaseInstructionTimespan: string;
    helpUseCaseInstructionSnapshot: string;
    helpUseCaseNavigation1: string;
    helpUseCaseNavigation2: string;
    helpUseCaseNavigation3: string;
    helpUseCaseTimespan1: string;
    helpUseCaseTimespan2: string;
    helpUseCaseSnapshot1: string;
    helpUseCaseSnapshot2: string;
    passedAuditCount: string;
    passableAuditCount: string;
    informativeAuditCount: string;
    highestImpact: string;
    varianceDisclaimer: string;
    calculatorLink: string;
    showRelevantAudits: string;
    opportunityResourceColumnLabel: string;
    opportunitySavingsColumnLabel: string;
    errorMissingAuditInfo: string;
    errorLabel: string;
    warningHeader: string;
    warningAuditsGroupTitle: string;
    passedAuditsGroupTitle: string;
    notApplicableAuditsGroupTitle: string;
    manualAuditsGroupTitle: string;
    toplevelWarningsMessage: string;
    crcInitialNavigation: string;
    crcLongestDurationLabel: string;
    snippetExpandButtonLabel: string;
    snippetCollapseButtonLabel: string;
    lsPerformanceCategoryDescription: string;
    labDataTitle: string;
    thirdPartyResourcesLabel: string;
    viewTreemapLabel: string;
    viewTraceLabel: string;
    viewOriginalTraceLabel: string;
    dropdownPrintSummary: string;
    dropdownPrintExpanded: string;
    dropdownCopyJSON: string;
    dropdownSaveHTML: string;
    dropdownSaveJSON: string;
    dropdownViewer: string;
    dropdownSaveGist: string;
    dropdownDarkTheme: string;
    runtimeSettingsDevice: string;
    runtimeSettingsNetworkThrottling: string;
    runtimeSettingsCPUThrottling: string;
    runtimeSettingsUANetwork: string;
    runtimeSettingsBenchmark: string;
    runtimeSettingsAxeVersion: string;
    runtimeSettingsScreenEmulation: string;
    footerIssue: string;
    runtimeNoEmulation: string;
    runtimeMobileEmulation: string;
    runtimeDesktopEmulation: string;
    runtimeUnknown: string;
    runtimeSingleLoad: string;
    runtimeAnalysisWindow: string;
    runtimeSingleLoadTooltip: string;
    throttlingProvided: string;
    show: string;
    hide: string;
    expandView: string;
    collapseView: string;
    runtimeSlow4g: string;
    runtimeCustom: string;
    firstPartyChipLabel: string;
    openInANewTabTooltip: string;
    unattributable: string;
};
declare function useStringFormatter(): (str: string, values?: Record<string, string | number>) => string;
declare const I18nProvider: FunctionComponent;
export { useI18n, useLocalizedStrings, useStringFormatter, I18nProvider, };
//# sourceMappingURL=i18n.d.ts.map