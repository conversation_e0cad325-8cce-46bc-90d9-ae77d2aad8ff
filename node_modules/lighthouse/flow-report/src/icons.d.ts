/**
 * @license Copyright 2021 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
import { FunctionComponent } from 'preact';
declare const SummaryIcon: FunctionComponent;
declare const NavigationIcon: FunctionComponent;
declare const TimespanIcon: FunctionComponent;
declare const SnapshotIcon: FunctionComponent;
declare const CloseIcon: FunctionComponent;
declare const EnvIcon: FunctionComponent;
declare const NetworkIcon: FunctionComponent;
declare const CpuIcon: FunctionComponent;
declare const HamburgerIcon: FunctionComponent;
declare const InfoIcon: FunctionComponent;
export { SummaryIcon, NavigationIcon, TimespanIcon, SnapshotIcon, CloseIcon, EnvIcon, NetworkIcon, CpuIcon, HamburgerIcon, InfoIcon, };
//# sourceMappingURL=icons.d.ts.map