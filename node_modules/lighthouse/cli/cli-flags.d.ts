/**
 * @param {string=} manualArgv
 * @param {{noExitOnFailure?: boolean}=} options
 * @return {LH.CliFlags}
 */
export function getFlags(manualArgv?: string | undefined, options?: {
    noExitOnFailure?: boolean;
} | undefined): LH.CliFlags;
/**
 * @param {string=} manualArgv
 */
export function getYargsParser(manualArgv?: string | undefined): yargs.Argv<yargs.Omit<yargs.Omit<yargs.Omit<yargs.Omit<yargs.Omit<yargs.Omit<yargs.Omit<yargs.Omit<{
    _: string[] | undefined;
} & {
    "cli-flags-path": unknown;
}, "verbose" | "quiet"> & yargs.InferredOptionTypes<{
    verbose: {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    quiet: {
        type: "boolean";
        default: boolean;
        describe: string;
    };
}>, "port" | "screenEmulation" | "emulatedUserAgent" | "hostname" | "preset" | "save-assets" | "list-all-audits" | "list-locales" | "list-trace-categories" | "debug-navigation" | "legacy-navigation" | "additional-trace-categories" | "config-path" | "chrome-flags" | "form-factor" | "max-wait-for-load" | "enable-error-reporting" | "gather-mode" | "audit-mode" | "only-audits" | "only-categories" | "skip-audits" | "budget-path" | "disable-full-page-screenshot"> & yargs.InferredOptionTypes<{
    'save-assets': {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    'list-all-audits': {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    'list-locales': {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    'list-trace-categories': {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    'debug-navigation': {
        type: "boolean";
        describe: string;
    };
    'legacy-navigation': {
        type: "boolean";
        default: boolean;
        describe: string;
    };
    'additional-trace-categories': {
        type: "string";
        describe: string;
    };
    'config-path': {
        type: "string";
        describe: string;
    };
    preset: {
        type: "string";
        describe: string;
    };
    'chrome-flags': {
        type: "string";
        default: string;
        describe: string;
    };
    port: {
        type: "number";
        default: number;
        describe: string;
    };
    hostname: {
        type: "string";
        default: string;
        describe: string;
    };
    'form-factor': {
        type: "string";
        describe: string;
    };
    screenEmulation: {
        describe: string;
        coerce: typeof coerceScreenEmulation;
    };
    emulatedUserAgent: {
        type: "string";
        coerce: typeof coerceOptionalStringBoolean;
        describe: string;
    };
    'max-wait-for-load': {
        type: "number";
        describe: string;
    };
    'enable-error-reporting': {
        type: "boolean";
        describe: string;
    };
    'gather-mode': {
        alias: string;
        coerce: typeof coerceOptionalStringBoolean;
        describe: string;
    };
    'audit-mode': {
        alias: string;
        coerce: typeof coerceOptionalStringBoolean;
        describe: string;
    };
    'only-audits': {
        array: true;
        type: "string";
        coerce: typeof splitCommaSeparatedValues;
        describe: string;
    };
    'only-categories': {
        array: true;
        type: "string";
        coerce: typeof splitCommaSeparatedValues;
        describe: string;
    };
    'skip-audits': {
        array: true;
        type: "string";
        coerce: typeof splitCommaSeparatedValues;
        describe: string;
    };
    'budget-path': {
        type: "string";
        describe: string;
    };
    'disable-full-page-screenshot': {
        type: "boolean";
        describe: string;
    };
}>, "output" | "view" | "output-path"> & yargs.InferredOptionTypes<{
    output: {
        type: "array";
        default: readonly ["html"];
        coerce: typeof coerceOutput;
        describe: string;
    };
    'output-path': {
        type: "string";
        coerce: typeof coerceOutputPath;
        describe: string;
    };
    view: {
        type: "boolean";
        default: boolean;
        describe: string;
    };
}>, "locale" | "blocked-url-patterns" | "disable-storage-reset" | "throttling-method"> & yargs.InferredOptionTypes<{
    locale: {
        coerce: typeof coerceLocale;
        describe: string;
    };
    'blocked-url-patterns': {
        array: true;
        type: "string";
        describe: string;
    };
    'disable-storage-reset': {
        type: "boolean";
        describe: string;
    };
    'throttling-method': {
        type: "string";
        describe: string;
    };
}> & {
    throttling: import("../types/lh.js").ThrottlingSettings | undefined;
}, "channel" | "plugins" | "extra-headers" | "precomputed-lantern-data-path" | "lantern-data-output-path" | "chrome-ignore-default-flags"> & yargs.InferredOptionTypes<{
    'extra-headers': {
        coerce: typeof coerceExtraHeaders;
        describe: string;
    };
    'precomputed-lantern-data-path': {
        type: "string";
        describe: string;
    };
    'lantern-data-output-path': {
        type: "string";
        describe: string;
    };
    plugins: {
        array: true;
        type: "string";
        coerce: typeof splitCommaSeparatedValues;
        describe: string;
    };
    channel: {
        type: "string";
        default: string;
    };
    'chrome-ignore-default-flags': {
        type: "boolean";
        default: boolean;
    };
}>, "form-factor"> & {
    "form-factor": "mobile" | "desktop" | undefined;
}, "throttling-method"> & {
    "throttling-method": "devtools" | "simulate" | "provided" | undefined;
}, "preset"> & {
    preset: "desktop" | "experimental" | "perf" | undefined;
}>;
import yargs from "yargs";
/**
 * Take yarg's unchecked object value and ensure it is a proper LH.screenEmulationSettings.
 * @param {unknown} value
 * @return {Partial<LH.ScreenEmulationSettings>|undefined}
 */
declare function coerceScreenEmulation(value: unknown): Partial<LH.ScreenEmulationSettings> | undefined;
/**
 * @param {unknown} value
 * @return {boolean|string|undefined}
 */
declare function coerceOptionalStringBoolean(value: unknown): boolean | string | undefined;
/**
 * Support comma-separated values for some array flags by splitting on any ',' found.
 * @param {Array<string>=} strings
 * @return {Array<string>=}
 */
declare function splitCommaSeparatedValues(strings?: Array<string> | undefined): Array<string> | undefined;
/**
 * Coerce output CLI input to `LH.SharedFlagsSettings['output']` or throw if not possible.
 * @param {Array<unknown>} values
 * @return {Array<LH.OutputMode>}
 */
declare function coerceOutput(values: Array<unknown>): Array<LH.OutputMode>;
/**
 * Verifies outputPath is something we can actually write to.
 * @param {unknown=} value
 * @return {string=}
 */
declare function coerceOutputPath(value?: unknown | undefined): string | undefined;
/**
 * Verifies value is a string, then coerces type to LH.Locale for convenience. However, don't
 * allowlist specific locales. Why? So we can support the user who requests 'es-MX' (unsupported)
 * and we'll fall back to 'es' (supported).
 * @param {unknown} value
 * @return {LH.Locale|undefined}
 */
declare function coerceLocale(value: unknown): LH.Locale | undefined;
/**
 * `--extra-headers` comes in as a JSON string or a path to a JSON string, but the flag value
 * needs to be the parsed object. Load file (if necessary) and returns the parsed object.
 * @param {unknown} value
 * @return {LH.SharedFlagsSettings['extraHeaders']}
 */
declare function coerceExtraHeaders(value: unknown): LH.SharedFlagsSettings['extraHeaders'];
export {};
//# sourceMappingURL=cli-flags.d.ts.map