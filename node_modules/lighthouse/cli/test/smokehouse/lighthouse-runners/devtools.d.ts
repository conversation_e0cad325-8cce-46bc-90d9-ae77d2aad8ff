/**
 * Launch Chrome and do a full Lighthouse run via DevTools.
 * By default, the latest DevTools frontend is used (.tmp/chromium-web-tests/devtools/devtools-frontend)
 * unless DEVTOOLS_PATH is set.
 * CHROME_PATH determines which Chrome is used–otherwise the default is puppeteer's chrome binary.
 * @param {string} url
 * @param {LH.Config=} config
 * @param {{isDebug?: boolean, useLegacyNavigation?: boolean}=} testRunnerOptions
 * @return {Promise<{lhr: LH.Result, artifacts: LH.Artifacts, log: string}>}
 */
export function runLighthouse(url: string, config?: LH.Config | undefined, testRunnerOptions?: {
    isDebug?: boolean;
    useLegacyNavigation?: boolean;
} | undefined): Promise<{
    lhr: LH.Result;
    artifacts: LH.Artifacts;
    log: string;
}>;
/**
 * Download/pull latest DevTools, build Lighthouse for DevTools, roll to DevTools, and build DevTools.
 */
export function setup(): Promise<void>;
//# sourceMappingURL=devtools.d.ts.map