/// <reference path="../../../../../../types/internal/lighthouse-logger.d.ts" />
/**
 * Launch Chrome and do a full Lighthouse run via the Lighthouse CLI.
 * @param {string} url
 * @param {LH.Config=} config
 * @param {{isDebug?: boolean, useFraggleRock?: boolean}=} testRunnerOptions
 * @return {Promise<{lhr: LH.Result, artifacts: LH.Artifacts, log: string}>}
 */
export function runLighthouse(url: string, config?: LH.Config | undefined, testRunnerOptions?: {
    isDebug?: boolean | undefined;
    useFraggleRock?: boolean | undefined;
} | undefined): Promise<{
    lhr: LH.Result;
    artifacts: LH.Artifacts;
    log: string;
}>;
import log from "lighthouse-logger";
//# sourceMappingURL=cli.d.ts.map