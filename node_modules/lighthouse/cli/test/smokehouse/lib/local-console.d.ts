/**
 * @license Copyright 2019 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
 */
/**
 * A simple buffered log to use in place of `console`.
 */
export class LocalConsole {
    _log: string;
    /**
     * @param {string} str
     */
    log(str: string): void;
    /**
     * Log but without the ending newline.
     * @param {string} str
     */
    write(str: string): void;
    /**
     * @return {string}
     */
    getLog(): string;
    /**
     * Append a stdout and stderr to this log.
     * @param {{stdout: string, stderr: string}} stdStrings
     */
    adoptStdStrings(stdStrings: {
        stdout: string;
        stderr: string;
    }): void;
}
//# sourceMappingURL=local-console.d.ts.map