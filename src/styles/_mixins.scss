// Cobra Ryu SCSS Mixins

// Media Query Mixins
@mixin media($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin media-max($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (max-width: map-get($breakpoints, $breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin media-between($min-breakpoint, $max-breakpoint) {
  @if map-has-key($breakpoints, $min-breakpoint) and map-has-key($breakpoints, $max-breakpoint) {
    @media (min-width: map-get($breakpoints, $min-breakpoint)) and (max-width: map-get($breakpoints, $max-breakpoint) - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoints: #{$min-breakpoint} or #{$max-breakpoint}";
  }
}

// Container Mixin
@mixin container($max-width: null) {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);

  @include media(sm) {
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }

  @include media(lg) {
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }

  @if $max-width {
    max-width: $max-width;
  } @else {
    max-width: map-get($container-max-widths, 2xl);
  }
}

// Flexbox Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  @include flex-column;
  align-items: center;
  justify-content: center;
}

// Grid Mixins
@mixin grid($columns: 1, $gap: var(--spacing-6)) {
  display: grid;
  grid-template-columns: repeat($columns, 1fr);
  gap: $gap;
}

@mixin grid-responsive($mobile: 1, $tablet: 2, $desktop: 3, $gap: var(--spacing-6)) {
  @include grid($mobile, $gap);

  @include media(md) {
    grid-template-columns: repeat($tablet, 1fr);
  }

  @include media(lg) {
    grid-template-columns: repeat($desktop, 1fr);
  }
}

// Typography Mixins
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin text-truncate-lines($lines) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

@mixin heading($size: 'lg') {
  font-family: var(--font-sans);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  color: var(--color-text-primary);

  @if $size == 'xs' {
    font-size: var(--text-lg);
  } @else if $size == 'sm' {
    font-size: var(--text-xl);
  } @else if $size == 'md' {
    font-size: var(--text-2xl);
  } @else if $size == 'lg' {
    font-size: var(--text-3xl);
  } @else if $size == 'xl' {
    font-size: var(--text-4xl);
  } @else if $size == '2xl' {
    font-size: var(--text-5xl);
  } @else if $size == '3xl' {
    font-size: var(--text-6xl);
  }
}

@mixin body-text($size: 'base') {
  font-family: var(--font-sans);
  font-weight: var(--font-normal);
  line-height: var(--leading-relaxed);
  color: var(--color-text-secondary);

  @if $size == 'sm' {
    font-size: var(--text-sm);
  } @else if $size == 'base' {
    font-size: var(--text-base);
  } @else if $size == 'lg' {
    font-size: var(--text-lg);
  }
}

// Button Mixins
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--btn-padding-md);
  font-family: var(--font-sans);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  line-height: 1;
  text-decoration: none;
  white-space: nowrap;
  border: 2px solid transparent;
  border-radius: var(--btn-radius);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  user-select: none;
  position: relative;
  overflow: hidden;

  &:focus-visible {
    outline: 2px solid var(--color-border-focus);
    outline-offset: 2px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  // Hover shimmer effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--duration-500) var(--ease-out);
  }

  &:hover::before {
    left: 100%;
  }
}

@mixin button-variant($bg-color, $text-color, $hover-bg-color: null, $border-color: null) {
  @include button-base;
  
  background-color: $bg-color;
  color: $text-color;
  
  @if $border-color {
    border-color: $border-color;
  }

  &:hover {
    @if $hover-bg-color {
      background-color: $hover-bg-color;
    } @else {
      background-color: darken($bg-color, 10%);
    }
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  &:active {
    transform: translateY(0);
  }
}

@mixin button-size($size) {
  @if $size == 'sm' {
    padding: var(--btn-padding-sm);
    font-size: var(--text-sm);
  } @else if $size == 'md' {
    padding: var(--btn-padding-md);
    font-size: var(--text-base);
  } @else if $size == 'lg' {
    padding: var(--btn-padding-lg);
    font-size: var(--text-lg);
  }
}

// Card Mixins
@mixin card {
  background: var(--color-bg-primary);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

@mixin card-hover {
  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--card-shadow-hover);
  }
}

@mixin card-padding($size: 'md') {
  @if $size == 'sm' {
    padding: var(--spacing-4);
  } @else if $size == 'md' {
    padding: var(--spacing-6);
  } @else if $size == 'lg' {
    padding: var(--spacing-8);
  }
}

// Form Mixins
@mixin form-input {
  appearance: none;
  width: 100%;
  padding: var(--input-padding);
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  border: var(--input-border-width) solid var(--color-border-primary);
  border-radius: var(--input-radius);
  transition: all var(--duration-200) var(--ease-out);

  &::placeholder {
    color: var(--color-text-muted);
  }

  &:focus {
    outline: none;
    border-color: var(--color-border-focus);
    box-shadow: 0 0 0 3px rgb(220 38 38 / 0.1);
  }

  &:invalid {
    border-color: var(--color-error);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-family: var(--font-sans);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--color-text-primary);
}

// Animation Mixins
@mixin animate($name, $duration: var(--duration-300), $easing: var(--ease-out), $delay: 0s) {
  animation: $name $duration $easing $delay forwards;
}

@mixin fade-in($duration: var(--duration-300), $delay: 0s) {
  opacity: 0;
  animation: fade-in $duration var(--ease-out) $delay forwards;
}

@mixin slide-in-up($duration: var(--duration-500), $delay: 0s, $distance: 30px) {
  opacity: 0;
  transform: translateY($distance);
  animation: slide-in-up $duration var(--ease-out) $delay forwards;
}

@mixin slide-in-down($duration: var(--duration-500), $delay: 0s, $distance: 30px) {
  opacity: 0;
  transform: translateY(-$distance);
  animation: slide-in-down $duration var(--ease-out) $delay forwards;
}

@mixin slide-in-left($duration: var(--duration-500), $delay: 0s, $distance: 30px) {
  opacity: 0;
  transform: translateX(-$distance);
  animation: slide-in-left $duration var(--ease-out) $delay forwards;
}

@mixin slide-in-right($duration: var(--duration-500), $delay: 0s, $distance: 30px) {
  opacity: 0;
  transform: translateX($distance);
  animation: slide-in-right $duration var(--ease-out) $delay forwards;
}

@mixin scale-in($duration: var(--duration-300), $delay: 0s) {
  opacity: 0;
  transform: scale(0.9);
  animation: scale-in $duration var(--ease-out) $delay forwards;
}

// Hover Effects Mixins
@mixin hover-lift($distance: 4px) {
  transition: transform var(--duration-300) var(--ease-out);

  &:hover {
    transform: translateY(-$distance);
  }
}

@mixin hover-grow($scale: 1.05) {
  transition: transform var(--duration-300) var(--ease-out);

  &:hover {
    transform: scale($scale);
  }
}

@mixin hover-rotate($angle: 5deg) {
  transition: transform var(--duration-300) var(--ease-out);

  &:hover {
    transform: rotate($angle);
  }
}

// Glassmorphism Mixins
@mixin glassmorphism($opacity: 0.25, $blur: 10px) {
  background: rgba(255, 255, 255, $opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

@mixin glassmorphism-dark($opacity: 0.25, $blur: 10px) {
  background: rgba(0, 0, 0, $opacity);
  backdrop-filter: blur($blur);
  -webkit-backdrop-filter: blur($blur);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

// Gradient Mixins
@mixin gradient-primary {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
}

@mixin gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary-800) 0%, var(--color-secondary-900) 100%);
}

@mixin gradient-accent {
  background: linear-gradient(135deg, var(--color-accent-500) 0%, var(--color-accent-600) 100%);
}

@mixin gradient-hero {
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(220, 38, 38, 0.8) 100%);
}

// Loading State Mixins
@mixin skeleton {
  background: linear-gradient(90deg, var(--color-secondary-200) 25%, var(--color-secondary-100) 50%, var(--color-secondary-200) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-base);
}

@mixin loading-spinner($size: 20px, $color: var(--color-primary-600)) {
  display: inline-block;
  width: $size;
  height: $size;
  border: 2px solid rgba($color, 0.3);
  border-radius: 50%;
  border-top-color: $color;
  animation: spin 1s ease-in-out infinite;
}

// Utility Mixins
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin aspect-ratio($width, $height) {
  aspect-ratio: $width / $height;
  
  // Fallback for older browsers
  @supports not (aspect-ratio: 1) {
    position: relative;
    
    &::before {
      content: '';
      display: block;
      padding-top: percentage($height / $width);
    }
    
    > * {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}

@mixin scrollbar-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

@mixin scrollbar-custom($track-color: var(--color-secondary-100), $thumb-color: var(--color-secondary-400)) {
  & {
    scrollbar-width: thin;
    scrollbar-color: $thumb-color $track-color;
  }
  
  &::-webkit-scrollbar {
    width: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: $track-color;
    border-radius: var(--radius-full);
  }
  
  &::-webkit-scrollbar-thumb {
    background: $thumb-color;
    border-radius: var(--radius-full);
    
    &:hover {
      background: var(--color-secondary-500);
    }
  }
}

// Focus Management Mixins
@mixin focus-ring($color: var(--color-primary-500)) {
  &:focus-visible {
    outline: 2px solid $color;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba($color, 0.1);
  }
}

@mixin focus-ring-inset($color: var(--color-primary-500)) {
  &:focus-visible {
    outline: none;
    box-shadow: inset 0 0 0 2px $color;
  }
}

// Accessibility Mixins
@mixin high-contrast {
  @media (prefers-contrast: high) {
    @content;
  }
}

@mixin reduced-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}

@mixin dark-mode {
  @media (prefers-color-scheme: dark) {
    @content;
  }
} 