// Cobra Ryu Strakonice - Main SCSS Entry Point
// Modern 2025 styling with advanced techniques

// Base styles and utilities
@use './variables' as *;
@use './mixins' as *;

// Modern CSS Reset
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: var(--header-height);
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-sans);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1;
  font-kerning: normal;
  
  // Prevent horizontal scroll on mobile
  overflow-x: hidden;
  
  // Disable zoom on double tap (iOS)
  touch-action: manipulation;
}

// Modern scrollbar
@include scrollbar-custom;

// Screen reader only class
.sr-only {
  @include sr-only;
}

// Focus-visible styles for accessibility
.focus\:not-sr-only:focus {
  position: absolute !important;
  width: auto !important;
  height: auto !important;
  padding: var(--spacing-2) var(--spacing-4) !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
  z-index: var(--z-skiplink);
  background: var(--color-primary-600);
  color: white;
  border-radius: var(--radius-lg);
  text-decoration: none;
  font-weight: var(--font-semibold);
}

// Global animations
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// Animation utility classes
.animate-fade-in {
  animation: fade-in var(--duration-500) var(--ease-out) forwards;
}

.animate-fade-in-up {
  animation: fade-in-up var(--duration-500) var(--ease-out) forwards;
}

.animate-fade-in-down {
  animation: fade-in-down var(--duration-500) var(--ease-out) forwards;
}

.animate-slide-in-left {
  animation: slide-in-left var(--duration-500) var(--ease-out) forwards;
}

.animate-slide-in-right {
  animation: slide-in-right var(--duration-500) var(--ease-out) forwards;
}

.animate-scale-in {
  animation: scale-in var(--duration-300) var(--ease-out) forwards;
}

.animate-bounce {
  animation: bounce 2s infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

// Animation delay classes
.delay-100 {
  animation-delay: 100ms;
  opacity: 0;
}

.delay-200 {
  animation-delay: 200ms;
  opacity: 0;
}

.delay-300 {
  animation-delay: 300ms;
  opacity: 0;
}

.delay-400 {
  animation-delay: 400ms;
  opacity: 0;
}

.delay-500 {
  animation-delay: 500ms;
  opacity: 0;
}

.delay-600 {
  animation-delay: 600ms;
  opacity: 0;
}

// Button Components
.btn {
  @include button-base;
  
  &-primary {
    @include button-variant(
      var(--color-primary-600),
      white,
      var(--color-primary-700)
    );
  }
  
  &-secondary {
    @include button-variant(
      transparent,
      white,
      white,
      white
    );
    
    &:hover {
      color: var(--color-gray-900) !important;
    }
  }
  
  &-ghost {
    @include button-variant(
      transparent,
      var(--color-primary-600),
      var(--color-primary-600),
      var(--color-primary-600)
    );
    
    &:hover {
      color: white !important;
    }
  }
  
  // Button sizes
  &-sm {
    @include button-size(sm);
  }
  
  &-md {
    @include button-size(md);
  }
  
  &-lg {
    @include button-size(lg);
  }
  
  // Button states
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  // Loading state
  .btn-loading {
    display: none;
  }
  
  &.loading {
    .btn-text {
      display: none;
    }
    
    .btn-loading {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Card Components
.card {
  @include card;
  
  &-hover {
    @include card-hover;
  }
  
  &-padding {
    @include card-padding(md);
    
    &-sm {
      @include card-padding(sm);
    }
    
    &-lg {
      @include card-padding(lg);
    }
  }
}

// Form Components
.form-input {
  @include form-input;
}

.form-label {
  @include form-label;
}

// Navigation Styles
#main-navigation {
  @include glassmorphism(0.95, 10px);
  border-bottom: 1px solid var(--color-border-primary);
  transition: all var(--duration-300) var(--ease-out);
  
  &.scrolled {
    box-shadow: var(--shadow-lg);
    background: rgba(255, 255, 255, 0.98);
  }
  
  .nav-link {
    position: relative;
    transition: all var(--duration-200) var(--ease-out);
    
    &::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--color-primary-600);
      transition: all var(--duration-300) var(--ease-out);
      transform: translateX(-50%);
    }
    
    &.active::after,
    &:hover::after {
      width: 100%;
    }
  }
  
  .mobile-nav-link {
    transition: all var(--duration-200) var(--ease-out);
    
    &:hover {
      background: var(--color-primary-50);
      transform: translateX(4px);
    }
    
    &.active {
      background: var(--color-primary-100);
      color: var(--color-primary-700);
      font-weight: var(--font-semibold);
    }
  }
}

// Background Patterns
.bg-dot-pattern {
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
}

// Hero Section Styles
#home {
  position: relative;
  
  // Parallax effect
  .absolute.inset-0 {
    will-change: transform;
  }
  
  // Enhanced gradient overlay
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
      ellipse at center,
      rgba(220, 38, 38, 0.1) 0%,
      rgba(0, 0, 0, 0.3) 100%
    );
    z-index: 1;
  }
  
  .relative.z-10 {
    z-index: 2;
  }
}

// Section Styles
section {
  scroll-margin-top: var(--header-height);
  
  // Intersection observer animation trigger
  &.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--duration-700) var(--ease-out);
    
    &.visible {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

// Loading Screen
#loading-screen {
  position: fixed;
  inset: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(220, 38, 38, 0.8) 100%);
  color: white;
  transition: opacity var(--duration-500) var(--ease-out);
  
  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
  }
}

// PWA Install Button
#pwa-install-button {
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(10px);
  
  &:hover {
    box-shadow: var(--shadow-2xl);
  }
}

// Utility Classes
.container {
  @include container;
}

.flex-center {
  @include flex-center;
}

.flex-between {
  @include flex-between;
}

.flex-column {
  @include flex-column;
}

.gradient-primary {
  @include gradient-primary;
}

.gradient-secondary {
  @include gradient-secondary;
}

.gradient-accent {
  @include gradient-accent;
}

.gradient-hero {
  @include gradient-hero;
}

.glass {
  @include glassmorphism;
}

.glass-dark {
  @include glassmorphism-dark;
}

.hover-lift {
  @include hover-lift;
}

.hover-grow {
  @include hover-grow;
}

.hover-rotate {
  @include hover-rotate;
}

.skeleton {
  @include skeleton;
}

// Focus ring utilities
.focus-ring {
  @include focus-ring;
}

.focus-ring-inset {
  @include focus-ring-inset;
}

// Responsive utilities
@include media(sm) {
  .sm\:hidden {
    display: none;
  }
  
  .sm\:block {
    display: block;
  }
  
  .sm\:flex {
    display: flex;
  }
}

@include media(md) {
  .md\:hidden {
    display: none;
  }
  
  .md\:block {
    display: block;
  }
  
  .md\:flex {
    display: flex;
  }
}

@include media(lg) {
  .lg\:hidden {
    display: none;
  }
  
  .lg\:block {
    display: block;
  }
  
  .lg\:flex {
    display: flex;
  }
}

// Print styles
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .btn {
    display: none !important;
  }
  
  section {
    page-break-inside: avoid;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
  
  .printing {
    .card {
      box-shadow: none !important;
      border: 1px solid #ccc !important;
    }
  }
}

// Dark mode support
@include dark-mode {
  .auto-dark {
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
  }
}

// High contrast mode
@include high-contrast {
  .btn-primary {
    border: 2px solid black !important;
  }
  
  .form-input {
    border-width: 3px !important;
  }
  
  .nav-link {
    text-decoration: underline;
  }
}

// Reduced motion
@include reduced-motion {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
} 