/**
 * Cobra Ryu Strakonice - Ultra-Modern Main Application
 * Built with TypeScript, Vite, and modern web standards
 */

import '@/styles/main.scss';
import { AppConfig } from '@/types';
import { EventBus } from '@/utils/event-bus';
import { Logger } from '@/utils/logger';

// Application configuration
const APP_CONFIG: AppConfig = {
  version: '2025.2.0',
  isDevelopment: true,
  apiEndpoints: {
    contact: '/api/contact',
    gallery: '/api/gallery',
  },
  social: {
    facebook: 'https://www.facebook.com/Cobra-Security-sro-461449347211225',
    youtube: '',
    instagram: '',
  },
  contact: {
    name: 'Cobra Ryu Strakonice',
    address: 'Volyňská 157, Strakonice, 38601',
    phone: '226 88 919',
    email: '<EMAIL>',
    coordinates: {
      lat: 49.2613744,
      lng: 13.9027244,
    },
  },
};

/**
 * Simplified Main Application Class
 */
export class CobraRyuApp {
  private readonly logger: Logger;
  private readonly eventBus: EventBus;
  private isInitialized = false;

  constructor(config: AppConfig = APP_CONFIG) {
    // Initialize core services
    this.logger = new Logger(config.isDevelopment);
    this.eventBus = new EventBus();

    // Setup global error handling
    this.setupErrorHandling();
    
    // Initialize application
    this.init(config);
  }

  /**
   * Initialize the application
   */
  private async init(config: AppConfig): Promise<void> {
    try {
      this.logger.info('🥋 Initializing Cobra Ryu Application v' + config.version);

      // Wait for DOM to be ready
      await this.waitForDOM();

      // Initialize basic functionality
      this.initializeBasicFeatures();

      // Setup global event listeners
      this.setupGlobalEvents();

      // Mark as initialized
      this.isInitialized = true;

      // Emit ready event
      this.eventBus.emit('app:ready', { config });

      this.logger.success('✅ Cobra Ryu Application initialized successfully!');

    } catch (error) {
      this.logger.error('❌ Failed to initialize Cobra Ryu Application:', error);
      throw error;
    }
  }

  /**
   * Wait for DOM to be ready
   */
  private waitForDOM(): Promise<void> {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => resolve());
      } else {
        resolve();
      }
    });
  }

  /**
   * Initialize basic features
   */
  private initializeBasicFeatures(): void {
    // Hide loading screen
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
      setTimeout(() => {
        loadingScreen.classList.add('hidden');
      }, 1000);
    }

    // Initialize navigation
    this.initializeNavigation();

    // Initialize forms
    this.initializeForms();

    this.logger.info('🔧 Basic features initialized successfully');
  }

  /**
   * Initialize navigation functionality
   */
  private initializeNavigation(): void {
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener('click', () => {
        const isOpen = !mobileMenu.classList.contains('hidden');
        mobileMenu.classList.toggle('hidden', isOpen);
        mobileMenuButton.setAttribute('aria-expanded', (!isOpen).toString());
      });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', (e) => {
        e.preventDefault();
        const href = anchor.getAttribute('href');
        if (href && href !== '#') {
          const target = document.querySelector(href);
          if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
            // Close mobile menu if open
            if (mobileMenu) {
              mobileMenu.classList.add('hidden');
              mobileMenuButton?.setAttribute('aria-expanded', 'false');
            }
          }
        }
      });
    });
  }

  /**
   * Initialize forms
   */
  private initializeForms(): void {
    const contactForm = document.getElementById('contact-form') as HTMLFormElement;
    if (contactForm) {
      contactForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.handleFormSubmit(contactForm);
      });
    }
  }

  /**
   * Handle form submission
   */
  private async handleFormSubmit(form: HTMLFormElement): Promise<void> {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    this.logger.info('📧 Form submitted:', data);
    
    // Simulate form submission
    const submitButton = form.querySelector('button[type="submit"]') as HTMLButtonElement;
    if (submitButton) {
      submitButton.classList.add('loading');
      submitButton.disabled = true;
      
      setTimeout(() => {
        submitButton.classList.remove('loading');
        submitButton.disabled = false;
        alert('Zpráva byla odeslána! (Simulace)');
        form.reset();
      }, 2000);
    }
  }

  /**
   * Setup event bus listeners
   */
  private setupEventBusListeners(): void {
    // Basic error handling
    this.eventBus.on('error:handled', (error: Error) => {
      this.logger.error('🚨 Application error:', error);
    });
  }

  /**
   * Setup global error handling
   */
  private setupErrorHandling(): void {
    // Handle uncaught errors
    window.addEventListener('error', (event) => {
      this.logger.error('💥 Uncaught error:', event.error);
      this.eventBus.emit('error:uncaught', event.error);
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logger.error('🚨 Unhandled promise rejection:', event.reason);
      this.eventBus.emit('error:unhandled-promise', event.reason);
      event.preventDefault();
    });
  }

  /**
   * Setup global event listeners
   */
  private setupGlobalEvents(): void {
    // Basic visibility change tracking
    document.addEventListener('visibilitychange', () => {
      const isVisible = !document.hidden;
      this.eventBus.emit('app:visibility-changed', isVisible);
      this.logger.debug(isVisible ? '👁️ App became visible' : '🫥 App became hidden');
    });

    // Setup event bus listeners
    this.setupEventBusListeners();
  }

  /**
   * Get application configuration
   */
  public getConfig(): AppConfig {
    return APP_CONFIG;
  }

  /**
   * Subscribe to application events
   */
  public on(event: string, callback: (...args: any[]) => void): void {
    this.eventBus.on(event, callback);
  }

  /**
   * Emit application event
   */
  public emit(event: string, ...args: any[]): void {
    this.eventBus.emit(event, ...args);
  }
}

// Initialize application when module loads
let app: CobraRyuApp;

try {
  app = new CobraRyuApp();
  
  // Make app available globally for debugging
  if (APP_CONFIG.isDevelopment) {
    (window as any).cobraRyuApp = app;
  }
} catch (error) {
  console.error('💥 Failed to initialize Cobra Ryu Application:', error);
}

// Export for potential external use
export default app; 